(()=>{var e={};e.id=158,e.ids=[158],e.modules={1378:(e,r,t)=>{Promise.resolve().then(t.bind(t,86332))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},8963:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=8963,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,r,t)=>{"use strict";e.exports=t(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38520:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>q,serverHooks:()=>k,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>x});var o=t(26142),i=t(94327),n=t(34862),u=t(18815),a=t(76741);let p=async()=>{try{let e=await u.database.pendingLogin.deleteMany({where:{expiresAt:{lt:new Date}}});return e.count>0&&a.R.info("Cleaned up expired login tokens",{count:e.count}),e.count}catch(e){throw a.R.error("Failed to cleanup expired tokens",{error:e}),e}},c=async(e=60)=>{try{let r=new Date(Date.now()-60*e*1e3),t=await u.database.pendingLogin.deleteMany({where:{createdAt:{lt:r}}});return t.count>0&&a.R.info("Cleaned up old login tokens",{count:t.count,maxAgeMinutes:e}),t.count}catch(e){throw a.R.error("Failed to cleanup old tokens",{error:e}),e}};var d=t(74433),l=t(26239);let x=async e=>{try{let r=e.headers.get("authorization"),t=process.env.CRON_SECRET;if(t&&r!==`Bearer ${t}`){let r=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown";return a.R.warn("Unauthorized cron request",{ip:r,userAgent:e.headers.get("user-agent")}),l.NextResponse.json({error:"Unauthorized"},{status:401})}let s=await p(),o=await c(120),i=s+o;return a.R.info("Token cleanup completed",{expiredTokens:s,oldTokens:o,totalCleaned:i}),l.NextResponse.json({success:!0,cleaned:{expired:s,old:o,total:i},timestamp:new Date().toISOString()})}catch(r){let e=(0,d.u)(r);return a.R.error("Token cleanup failed",{error:e}),l.NextResponse.json({error:"Cleanup failed",message:e},{status:500})}},q=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/cron/cleanup-tokens/route",pathname:"/api/cron/cleanup-tokens",filename:"route",bundlePath:"app/api/cron/cleanup-tokens/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\cron\\cleanup-tokens\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:g,serverHooks:k}=q;function w(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:g})}},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67098:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,21034,23))},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74433:(e,r,t)=>{"use strict";t.d(r,{u:()=>i});var s=t(4520),o=t(76741);let i=e=>{let r="An error occurred";r=e instanceof Error||e&&"object"==typeof e&&"message"in e?e.message:String(e);try{(0,s.captureException)(e),o.R.error(`Parsing error: ${r}`)}catch(e){console.error("Error parsing error:",e)}return r}},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76741:(e,r,t)=>{"use strict";t.d(r,{R:()=>s});let s=t(42870).log},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2923,25,5480,3319,864],()=>t(38520));module.exports=s})();