{"name": "@qapt-coder/types", "version": "0.0.0", "type": "module", "main": "./dist/index.cjs", "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "vitest run", "build": "tsup", "npm:publish:test": "tsup --outDir npm/dist && cd npm && npm publish --dry-run", "npm:publish": "tsup --outDir npm/dist && cd npm && npm publish", "clean": "rimraf dist npm/dist .turbo"}, "dependencies": {"zod": "^3.24.2"}, "devDependencies": {"@qapt-coder/config-eslint": "workspace:^", "@qapt-coder/config-typescript": "workspace:^", "@types/node": "^22.15.20", "tsup": "^8.3.5", "vitest": "^3.1.3"}}