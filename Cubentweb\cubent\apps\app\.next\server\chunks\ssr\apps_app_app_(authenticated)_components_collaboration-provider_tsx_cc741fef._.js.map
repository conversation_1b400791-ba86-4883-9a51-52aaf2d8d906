{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/collaboration-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CollaborationProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CollaborationProvider() from the server but CollaborationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/app/app/(authenticated)/components/collaboration-provider.tsx <module evaluation>\",\n    \"CollaborationProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,wBAAwB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,oGACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/collaboration-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CollaborationProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CollaborationProvider() from the server but CollaborationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/app/app/(authenticated)/components/collaboration-provider.tsx\",\n    \"CollaborationProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,wBAAwB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,gFACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}