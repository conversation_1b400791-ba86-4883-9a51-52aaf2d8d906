(()=>{var e={};e.id=4082,e.ids=[4082],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,r,t)=>{"use strict";e.exports=t(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},93234:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>x});var u=t(26142),i=t(94327),o=t(34862),p=t(37838),a=t(18815),c=t(26239),n=t(25);let d=n.z.object({userId:n.z.string()});async function x(e){try{let{userId:r}=await (0,p.j)();if(!r)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),{userId:s}=d.parse(t);if(!await a.database.user.findUnique({where:{id:s,clerkId:r}}))return c.NextResponse.json({error:"User not found"},{status:404});let u=await a.database.user.update({where:{id:s},data:{termsAccepted:!0,termsAcceptedAt:new Date}});return c.NextResponse.json({success:!0,message:"Terms accepted successfully",termsAcceptedAt:u.termsAcceptedAt})}catch(e){return console.error("Terms acceptance error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new u.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/terms/accept/route",pathname:"/api/terms/accept",filename:"route",bundlePath:"app/api/terms/accept/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\terms\\accept\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:q,workUnitAsyncStorage:m,serverHooks:h}=l;function f(){return(0,o.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:m})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>t(93234));module.exports=s})();