{"title": "<PERSON><PERSON><PERSON>", "done": "Tamamlandı", "modes": {"title": "<PERSON><PERSON><PERSON>", "createNewMode": "<PERSON>ni mod oluş<PERSON>", "editModesConfig": "Mod <PERSON>landırmasını düzenle", "editGlobalModes": "Global modları düzenle", "editProjectModes": "<PERSON><PERSON> mod<PERSON> dü<PERSON> (.roomodes)", "createModeHelpText": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>'nun davranışını uyarlayan özel kişiliklerdir. <0>Modları Kullanma Hakkında Bilgi Edinin</0> veya <1>Modları Özelleştirme.</1>", "selectMode": "Modları Ara"}, "apiConfiguration": {"title": "API Yapılandırması", "select": "Bu mod için hangi API yapılandırmasının kullanılacağını seçin"}, "tools": {"title": "Kullanılabilir <PERSON>", "builtInModesText": "Yerleşik modlar için araçlar değiştirilemez", "editTools": "Araçları düzenle", "doneEditing": "Düzenlemeyi bitir", "allowedFiles": "<PERSON>zin verilen <PERSON>ar:", "toolNames": {"read": "Dosyaları Oku", "edit": "Dosyaları Düzenle", "browser": "Tarayıcı Kullan", "command": "Komutları Çalıştır", "mcp": "<PERSON><PERSON>"}, "noTools": "Yok"}, "roleDefinition": {"title": "Rol Tanımı", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "description": "Bu mod için Roo'nun uzmanlığını ve kişiliğini tanımlayın. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, Roo'nun kendini nasıl sunduğunu ve görevlere nasıl yaklaştığını şekillendirir."}, "whenToUse": {"title": "Ne zaman kullanılmalı (isteğe bağlı)", "description": "Bu modun ne zaman kullanılması gerektiğini açıklayın. <PERSON><PERSON>, Orchestrator'ın bir görev için doğru modu seçmesine yardımcı olur.", "resetToDefault": "'Ne zaman kullanılmalı' açıklamasını varsayılana sıfırla"}, "customInstructions": {"title": "Moda özgü özel talimatlar (isteğe bağlı)", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "description": "{{modeName}} modu i<PERSON><PERSON> da<PERSON> yönergeleri e<PERSON>.", "loadFromFile": "{{mode}} moduna özgü özel talimatlar ayrıca çalışma alanınızdaki <span>.roo/rules-{{slug}}/</span> klasöründen yüklenebilir (.roorules-{{slug}} ve .clinerules-{{slug}} kullanımdan kaldırılmıştır ve yakında çalışmayı durduracaklardır)."}, "globalCustomInstructions": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Bu talimatlar tüm modlara uygulanır. Aşağıdaki moda özgü talimatlarla geliştirilebilen temel davranış seti sağlarlar. <0>Daha fazla bilgi edinin</0>", "loadFromFile": "Talimatlar ayrıca çalışma alanınızdaki <span>.roo/rules/</span> klasöründen de yüklenebilir (.roorules ve .clinerules kullanımdan kaldırılmıştır ve yakında çalışmayı durduracaklardır)."}, "systemPrompt": {"preview": "Sistem promptunu <PERSON>", "copy": "Sistem promptunu pan<PERSON> k<PERSON>ala", "title": "Sistem promptu ({{modeName}} modu)"}, "supportPrompts": {"title": "Destek Promptları", "resetPrompt": "{{promptType}} promptunu var<PERSON> sı<PERSON>ı<PERSON>a", "prompt": "Prompt", "enhance": {"apiConfiguration": "API Yapılandırması", "apiConfigDescription": "Promptları geliştirmek için her zaman kullanılacak bir API yapılandırması seçebilir veya şu anda seçili olanı kullanabilirsiniz", "useCurrentConfig": "Şu anda seçili API yapılandırmasını kullan", "testPromptPlaceholder": "Geliştirmeyi test etmek i<PERSON>in bir prompt girin", "previewButton": "Prompt geliştirmesini önizle", "testEnhancement": "G<PERSON>ş<PERSON>rm<PERSON>i test et"}, "types": {"ENHANCE": {"label": "<PERSON><PERSON><PERSON>", "description": "Girdileriniz için özel öneriler veya iyileştirmeler almak için prompt geliştirmeyi kullanın. <PERSON><PERSON>, Roo'nun niyetinizi anlamasını ve mümkün olan en iyi yanıtları sağlamasını garanti eder. Sohbetteki ✨ simgesi aracılığıyla kullanılabilir."}, "EXPLAIN": {"label": "Kodu Açıkla", "description": "<PERSON>d <PERSON>, fonksiyonlar veya tüm dosyalar hakkında ayrıntılı açıklamalar alın. Karmaşık kodu anlamak veya yeni kalıpları öğrenmek için faydalıdır. Ko<PERSON> <PERSON><PERSON> (editördeki ampul simgesi) ve editör bağlam menüsünde (seçili koda sağ tıklayın) kullanılabilir."}, "FIX": {"label": "Sorunları Düzelt", "description": "Hataları, bugları veya kod kalitesi sorunlarını tanımlama ve çözme konusunda yardım alın. Sorunları çözmek için adım adım rehberlik sağlar. <PERSON><PERSON> <PERSON> (editördeki ampul simgesi) ve editör bağlam menüsünde (seçili koda sağ tıklayın) kullanılabilir."}, "IMPROVE": {"label": "<PERSON><PERSON>tir", "description": "İşlevselliği korurken kod optimizasyonu, daha iyi uygulamalar ve mimari iyileştirmeler için öneriler alın. <PERSON><PERSON> <PERSON> (editördeki ampul simgesi) ve editör bağlam menüsünde (seçili koda sağ tıklayın) kullanılabilir."}, "ADD_TO_CONTEXT": {"label": "Bağ<PERSON><PERSON>", "description": "Mevcut görevinize veya konuşmanıza bağlam ekleyin. Ek bilgi veya açıklamalar sağlamak için faydalıdır. <PERSON><PERSON> <PERSON> (editördeki ampul simgesi) ve editör bağlam menüsünde (seçili koda sağ tıklayın) kullanılabilir."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Terminal İçeriğini Bağlama Ekle", "description": "Terminal çıktısını mevcut görevinize veya konuşmanıza ekleyin. Komut çıktılarını veya günlükleri sağlamak için faydalıdır. Terminal bağlam menüsünde (seçili terminal içeriğine sağ tıklayın) kullanılabilir."}, "TERMINAL_FIX": {"label": "Terminal Komutunu Düzelt", "description": "Başarısız olan veya iyileştirme gerektiren terminal komutlarını düzeltme konusunda yardım alın. Terminal bağlam menüsünde (seçili terminal içeriğine sağ tıklayın) kullanılabilir."}, "TERMINAL_EXPLAIN": {"label": "Terminal Komutunu Açıkla", "description": "Terminal komutları ve çıktıları hakkında ayrıntılı açıklamalar alın. Terminal bağlam menüsünde (seçili terminal içeriğine sağ tıklayın) kullanılabilir."}, "NEW_TASK": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> yeni bir görev ba<PERSON><PERSON>. Ko<PERSON>t paletinde kullanılabilir."}}}, "advancedSystemPrompt": {"title": "Gelişmiş: Sistem Promptunu Geçersiz Kıl", "description": "<2>⚠️ Uyarı:</2> Bu gelişmiş özellik güvenlik önlemlerini atlar. <1>KULLANMADAN ÖNCE BUNU OKUYUN!</1>Çalışma alanınızda <span>.roo/system-prompt-{{slug}}</span> adresinde bir dosya oluşturarak varsayılan sistem istemini geçersiz kılın."}, "createModeDialog": {"title": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "name": {"label": "İsim", "placeholder": "<PERSON><PERSON> adını girin"}, "slug": {"label": "Slug", "description": "<PERSON><PERSON>, URL'lerde ve dosya adlarında kullanılır. Küçük harflerle yazılmalı ve yalnızca harfler, sayılar ve kısa çizgiler içermelidir."}, "saveLocation": {"label": "<PERSON><PERSON><PERSON>", "description": "Bu modu nereye ka<PERSON> se<PERSON>. Projeye özgü modlar, global modlara göre önceliklidir.", "global": {"label": "Global", "description": "<PERSON><PERSON>m çalışma alanlarında kullanılabilir"}, "project": {"label": "<PERSON><PERSON><PERSON> (.room<PERSON>)", "description": "Yalnızca bu çalışma alanında kullanılabilir, globale göre önceliklidir"}}, "roleDefinition": {"label": "Rol Tanımı", "description": "Bu mod iç<PERSON>'nun uzmanlığını ve kişiliğini tanımlayın."}, "whenToUse": {"label": "Ne zaman kullanılmalı (isteğe bağlı)", "description": "Bu modun ne zaman en etkili olduğu ve hangi tür görevlerde üstün olduğu hakkında net bir açıklama sağlayın."}, "tools": {"label": "Kullanılabilir <PERSON>", "description": "Bu modun hangi araçları kullanabileceğini seçin."}, "customInstructions": {"label": "<PERSON><PERSON> (isteğe bağlı)", "description": "Bu mod için ö<PERSON> davranış yönergeleri ekleyin."}, "buttons": {"cancel": "İptal", "create": "<PERSON><PERSON>"}, "deleteMode": "Modu sil"}, "allFiles": "<PERSON><PERSON><PERSON>"}