(()=>{var t={};t.id=7162,t.ids=[7162],t.modules={1359:(t,e,i)=>{"use strict";i.d(e,{N:()=>n});var r=i(12901),s=i(37838);async function n(){i(1447);let{userId:t}=await (0,s.j)();return t?(await (0,r.$)()).users.getUser(t):null}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},11997:t=>{"use strict";t.exports=require("punycode")},12901:(t,e,i)=>{"use strict";i.d(e,{$:()=>h});var r=i(8741),s=i(23056),n=i(76315),o=i(97495);let a=new(i(16698)).AsyncLocalStorage;var l=i(60606);let h=async()=>{var t,e;let i;try{let t=await (0,s.TG)(),e=(0,o._b)(t,r.AA.Headers.ClerkRequestData);i=(0,l.Kk)(e)}catch(t){if(t&&(0,s.Sz)(t))throw t}let h=null!=(e=null==(t=a.getStore())?void 0:t.get("requestData"))?e:i;return(null==h?void 0:h.secretKey)||(null==h?void 0:h.publishableKey)?(0,n.n)(h):(0,n.n)({})}},16698:t=>{"use strict";t.exports=require("node:async_hooks")},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26142:(t,e,i)=>{"use strict";t.exports=i(44870)},26790:(t,e,i)=>{"use strict";i.d(e,{z:()=>R});var r,s,n,o,a,l,h,d,u,c,p,f,m,y,_,w,g,v,E,S=i(45940);i(92867);var b=i(37081);i(27322),i(6264);var O=i(49530),T=i(57136),k=i(94051),A=class{constructor(){(0,k.VK)(this,n),(0,k.VK)(this,r,"clerk_telemetry_throttler"),(0,k.VK)(this,s,864e5)}isEventThrottled(t){if(!(0,k.S7)(this,n,l))return!1;let e=Date.now(),i=(0,k.jq)(this,n,o).call(this,t),h=(0,k.S7)(this,n,a)?.[i];if(!h){let t={...(0,k.S7)(this,n,a),[i]:e};localStorage.setItem((0,k.S7)(this,r),JSON.stringify(t))}if(h&&e-h>(0,k.S7)(this,s)){let t=(0,k.S7)(this,n,a);delete t[i],localStorage.setItem((0,k.S7)(this,r),JSON.stringify(t))}return!!h}};r=new WeakMap,s=new WeakMap,n=new WeakSet,o=function(t){let{sk:e,pk:i,payload:r,...s}=t,n={...r,...s};return JSON.stringify(Object.keys({...r,...s}).sort().map(t=>n[t]))},a=function(){let t=localStorage.getItem((0,k.S7)(this,r));return t?JSON.parse(t):{}},l=function(){if("undefined"==typeof window)return!1;let t=window.localStorage;if(!t)return!1;try{let e="test";return t.setItem(e,e),t.removeItem(e),!0}catch(e){return e instanceof DOMException&&("QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)&&t.length>0&&t.removeItem((0,k.S7)(this,r)),!1}};var I={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},C=class{constructor(t){(0,k.VK)(this,f),(0,k.VK)(this,h),(0,k.VK)(this,d),(0,k.VK)(this,u,{}),(0,k.VK)(this,c,[]),(0,k.VK)(this,p),(0,k.OV)(this,h,{maxBufferSize:t.maxBufferSize??I.maxBufferSize,samplingRate:t.samplingRate??I.samplingRate,disabled:t.disabled??!1,debug:t.debug??!1,endpoint:I.endpoint}),t.clerkVersion||"undefined"!=typeof window?(0,k.S7)(this,u).clerkVersion=t.clerkVersion??"":(0,k.S7)(this,u).clerkVersion="",(0,k.S7)(this,u).sdk=t.sdk,(0,k.S7)(this,u).sdkVersion=t.sdkVersion,(0,k.S7)(this,u).publishableKey=t.publishableKey??"";let e=(0,T.q5)(t.publishableKey);e&&((0,k.S7)(this,u).instanceType=e.instanceType),t.secretKey&&((0,k.S7)(this,u).secretKey=t.secretKey.substring(0,16)),(0,k.OV)(this,d,new A)}get isEnabled(){return!("development"!==(0,k.S7)(this,u).instanceType||(0,k.S7)(this,h).disabled||"undefined"!=typeof process&&(0,O.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,k.S7)(this,h).debug||"undefined"!=typeof process&&(0,O.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(t){let e=(0,k.jq)(this,f,E).call(this,t.event,t.payload);(0,k.jq)(this,f,g).call(this,e.event,e),(0,k.jq)(this,f,m).call(this,e,t.eventSamplingRate)&&((0,k.S7)(this,c).push(e),(0,k.jq)(this,f,_).call(this))}};h=new WeakMap,d=new WeakMap,u=new WeakMap,c=new WeakMap,p=new WeakMap,f=new WeakSet,m=function(t,e){return this.isEnabled&&!this.isDebug&&(0,k.jq)(this,f,y).call(this,t,e)},y=function(t,e){let i=Math.random();return!!(i<=(0,k.S7)(this,h).samplingRate&&(void 0===e||i<=e))&&!(0,k.S7)(this,d).isEventThrottled(t)},_=function(){if("undefined"==typeof window)return void(0,k.jq)(this,f,w).call(this);if((0,k.S7)(this,c).length>=(0,k.S7)(this,h).maxBufferSize){(0,k.S7)(this,p)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,k.S7)(this,p)),(0,k.jq)(this,f,w).call(this);return}(0,k.S7)(this,p)||("requestIdleCallback"in window?(0,k.OV)(this,p,requestIdleCallback(()=>{(0,k.jq)(this,f,w).call(this)})):(0,k.OV)(this,p,setTimeout(()=>{(0,k.jq)(this,f,w).call(this)},0)))},w=function(){fetch(new URL("/v1/event",(0,k.S7)(this,h).endpoint),{method:"POST",body:JSON.stringify({events:(0,k.S7)(this,c)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,k.OV)(this,c,[])}).catch(()=>void 0)},g=function(t,e){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",t),console.log(e),console.groupEnd()):console.log("[clerk/telemetry]",t,e))},v=function(){let t={name:(0,k.S7)(this,u).sdk,version:(0,k.S7)(this,u).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(t={...t,...window.Clerk.constructor.sdkMetadata}),t},E=function(t,e){let i=(0,k.jq)(this,f,v).call(this);return{event:t,cv:(0,k.S7)(this,u).clerkVersion??"",it:(0,k.S7)(this,u).instanceType??"",sdk:i.name,sdkv:i.version,...(0,k.S7)(this,u).publishableKey?{pk:(0,k.S7)(this,u).publishableKey}:{},...(0,k.S7)(this,u).secretKey?{sk:(0,k.S7)(this,u).secretKey}:{},payload:e}};function R(t){let e={...t},i=(0,S.y3)(e),r=(0,S.Bs)({options:e,apiClient:i}),s=new C({...t.telemetry,publishableKey:e.publishableKey,secretKey:e.secretKey,samplingRate:.1,...e.sdkMetadata?{sdk:e.sdkMetadata.name,sdkVersion:e.sdkMetadata.version}:{}});return{...i,...r,telemetry:s}}(0,b.C)(S.nr)},27910:t=>{"use strict";t.exports=require("stream")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55591:t=>{"use strict";t.exports=require("https")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:t=>{"use strict";t.exports=require("node:fs")},73545:(t,e,i)=>{"use strict";i.r(e),i.d(e,{patchFetch:()=>eJ,routeModule:()=>eH,serverHooks:()=>eF,workAsyncStorage:()=>eV,workUnitAsyncStorage:()=>eW});var r={};i.r(r),i.d(r,{POST:()=>eB});var s=i(26142),n=i(94327),o=i(34862),a=i(1359),l=i(37838),h=Object.defineProperty,d="@liveblocks/core",u="2.24.2",c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{};function p(t){console.error(t)}function f(t,e,i){let r=Symbol.for(t),s=i?`${e||"dev"} (${i})`:e||"dev";c[r]?c[r]===s||p(`Multiple copies of Liveblocks are being loaded in your project. This will cause issues! See https://liveblocks.io/docs/errors/dupes 

Conflicts:
- ${t} ${c[r]} (already loaded)
- ${t} ${s} (trying to load this now)`):c[r]=s,e&&u&&e!==u&&p(`Cross-linked versions of Liveblocks found, which will cause issues! See https://liveblocks.io/docs/errors/cross-linked 

Conflicts:
- ${d} is at ${u}
- ${t} is at ${e}

Always upgrade all Liveblocks packages to the same version number.`)}function m(t){let e=t.editedAt?new Date(t.editedAt):void 0,i=new Date(t.createdAt),r=t.reactions.map(t=>({...t,createdAt:new Date(t.createdAt)}));if(t.body)return{...t,reactions:r,createdAt:i,editedAt:e};{let s=new Date(t.deletedAt);return{...t,reactions:r,createdAt:i,editedAt:e,deletedAt:s}}}function y(t){let e=new Date(t.createdAt),i=new Date(t.updatedAt),r=t.comments.map(t=>m(t));return{...t,createdAt:e,updatedAt:i,comments:r}}function _(t){let e=new Date(t.notifiedAt),i=t.readAt?new Date(t.readAt):null;if("activities"in t){let r=t.activities.map(t=>({...t,createdAt:new Date(t.createdAt)}));return{...t,notifiedAt:e,readAt:i,activities:r}}return{...t,notifiedAt:e,readAt:i}}function w(t){let e=new Date(t.createdAt);return{...t,createdAt:e}}((t,e)=>{for(var i in e)h(t,i,{get:e[i],enumerable:!0})})({},{error:()=>S,errorWithTitle:()=>T,warn:()=>E,warnWithTitle:()=>O});var g="background:#0e0d12;border-radius:9999px;color:#fff;padding:3px 7px;font-family:sans-serif;font-weight:600;";function v(t){return"undefined"==typeof window?console[t]:(e,...i)=>console[t]("%cLiveblocks",g,e,...i)}var E=v("warn"),S=v("error");function b(t){return"undefined"==typeof window?console[t]:(e,i,...r)=>console[t](`%cLiveblocks%c ${e}`,g,"font-weight:600",i,...r)}var O=b("warn"),T=b("error");function k(t){return null!==t&&"object"==typeof t&&"[object Object]"===Object.prototype.toString.call(t)}function A(t){return k(t)&&"string"==typeof t.startsWith}function I(t){throw Error(t)}function C(t){return Object.entries(t)}function R(t){try{return JSON.parse(t)}catch(t){return}}function P(t){return JSON.parse(JSON.stringify(t))}function D(t){let e={...t};return Object.keys(t).forEach(t=>{void 0===e[t]&&delete e[t]}),e}async function x(t,e,i){let r;return Promise.race([t,new Promise((t,s)=>{r=setTimeout(()=>{s(Error(i))},e)})]).finally(()=>clearTimeout(r))}var N=class t extends Error{response;details;constructor(t,e,i){super(t),this.name="HttpError",this.response=e,this.details=i}static async fromResponse(e){let i,r,s;try{i=await e.text()}catch{}let n=i?R(i):void 0;k(n)&&(r=n);let o="";o||="string"==typeof r?.message?r.message:"",o||="string"==typeof r?.error?r.error:"",void 0===n&&(o||=i||""),o||=e.statusText;try{s=new URL(e.url).pathname}catch{}return new t(o+=void 0!==s?` (got status ${e.status} from ${s})`:` (got status ${e.status})`,e,r)}get status(){return this.response.status}},$=t=>t instanceof N&&t.status>=400&&t.status<500;function L(){let t,e;return[new Promise((i,r)=>{t=i,e=r}),t,e]}function j(){let t=new Set;function e(e){return t.add(e),()=>t.delete(e)}function i(t){let i=e(e=>(i(),t(e)));return i}async function r(t){let i;return new Promise(r=>{i=e(e=>{(void 0===t||t(e))&&r(e)})}).finally(()=>i?.())}return{notify:function(e){let i=!1;for(let r of t)r(e),i=!0;return i},subscribe:e,subscribeOnce:i,count:function(){return t.size},waitUntil:r,dispose(){t.clear()},observable:{subscribe:e,subscribeOnce:i,waitUntil:r}}}var M=t=>t,U=Symbol("kSinks"),K=Symbol("kTrigger"),z=null,q=null;function B(t){if(null!==z)return void t();z=new Set;try{t()}finally{for(let t of z)t[K]();z=null}}function H(t){z||I("Expected to be in an active batch"),z.add(t)}function V(t,e){let i=!1,r={...t};return Object.keys(e).forEach(t=>{let s=e[t];r[t]!==s&&(void 0===s?delete r[t]:r[t]=s,i=!0)}),i?r:t}var W=class{equals;#t;[U];constructor(t){this.equals=t??Object.is,this.#t=j(),this[U]=new Set,this.get=this.get.bind(this),this.subscribe=this.subscribe.bind(this),this.subscribeOnce=this.subscribeOnce.bind(this)}dispose(){this.#t.dispose(),this.#t="(disposed)",this.equals="(disposed)"}get hasWatchers(){if(this.#t.count()>0)return!0;for(let t of this[U])if(t.hasWatchers)return!0;return!1}[K](){for(let t of(this.#t.notify(),this[U]))H(t)}subscribe(t){return 0===this.#t.count()&&this.get(),this.#t.subscribe(t)}subscribeOnce(t){let e=this.subscribe(()=>(e(),t()));return e}waitUntil(){throw Error("waitUntil not supported on Signals")}markSinksDirty(){for(let t of this[U])t.markDirty()}addSink(t){this[U].add(t)}removeSink(t){this[U].delete(t)}asReadonly(){return this}},F=class extends W{#e;constructor(t,e){super(e),this.#e=M(t)}dispose(){super.dispose(),this.#e="(disposed)"}get(){return q?.add(this),this.#e}set(t){B(()=>{"function"==typeof t&&(t=t(this.#e)),this.equals(this.#e,t)||(this.#e=M(t),this.markSinksDirty(),H(this))})}},J=Symbol(),G=class t extends W{#i;#r;#s;#n;#o;static from(...e){let i=e.pop();if("function"!=typeof i&&I("Invalid .from() call, last argument expected to be a function"),"function"!=typeof e[e.length-1])return new t(e,i);{let r=e.pop();return new t(e,r,i)}}constructor(t,e,i){super(i),this.#r=!0,this.#i=J,this.#n=t,this.#s=new Set,this.#o=e}dispose(){for(let t of this.#s)t.removeSink(this);this.#i="(disposed)",this.#s="(disposed)",this.#n="(disposed)",this.#o="(disposed)"}get isDirty(){return this.#r}#a(){let t,e=q;q=new Set;try{t=this.#o(...this.#n.map(t=>t.get()))}finally{let t=this.#s;for(let e of(this.#s=new Set,q))this.#s.add(e),t.delete(e);for(let e of t)e.removeSink(this);for(let t of this.#s)t.addSink(this);q=e}return this.#r=!1,!this.equals(this.#i,t)&&(this.#i=t,!0)}markDirty(){this.#r||(this.#r=!0,this.markSinksDirty())}get(){return this.#r&&this.#a(),q?.add(this),this.#i}[K](){this.hasWatchers&&this.#a()&&super[K]()}},Y=class extends W{#l;constructor(t){super(),this.#l=t}dispose(){super.dispose(),this.#l="(disposed)"}get(){return q?.add(this),this.#l}mutate(t){B(()=>{let e=!t||t(this.#l);null!==e&&"object"==typeof e&&"then"in e&&I("MutableSignal.mutate() does not support async callbacks"),!1!==e&&(this.markSinksDirty(),H(this))})}};function X(t,e){return null===e||"object"!=typeof e||Array.isArray(e)?e:Object.keys(e).sort().reduce((t,i)=>(t[i]=e[i],t),{})}function Q(t){return JSON.stringify(t,X)}function Z(t){try{return JSON.stringify(t)}catch(e){throw console.error(`Could not stringify: ${e.message}`),console.error(t),e}}var tt=class{input;resolve;reject;promise;constructor(t){this.input=t;let{promise:e,resolve:i,reject:r}=function(){let[t,e,i]=L();return{promise:t,resolve:e,reject:i}}();this.promise=e,this.resolve=i,this.reject=r}},te=(t=21)=>crypto.getRandomValues(new Uint8Array(t)).reduce((t,e)=>t+=(e&=63)<36?e.toString(36):e<62?(e-26).toString(36).toUpperCase():e<63?"_":"-","");var ti=/^[a-zA-Z_][a-zA-Z0-9_]*$/;function tr(t){let e=[],i=Object.entries(t),r=[],s=[],n=[];return i.forEach(([t,e])=>{if(!ti.test(t))throw Error("Key must only contain letters, numbers, _");to(e)?r.push([t,e]):k(e)&&(A(e)?s.push([t,e]):n.push([t,e]))}),e=[...ts(r),...tn(s)],n.forEach(([t,i])=>{let r=Object.entries(i),s=[],n=[];r.forEach(([e,i])=>{if(tl(e))throw Error("Key cannot be empty");to(i)?s.push([ta(t,e),i]):A(i)&&n.push([ta(t,e),i])}),e=[...e,...ts(s),...tn(n)]}),e.map(({key:t,operator:e,value:i})=>`${t}${e}${th(i)}`).join(" ")}var ts=t=>{let e=[];return t.forEach(([t,i])=>{e.push({key:t,operator:":",value:i})}),e},tn=t=>{let e=[];return t.forEach(([t,i])=>{"startsWith"in i&&"string"==typeof i.startsWith&&e.push({key:t,operator:"^",value:i.startsWith})}),e},to=t=>"string"==typeof t||"number"==typeof t||"boolean"==typeof t||null===t,ta=(t,e)=>e?`${t}[${th(e)}]`:t,tl=t=>!t||""===t.toString().trim();function th(t){let e=JSON.stringify(t);return"string"!=typeof t||e.includes("'")?e:`'${e.slice(1,-1).replace(/\\"/g,'"')}'`}function td(t,e,i){let r=new URL(e,t);return void 0!==i&&(r.search=(i instanceof URLSearchParams?i:function(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))null!=r&&e.set(i,r.toString());return e}(i)).toString()),r.toString()}function tu(t,...e){return t.reduce((t,i,r)=>t+encodeURIComponent(e[r-1]??"")+i)}function tc(t,e){throw Error(e)}function tp(t,e="Expected value to be non-nullable"){return t}var tf=class{#h;constructor(t){this.#h=t}get current(){return this.#h}allowPatching(t){let e=this,i=!0;t({...this.#h,patch(t){if(i)for(let i of(e.#h=Object.assign({},e.#h,t),Object.entries(t))){let[t,e]=i;"patch"!==t&&(this[t]=e)}else throw Error("Can no longer patch stale context")}}),i=!1}},tm=1,ty=class{id;#d;#u;#c;#p;#f;#m;events;#y;#_;#w;get #g(){let t=this.#c.values()[Symbol.iterator]().next();if(!t.done)return t.value;throw Error("No states defined yet")}get currentState(){if(null===this.#p)if(0===this.#d)throw Error("Not started yet");else throw Error("Already stopped");return this.#p}start(){if(0!==this.#d)throw Error("State machine has already started");return this.#d=1,this.#p=this.#g,this.#v(null),this}stop(){if(1!==this.#d)throw Error("Cannot stop a state machine that hasn't started yet");this.#E(null),this.#d=2,this.#p=null}constructor(t){this.id=tm++,this.#d=0,this.#p=null,this.#c=new Set,this.#_=new Map,this.#y=[],this.#w=new Set,this.#f=new Map,this.#u=new tf(t),this.#m={didReceiveEvent:j(),willTransition:j(),didIgnoreEvent:j(),willExitState:j(),didEnterState:j()},this.events={didReceiveEvent:this.#m.didReceiveEvent.observable,willTransition:this.#m.willTransition.observable,didIgnoreEvent:this.#m.didIgnoreEvent.observable,willExitState:this.#m.willExitState.observable,didEnterState:this.#m.didEnterState.observable}}get context(){return this.#u.current}addState(t){if(0!==this.#d)throw Error("Already started");return this.#c.add(t),this}onEnter(t,e){if(0!==this.#d)throw Error("Already started");if(this.#_.has(t))throw Error(`enter/exit function for ${t} already exists`);return this.#_.set(t,e),this}onEnterAsync(t,e,i,r,s){return this.onEnter(t,()=>{let t=new AbortController,n=t.signal,o=s?setTimeout(()=>{let t=Error("Timed out");this.#S({type:"ASYNC_ERROR",reason:t},r)},s):void 0,a=!1;return e(this.#u.current,n).then(t=>{n.aborted||(a=!0,this.#S({type:"ASYNC_OK",data:t},i))},t=>{n.aborted||(a=!0,this.#S({type:"ASYNC_ERROR",reason:t},r))}),()=>{clearTimeout(o),a||t.abort()}})}#b(t){let e=[];if("*"===t)for(let t of this.#c)e.push(t);else if(t.endsWith(".*")){let i=t.slice(0,-1);for(let t of this.#c)t.startsWith(i)&&e.push(t)}else this.#c.has(t)&&e.push(t);if(0===e.length)throw Error(`No states match ${JSON.stringify(t)}`);return e}addTransitions(t,e){if(0!==this.#d)throw Error("Already started");for(let i of this.#b(t)){let r=this.#f.get(i);for(let[s,n]of(void 0===r&&(r=new Map,this.#f.set(i,r)),Object.entries(e))){if(r.has(s))throw Error(`Trying to set transition "${s}" on "${i}" (via "${t}"), but a transition already exists there.`);let e=n;if(this.#w.add(s),void 0!==e){let t="function"==typeof e?e:()=>e;r.set(s,t)}}}return this}addTimedTransition(t,e,i){return this.onEnter(t,()=>{let t=setTimeout(()=>{this.#S({type:"TIMER"},i)},"function"==typeof e?e(this.#u.current):e);return()=>{clearTimeout(t)}})}#O(t){return this.#f.get(this.currentState)?.get(t)}#E(t){this.#m.willExitState.notify(this.currentState),this.#u.allowPatching(e=>{t=t??this.#y.length;for(let i=0;i<t;i++)this.#y.pop()?.(e)})}#v(t){let e=function(t,e){let i=t.split(".");if(e<1||e>i.length+1)throw Error("Invalid number of levels");let r=[];e>i.length&&r.push("*");for(let t=i.length-e+1;t<i.length;t++){let e=i.slice(0,t);e.length>0&&r.push(e.join(".")+".*")}return r.push(t),r}(this.currentState,t??this.currentState.split(".").length+1);this.#u.allowPatching(t=>{for(let i of e){let e=this.#_.get(i),r=e?.(t);"function"==typeof r?this.#y.push(r):this.#y.push(null)}}),this.#m.didEnterState.notify(this.currentState)}send(t){if(!this.#w.has(t.type))throw Error(`Invalid event ${JSON.stringify(t.type)}`);if(2===this.#d)return;let e=this.#O(t.type);if(void 0!==e)return this.#S(t,e);this.#m.didIgnoreEvent.notify(t)}#S(t,e){let i,r;this.#m.didReceiveEvent.notify(t);let s=this.currentState,n=("function"==typeof e?e:()=>e)(t,this.#u.current);if(null===n)return void this.#m.didIgnoreEvent.notify(t);if("string"==typeof n?i=n:(i=n.target,r=Array.isArray(n.effect)?n.effect:[n.effect]),!this.#c.has(i))throw Error(`Invalid next state name: ${JSON.stringify(i)}`);this.#m.willTransition.notify({from:s,to:i});let[o,a]=function(t,e){if(t===e)return[0,0];let i=t.split("."),r=e.split("."),s=Math.min(i.length,r.length),n=0;for(;n<s&&i[n]===r[n];n++);return[i.length-n,r.length-n]}(this.currentState,i);if(o>0&&this.#E(o),this.#p=i,void 0!==r){let e=r;this.#u.allowPatching(i=>{for(let r of e)"function"==typeof r?r(i,t):i.patch(r)})}a>0&&this.#v(a)}},t_=(t=>(t[t.UPDATE_PRESENCE=100]="UPDATE_PRESENCE",t[t.USER_JOINED=101]="USER_JOINED",t[t.USER_LEFT=102]="USER_LEFT",t[t.BROADCASTED_EVENT=103]="BROADCASTED_EVENT",t[t.ROOM_STATE=104]="ROOM_STATE",t[t.INITIAL_STORAGE_STATE=200]="INITIAL_STORAGE_STATE",t[t.UPDATE_STORAGE=201]="UPDATE_STORAGE",t[t.REJECT_STORAGE_OP=299]="REJECT_STORAGE_OP",t[t.UPDATE_YDOC=300]="UPDATE_YDOC",t[t.THREAD_CREATED=400]="THREAD_CREATED",t[t.THREAD_DELETED=407]="THREAD_DELETED",t[t.THREAD_METADATA_UPDATED=401]="THREAD_METADATA_UPDATED",t[t.THREAD_UPDATED=408]="THREAD_UPDATED",t[t.COMMENT_CREATED=402]="COMMENT_CREATED",t[t.COMMENT_EDITED=403]="COMMENT_EDITED",t[t.COMMENT_DELETED=404]="COMMENT_DELETED",t[t.COMMENT_REACTION_ADDED=405]="COMMENT_REACTION_ADDED",t[t.COMMENT_REACTION_REMOVED=406]="COMMENT_REACTION_REMOVED",t))(t_||{}),tw=(t=>(t[t.CLOSE_NORMAL=1e3]="CLOSE_NORMAL",t[t.CLOSE_ABNORMAL=1006]="CLOSE_ABNORMAL",t[t.UNEXPECTED_CONDITION=1011]="UNEXPECTED_CONDITION",t[t.TRY_AGAIN_LATER=1013]="TRY_AGAIN_LATER",t[t.INVALID_MESSAGE_FORMAT=4e3]="INVALID_MESSAGE_FORMAT",t[t.NOT_ALLOWED=4001]="NOT_ALLOWED",t[t.MAX_NUMBER_OF_MESSAGES_PER_SECONDS=4002]="MAX_NUMBER_OF_MESSAGES_PER_SECONDS",t[t.MAX_NUMBER_OF_CONCURRENT_CONNECTIONS=4003]="MAX_NUMBER_OF_CONCURRENT_CONNECTIONS",t[t.MAX_NUMBER_OF_MESSAGES_PER_DAY_PER_APP=4004]="MAX_NUMBER_OF_MESSAGES_PER_DAY_PER_APP",t[t.MAX_NUMBER_OF_CONCURRENT_CONNECTIONS_PER_ROOM=4005]="MAX_NUMBER_OF_CONCURRENT_CONNECTIONS_PER_ROOM",t[t.ROOM_ID_UPDATED=4006]="ROOM_ID_UPDATED",t[t.KICKED=4100]="KICKED",t[t.TOKEN_EXPIRED=4109]="TOKEN_EXPIRED",t[t.CLOSE_WITHOUT_RETRY=4999]="CLOSE_WITHOUT_RETRY",t))(tw||{});function tg(t){return 4999===t||t>=4e3&&t<4100}function tv(t){return 1013===t||t>=4200&&t<4300}function tE(t){let e=t.currentState;switch(e){case"@ok.connected":case"@ok.awaiting-pong":return"connected";case"@idle.initial":return"initial";case"@auth.busy":case"@auth.backoff":case"@connecting.busy":case"@connecting.backoff":case"@idle.zombie":return t.context.successCount>0?"reconnecting":"connecting";case"@idle.failed":return"disconnected";default:return tc(e,"Unknown state")}}var tS=[250,500,1e3,2e3,4e3,8e3,1e4],tb=tS[0]-1,tO=class extends Error{constructor(t){super(t)}};function tT(t,e){return e.find(e=>e>t)??e[e.length-1]}function tk(t){t.patch({backoffDelay:tT(t.backoffDelay,tS)})}function tA(t){t.patch({backoffDelay:tT(t.backoffDelay,null)})}function tI(t){t.patch({successCount:0})}function tC(t,e){let i=2===t?S:1===t?E:()=>{};return()=>{i(e)}}function tR(t){let e="Connection to Liveblocks websocket server";return i=>{t instanceof Error?E(`${e} could not be established. ${String(t)}`):E(tx(t)?`${e} closed prematurely (code: ${t.code}). Retrying in ${i.backoffDelay}ms.`:`${e} could not be established.`)}}function tP(t){let e=[`code: ${t.code}`];return t.reason&&e.push(`reason: ${t.reason}`),t=>{E(`Connection to Liveblocks websocket server closed (${e.join(", ")}). Retrying in ${t.backoffDelay}ms.`)}}var tD=tC(1,"Connection to WebSocket closed permanently. Won't retry.");function tx(t){return!(t instanceof Error)&&"close"===t.type}var tN=t=>e=>e.patch(t),t$=(t=>(t.Read="room:read",t.Write="room:write",t.PresenceWrite="room:presence:write",t.CommentsWrite="comments:write",t.CommentsRead="comments:read",t))(t$||{});Symbol();j().observable,Date.now();var tL=Symbol("notification-settings-plain");function tj(t){let e={[tL]:{value:t,enumerable:!1}};for(let t of["email","slack","teams","webPush"])e[t]={enumerable:!0,get(){let e=this[tL][t];return void 0===e?(S(`In order to use the '${t}' channel, please set up your project first. For more information: https://liveblocks.io/docs/errors/enable-a-notification-channel`),null):e}};return void 0!==e?Object.create(null,e):Object.create(null)}var tM=tz(0),tU=tz(1),tK=tM+tz(-1);function tz(t){let e=32+(t<0?95+t:t);if(e<32||e>126)throw Error(`Invalid n value: ${t}`);return String.fromCharCode(e)}function tq(t,e){if(void 0!==t&&void 0!==e){var i=t,r=e;if(i<r)return tB(i,r);if(i>r)return tB(r,i);throw Error("Cannot compute value between two equal positions")}if(void 0!==t){var s=t;for(let t=0;t<=s.length-1;t++){let e=s.charCodeAt(t);if(!(e>=126))return s.substring(0,t)+String.fromCharCode(e+1)}return s+tU}if(void 0===e)return tU;{var n=e;let t=n.length-1;for(let e=0;e<=t;e++){let i=n.charCodeAt(e);if(!(i<=32))if(e!==t)return n.substring(0,e+1);else if(33===i)return n.substring(0,e)+tK;else return n.substring(0,e)+String.fromCharCode(i-1)}return tU}}function tB(t,e){let i=0,r=t.length,s=e.length;for(;;){let a=i<r?t.charCodeAt(i):32,l=i<s?e.charCodeAt(i):126;if(a===l){i++;continue}if(l-a!=1){var n,o;return n=t,((o=i)<n.length?n.substring(0,o):n+tM.repeat(o-n.length))+String.fromCharCode(l+a>>1)}{let e=i+1,r=t.substring(0,e);return r.length<e&&(r+=tM.repeat(e-r.length)),r+tB(t.substring(e),"")}}}function tH(t){return!function(t){if(""===t)return!1;let e=t.length-1,i=t.charCodeAt(e);if(i<33||i>126)return!1;for(let i=0;i<e;i++){let e=t.charCodeAt(i);if(e<32||e>126)return!1}return!0}(t)?function(t){let e=[];for(let i=0;i<t.length;i++){let r=t.charCodeAt(i);e.push(r<32?32:r>126?126:r)}for(;e.length>0&&32===e[e.length-1];)e.length--;return e.length>0?String.fromCharCode(...e):tU}(t):t}var tV=(t=>(t[t.INIT=0]="INIT",t[t.SET_PARENT_KEY=1]="SET_PARENT_KEY",t[t.CREATE_LIST=2]="CREATE_LIST",t[t.UPDATE_OBJECT=3]="UPDATE_OBJECT",t[t.CREATE_OBJECT=4]="CREATE_OBJECT",t[t.DELETE_CRDT=5]="DELETE_CRDT",t[t.DELETE_OBJECT_KEY=6]="DELETE_OBJECT_KEY",t[t.CREATE_MAP=7]="CREATE_MAP",t[t.CREATE_REGISTER=8]="CREATE_REGISTER",t))(tV||{});function tW(t,e,i=tH(e)){return Object.freeze({type:"HasParent",node:t,key:e,pos:i})}var tF=Object.freeze({type:"NoParent"}),tJ=class{#T;#k;#A=tF;_getParentKeyOrThrow(){switch(this.parent.type){case"HasParent":return this.parent.key;case"NoParent":throw Error("Parent key is missing");case"Orphaned":return this.parent.oldKey;default:return tc(this.parent,"Unknown state")}}get _parentPos(){switch(this.parent.type){case"HasParent":return this.parent.pos;case"NoParent":throw Error("Parent key is missing");case"Orphaned":return this.parent.oldPos;default:return tc(this.parent,"Unknown state")}}get _pool(){return this.#T}get roomId(){return this.#T?this.#T.roomId:null}get _id(){return this.#k}get parent(){return this.#A}get _parentKey(){switch(this.parent.type){case"HasParent":return this.parent.key;case"NoParent":return null;case"Orphaned":return this.parent.oldKey;default:return tc(this.parent,"Unknown state")}}_apply(t,e){if(5===t.type&&"HasParent"===this.parent.type)return this.parent.node._detachChild(this);return{modified:!1}}_setParentLink(t,e){switch(this.parent.type){case"HasParent":if(this.parent.node!==t)throw Error("Cannot set parent: node already has a parent");this.#A=tW(t,e);return;case"Orphaned":case"NoParent":this.#A=tW(t,e);return;default:return tc(this.parent,"Unknown state")}}_attach(t,e){if(this.#k||this.#T)throw Error("Cannot attach node: already attached");e.addNode(t,this),this.#k=t,this.#T=e}_detach(){switch(this.#T&&this.#k&&this.#T.deleteNode(this.#k),this.parent.type){case"HasParent":this.#A=function(t,e=tH(t)){return Object.freeze({type:"Orphaned",oldKey:t,oldPos:e})}(this.parent.key,this.parent.pos);break;case"NoParent":this.#A=tF;break;case"Orphaned":break;default:tc(this.parent,"Unknown state")}this.#T=void 0}#I;#C;#R;invalidate(){(void 0!==this.#I||void 0!==this.#R)&&(this.#I=void 0,this.#R=void 0,"HasParent"===this.parent.type&&this.parent.node.invalidate())}toTreeNode(t){return(void 0===this.#R||this.#C!==t)&&(this.#C=t,this.#R=this._toTreeNode(t)),this.#R}toImmutable(){return void 0===this.#I&&(this.#I=this._toImmutable()),this.#I}},tG=(t=>(t[t.OBJECT=0]="OBJECT",t[t.LIST=1]="LIST",t[t.MAP=2]="MAP",t[t.REGISTER=3]="REGISTER",t))(tG||{}),tY=class t extends tJ{#P;constructor(t){super(),this.#P=t}get data(){return this.#P}static _deserialize([e,i],r,s){let n=new t(i.data);return n._attach(e,s),n}_toOps(t,e,i){if(void 0===this._id)throw Error("Cannot serialize register if parentId or parentKey is undefined");return[{type:8,opId:i?.generateOpId(),id:this._id,parentId:t,parentKey:e,data:this.data}]}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveRegister if parent is missing");return{type:3,parentId:tp(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key,data:this.data}}_attachChild(t){throw Error("Method not implemented.")}_detachChild(t){throw Error("Method not implemented.")}_apply(t,e){return super._apply(t,e)}_toTreeNode(t){return{type:"Json",id:this._id??te(),key:t,payload:this.#P}}_toImmutable(){return this.#P}clone(){return P(this.data)}};function tX(t,e){let i=t._parentPos,r=e._parentPos;return i===r?0:i<r?-1:1}var tQ=class t extends tJ{#D;#x;#N;constructor(t){let e;for(let i of(super(),this.#D=[],this.#x=new WeakSet,this.#N=new Map,t)){let t=tq(e),r=eo(i);r._setParentLink(this,t),this.#D.push(r),e=t}}static _deserialize([e],i,r){let s=new t([]);s._attach(e,r);let n=i.get(e);if(void 0===n)return s;for(let[t,e]of n){let n=et([t,e],i,r);n._setParentLink(s,e.parentKey),s._insertAndSort(n)}return s}_toOps(t,e,i){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let r=[],s={id:this._id,opId:i?.generateOpId(),type:2,parentId:t,parentKey:e};for(let t of(r.push(s),this.#D)){let e=t._getParentKeyOrThrow(),s=t5(t._toOps(this._id,e,i),void 0),n=s[0].opId;void 0!==n&&this.#N.set(e,n),r.push(...s)}return r}_insertAndSort(t){this.#D.push(t),this._sortItems()}_sortItems(){this.#D.sort(tX),this.invalidate()}_indexOfPosition(t){return this.#D.findIndex(e=>e._getParentKeyOrThrow()===t)}_attach(t,e){for(let i of(super._attach(t,e),this.#D))i._attach(e.generateId(),e)}_detach(){for(let t of(super._detach(),this.#D))t._detach()}#$(t){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:e,parentKey:i}=t,r=t9(t);r._attach(e,this._pool),r._setParentLink(this,i);let s=t.deletedId,n=this._indexOfPosition(i);if(-1!==n){let e=this.#D[n];if(e._id===s)return e._detach(),this.#D[n]=r,{modified:t0(this,[t1(n,r)]),reverse:[]};{this.#x.add(e),this.#D[n]=r;let i=[t1(n,r)],s=this.#L(t.deletedId);return s&&i.push(s),{modified:t0(this,i),reverse:[]}}}{let e=[],s=this.#L(t.deletedId);return s&&e.push(s),this._insertAndSort(r),e.push(t3(this._indexOfPosition(i),r)),{reverse:[],modified:t0(this,e)}}}#j(t){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let e=[],i=this.#L(t.deletedId);i&&e.push(i);let r=this.#N.get(t.parentKey);if(void 0!==r)if(r!==t.opId)return 0===e.length?{modified:!1}:{modified:t0(this,e),reverse:[]};else this.#N.delete(t.parentKey);let s=this._indexOfPosition(t.parentKey),n=this.#D.find(e=>e._id===t.id);if(void 0!==n){if(n._parentKey===t.parentKey)return{modified:e.length>0&&t0(this,e),reverse:[]};if(-1!==s){this.#x.add(this.#D[s]);let[t]=this.#D.splice(s,1);e.push(t2(s,t))}let i=this.#D.indexOf(n);n._setParentLink(this,t.parentKey),this._sortItems();let r=this.#D.indexOf(n);return r!==i&&e.push(t7(i,r,n)),{modified:e.length>0&&t0(this,e),reverse:[]}}{let i=this._pool.getNode(t.id);if(i&&this.#x.has(i)){i._setParentLink(this,t.parentKey),this.#x.delete(i),this._insertAndSort(i);let r=this.#D.indexOf(i);return{modified:t0(this,[-1===s?t3(r,i):t1(r,i),...e]),reverse:[]}}{-1!==s&&this.#D.splice(s,1);let{newItem:i,newIndex:r}=this.#M(t,t.parentKey);return{modified:t0(this,[-1===s?t3(r,i):t1(r,i),...e]),reverse:[]}}}}#L(t){if(void 0===t||void 0===this._pool)return null;let e=this._pool.getNode(t);if(void 0===e)return null;let i=this._detachChild(e);return!1===i.modified?null:i.modified.updates[0]}#U(t){if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let e=tH(t.parentKey),i=this._indexOfPosition(e);-1!==i&&this.#K(i,e);let{newItem:r,newIndex:s}=this.#M(t,e);return{modified:t0(this,[t3(s,r)]),reverse:[]}}#z(t){let e=this.#D.find(e=>e._id===t.id),i=tH(t.parentKey),r=this._indexOfPosition(i);if(e)if(e._parentKey===i)return{modified:!1};else{let t=this.#D.indexOf(e);-1!==r&&this.#K(r,i),e._setParentLink(this,i),this._sortItems();let s=this._indexOfPosition(i);return s===t?{modified:!1}:{modified:t0(this,[t7(t,s,e)]),reverse:[]}}{let e=tp(this._pool).getNode(t.id);if(e&&this.#x.has(e))return e._setParentLink(this,i),this.#x.delete(e),this._insertAndSort(e),{modified:t0(this,[t3(this._indexOfPosition(i),e)]),reverse:[]};{-1!==r&&this.#K(r,i);let{newItem:e,newIndex:s}=this.#M(t,i);return{modified:t0(this,[t3(s,e)]),reverse:[]}}}}#q(t){let{id:e,parentKey:i}=t,r=t9(t);if(this._pool?.getNode(e)!==void 0)return{modified:!1};r._attach(e,tp(this._pool)),r._setParentLink(this,i);let s=this._indexOfPosition(i),n=i;return -1!==s&&(n=tq(this.#D[s]?._parentPos,this.#D[s+1]?._parentPos),r._setParentLink(this,n)),this._insertAndSort(r),{modified:t0(this,[t3(this._indexOfPosition(n),r)]),reverse:[{type:5,id:e}]}}#B(t){let{id:e,parentKey:i}=t,r=t9(t);if(this._pool?.getNode(e)!==void 0)return{modified:!1};this.#N.set(i,tp(t.opId));let s=this._indexOfPosition(i);if(r._attach(e,tp(this._pool)),r._setParentLink(this,i),-1===s)return this._insertAndSort(r),this.#L(t.deletedId),{reverse:[{type:5,id:e}],modified:t0(this,[t3(this._indexOfPosition(i),r)])};{let e=this.#D[s];e._detach(),this.#D[s]=r;let n=t5(e._toOps(tp(this._id),i,this._pool),t.id),o=[t1(s,r)],a=this.#L(t.deletedId);return a&&o.push(a),{modified:t0(this,o),reverse:n}}}_attachChild(t,e){let i;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");return!1!==(i="set"===t.intent?1===e?this.#$(t):2===e?this.#j(t):this.#B(t):1===e?this.#U(t):2===e?this.#z(t):this.#q(t)).modified&&this.invalidate(),i}_detachChild(t){if(t){let e=tp(t._parentKey),i=t._toOps(tp(this._id),e,this._pool),r=this.#D.indexOf(t);if(-1===r)return{modified:!1};let[s]=this.#D.splice(r,1);return this.invalidate(),t._detach(),{modified:t0(this,[t2(r,s)]),reverse:i}}return{modified:!1}}#H(t,e){if(this.#x.has(e))return this.#x.delete(e),e._setParentLink(this,t),this._insertAndSort(e),{modified:t0(this,[t3(this.#D.indexOf(e),e)]),reverse:[]};if(t===e._parentKey)return{modified:!1};let i=this._indexOfPosition(t);if(-1===i){let i=this.#D.indexOf(e);e._setParentLink(this,t),this._sortItems();let r=this.#D.indexOf(e);return r===i?{modified:!1}:{modified:t0(this,[t7(i,r,e)]),reverse:[]}}{this.#D[i]._setParentLink(this,tq(t,this.#D[i+1]?._parentPos));let r=this.#D.indexOf(e);e._setParentLink(this,t),this._sortItems();let s=this.#D.indexOf(e);return s===r?{modified:!1}:{modified:t0(this,[t7(r,s,e)]),reverse:[]}}}#V(t,e){let i=tp(e._parentKey);if(this.#x.has(e)){let i=this._indexOfPosition(t);return this.#x.delete(e),-1!==i&&this.#D[i]._setParentLink(this,tq(t,this.#D[i+1]?._parentPos)),e._setParentLink(this,t),this._insertAndSort(e),{modified:!1}}{if(t===i)return{modified:!1};let r=this.#D.indexOf(e),s=this._indexOfPosition(t);-1!==s&&this.#D[s]._setParentLink(this,tq(t,this.#D[s+1]?._parentPos)),e._setParentLink(this,t),this._sortItems();let n=this.#D.indexOf(e);return r===n?{modified:!1}:{modified:t0(this,[t7(r,n,e)]),reverse:[]}}}#W(t,e){let i=tp(e._parentKey),r=this.#D.indexOf(e),s=this._indexOfPosition(t);-1!==s&&this.#D[s]._setParentLink(this,tq(t,this.#D[s+1]?._parentPos)),e._setParentLink(this,t),this._sortItems();let n=this.#D.indexOf(e);return r===n?{modified:!1}:{modified:t0(this,[t7(r,n,e)]),reverse:[{type:1,id:tp(e._id),parentKey:i}]}}_setChildKey(t,e,i){return 1===i?this.#H(t,e):2===i?this.#V(t,e):this.#W(t,e)}_apply(t,e){return super._apply(t,e)}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveList if parent is missing");return{type:1,parentId:tp(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key}}get length(){return this.#D.length}push(t){return this._pool?.assertStorageIsWritable(),this.insert(t,this.length)}insert(t,e){if(this._pool?.assertStorageIsWritable(),e<0||e>this.#D.length)throw Error(`Cannot insert list item at index "${e}". index should be between 0 and ${this.#D.length}`);let i=tq(this.#D[e-1]?this.#D[e-1]._parentPos:void 0,this.#D[e]?this.#D[e]._parentPos:void 0),r=eo(t);if(r._setParentLink(this,i),this._insertAndSort(r),this._pool&&this._id){let t=this._pool.generateId();r._attach(t,this._pool),this._pool.dispatch(r._toOps(this._id,i,this._pool),[{type:5,id:t}],new Map([[this._id,t0(this,[t3(e,r)])]]))}}move(t,e){if(this._pool?.assertStorageIsWritable(),e<0)throw Error("targetIndex cannot be less than 0");if(e>=this.#D.length)throw Error("targetIndex cannot be greater or equal than the list length");if(t<0)throw Error("index cannot be less than 0");if(t>=this.#D.length)throw Error("index cannot be greater or equal than the list length");let i=null,r=null;t<e?(r=e===this.#D.length-1?void 0:this.#D[e+1]._parentPos,i=this.#D[e]._parentPos):(r=this.#D[e]._parentPos,i=0===e?void 0:this.#D[e-1]._parentPos);let s=tq(i,r),n=this.#D[t],o=n._getParentKeyOrThrow();if(n._setParentLink(this,s),this._sortItems(),this._pool&&this._id){let i=new Map([[this._id,t0(this,[t7(t,e,n)])]]);this._pool.dispatch([{type:1,id:tp(n._id),opId:this._pool.generateOpId(),parentKey:s}],[{type:1,id:tp(n._id),parentKey:o}],i)}}delete(t){if(this._pool?.assertStorageIsWritable(),t<0||t>=this.#D.length)throw Error(`Cannot delete list item at index "${t}". index should be between 0 and ${this.#D.length-1}`);let e=this.#D[t];e._detach();let[i]=this.#D.splice(t,1);if(this.invalidate(),this._pool){let r=e._id;if(r){let s=new Map;s.set(tp(this._id),t0(this,[t2(t,i)])),this._pool.dispatch([{id:r,opId:this._pool.generateOpId(),type:5}],e._toOps(tp(this._id),e._getParentKeyOrThrow()),s)}}}clear(){if(this._pool?.assertStorageIsWritable(),this._pool){let t=[],e=[],i=[];for(let r of this.#D){r._detach();let s=r._id;s&&(t.push({type:5,id:s,opId:this._pool.generateOpId()}),e.push(...r._toOps(tp(this._id),r._getParentKeyOrThrow())),i.push(t2(0,r)))}this.#D=[],this.invalidate();let r=new Map;r.set(tp(this._id),t0(this,i)),this._pool.dispatch(t,e,r)}else{for(let t of this.#D)t._detach();this.#D=[],this.invalidate()}}set(t,e){if(this._pool?.assertStorageIsWritable(),t<0||t>=this.#D.length)throw Error(`Cannot set list item at index "${t}". index should be between 0 and ${this.#D.length-1}`);let i=this.#D[t],r=i._getParentKeyOrThrow(),s=i._id;i._detach();let n=eo(e);if(n._setParentLink(this,r),this.#D[t]=n,this.invalidate(),this._pool&&this._id){let e=this._pool.generateId();n._attach(e,this._pool);let o=new Map;o.set(this._id,t0(this,[t1(t,n)]));let a=t5(n._toOps(this._id,r,this._pool),s);this.#N.set(r,tp(a[0].opId));let l=t5(i._toOps(this._id,r,void 0),e);this._pool.dispatch(a,l,o)}}toArray(){return this.#D.map(t=>en(t))}every(t){return this.toArray().every(t)}filter(t){return this.toArray().filter(t)}find(t){return this.toArray().find(t)}findIndex(t){return this.toArray().findIndex(t)}forEach(t){return this.toArray().forEach(t)}get(t){if(!(t<0)&&!(t>=this.#D.length))return en(this.#D[t])}indexOf(t,e){return this.toArray().indexOf(t,e)}lastIndexOf(t,e){return this.toArray().lastIndexOf(t,e)}map(t){return this.#D.map((e,i)=>t(en(e),i))}some(t){return this.toArray().some(t)}[Symbol.iterator](){return new tZ(this.#D)}#M(t,e){let i=t9(t);return i._attach(t.id,tp(this._pool)),i._setParentLink(this,e),this._insertAndSort(i),{newItem:i,newIndex:this._indexOfPosition(e)}}#K(t,e){let i=tq(e,this.#D.length>t+1?this.#D[t+1]?._parentPos:void 0);this.#D[t]._setParentLink(this,i)}_toTreeNode(t){return{type:"LiveList",id:this._id??te(),key:t,payload:this.#D.map((t,e)=>t.toTreeNode(e.toString()))}}toImmutable(){return super.toImmutable()}_toImmutable(){return this.#D.map(t=>t.toImmutable())}clone(){return new t(this.#D.map(t=>t.clone()))}},tZ=class{#F;constructor(t){this.#F=t[Symbol.iterator]()}[Symbol.iterator](){return this}next(){let t=this.#F.next();return t.done?{done:!0,value:void 0}:{value:en(t.value)}}};function t0(t,e){return{node:t,type:"LiveList",updates:e}}function t1(t,e){return{index:t,type:"set",item:e instanceof tY?e.data:e}}function t2(t,e){return{type:"delete",index:t,deletedItem:e instanceof tY?e.data:e}}function t3(t,e){return{index:t,type:"insert",item:e instanceof tY?e.data:e}}function t7(t,e,i){return{type:"move",index:e,item:i instanceof tY?i.data:i,previousIndex:t}}function t5(t,e){return t.map((t,i)=>0===i?{...t,intent:"set",deletedId:e}:t)}var t4=class t extends tJ{#J;#G;constructor(t){if(super(),this.#G=new Map,t){let e=[];for(let[i,r]of t){let t=eo(r);t._setParentLink(this,i),e.push([i,t])}this.#J=new Map(e)}else this.#J=new Map}_toOps(t,e,i){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let r=[],s={id:this._id,opId:i?.generateOpId(),type:7,parentId:t,parentKey:e};for(let[t,e]of(r.push(s),this.#J))r.push(...e._toOps(this._id,t,i));return r}static _deserialize([e,i],r,s){let n=new t;n._attach(e,s);let o=r.get(e);if(void 0===o)return n;for(let[t,e]of o){let i=et([t,e],r,s);i._setParentLink(n,e.parentKey),n.#J.set(e.parentKey,i),n.invalidate()}return n}_attach(t,e){for(let[i,r]of(super._attach(t,e),this.#J))ei(r)&&r._attach(e.generateId(),e)}_attachChild(t,e){let i;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:r,parentKey:s,opId:n}=t,o=t9(t);if(void 0!==this._pool.getNode(r))return{modified:!1};if(2===e){let t=this.#G.get(s);if(t===n)return this.#G.delete(s),{modified:!1};if(void 0!==t)return{modified:!1}}else 1===e&&this.#G.delete(s);let a=this.#J.get(s);if(a){let t=tp(this._id);i=a._toOps(t,s),a._detach()}else i=[{type:5,id:r}];return o._setParentLink(this,s),o._attach(r,this._pool),this.#J.set(s,o),this.invalidate(),{modified:{node:this,type:"LiveMap",updates:{[s]:{type:"update"}}},reverse:i}}_detach(){for(let t of(super._detach(),this.#J.values()))t._detach()}_detachChild(t){let e=tp(this._id),i=tp(t._parentKey),r=t._toOps(e,i,this._pool);for(let[e,i]of this.#J)i===t&&(this.#J.delete(e),this.invalidate());return t._detach(),{modified:{node:this,type:"LiveMap",updates:{[i]:{type:"delete"}}},reverse:r}}_serialize(){if("HasParent"!==this.parent.type)throw Error("Cannot serialize LiveMap if parent is missing");return{type:2,parentId:tp(this.parent.node._id,"Parent node expected to have ID"),parentKey:this.parent.key}}get(t){let e=this.#J.get(t);if(void 0!==e)return en(e)}set(t,e){this._pool?.assertStorageIsWritable();let i=this.#J.get(t);i&&i._detach();let r=eo(e);if(r._setParentLink(this,t),this.#J.set(t,r),this.invalidate(),this._pool&&this._id){let e=this._pool.generateId();r._attach(e,this._pool);let s=new Map;s.set(this._id,{node:this,type:"LiveMap",updates:{[t]:{type:"update"}}});let n=r._toOps(this._id,t,this._pool);this.#G.set(t,tp(n[0].opId)),this._pool.dispatch(r._toOps(this._id,t,this._pool),i?i._toOps(this._id,t):[{type:5,id:e}],s)}}get size(){return this.#J.size}has(t){return this.#J.has(t)}delete(t){this._pool?.assertStorageIsWritable();let e=this.#J.get(t);if(void 0===e)return!1;if(e._detach(),this.#J.delete(t),this.invalidate(),this._pool&&e._id){let i=tp(this._id),r=new Map;r.set(i,{node:this,type:"LiveMap",updates:{[t]:{type:"delete"}}}),this._pool.dispatch([{type:5,id:e._id,opId:this._pool.generateOpId()}],e._toOps(i,t),r)}return!0}entries(){let t=this.#J.entries();return{[Symbol.iterator](){return this},next(){let e=t.next();return e.done?{done:!0,value:void 0}:{value:[e.value[0],en(e.value[1])]}}}}[Symbol.iterator](){return this.entries()}keys(){return this.#J.keys()}values(){let t=this.#J.values();return{[Symbol.iterator](){return this},next(){let e=t.next();return e.done?{done:!0,value:void 0}:{value:en(e.value)}}}}forEach(t){for(let e of this)t(e[1],e[0],this)}_toTreeNode(t){return{type:"LiveMap",id:this._id??te(),key:t,payload:Array.from(this.#J.entries()).map(([t,e])=>e.toTreeNode(t))}}toImmutable(){return super.toImmutable()}_toImmutable(){let t=new Map;for(let[e,i]of this.#J)t.set(e,i.toImmutable());return M(t)}clone(){return new t(Array.from(this.#J).map(([t,e])=>[t,e.clone()]))}},t6=class t extends tJ{#J;#Y;static #X(t){let e=new Map,i=null;for(let[s,n]of t){var r;if(0!==n.type||void 0!==(r=n).parentId&&void 0!==r.parentKey){let t=[s,n],i=e.get(n.parentId);void 0!==i?i.push(t):e.set(n.parentId,[t])}else i=[s,n]}if(null===i)throw Error("Root can't be null");return[i,e]}static _fromItems(e,i){let[r,s]=t.#X(e);return t._deserialize(r,s,i)}constructor(t={}){super(),this.#Y=new Map;let e=D(t);for(let t of Object.keys(e)){let i=e[t];ei(i)&&i._setParentLink(this,t)}this.#J=new Map(Object.entries(e))}_toOps(t,e,i){if(void 0===this._id)throw Error("Cannot serialize item is not attached");let r=i?.generateOpId(),s=[],n={type:4,id:this._id,opId:r,parentId:t,parentKey:e,data:{}};for(let[t,e]of(s.push(n),this.#J))ei(e)?s.push(...e._toOps(this._id,t,i)):n.data[t]=e;return s}static _deserialize([e,i],r,s){let n=new t(i.data);return n._attach(e,s),this._deserializeChildren(n,r,s)}static _deserializeChildren(t,e,i){let r=e.get(tp(t._id));if(void 0===r)return t;for(let[s,n]of r){let r=function([t,e],i,r){switch(e.type){case 0:return t6._deserialize([t,e],i,r);case 1:return tQ._deserialize([t,e],i,r);case 2:return t4._deserialize([t,e],i,r);case 3:return e.data;default:throw Error("Unexpected CRDT type")}}([s,n],e,i);ee(r)&&r._setParentLink(t,n.parentKey),t.#J.set(n.parentKey,r),t.invalidate()}return t}_attach(t,e){for(let[i,r]of(super._attach(t,e),this.#J))ei(r)&&r._attach(e.generateId(),e)}_attachChild(t,e){let i;if(void 0===this._pool)throw Error("Can't attach child if managed pool is not present");let{id:r,opId:s,parentKey:n}=t,o=t8(t);if(void 0!==this._pool.getNode(r))return this.#Y.get(n)===s&&this.#Y.delete(n),{modified:!1};if(0===e)this.#Y.set(n,tp(s));else if(void 0===this.#Y.get(n));else if(this.#Y.get(n)===s)return this.#Y.delete(n),{modified:!1};else return{modified:!1};let a=tp(this._id),l=this.#J.get(n);return ei(l)?(i=l._toOps(a,n),l._detach()):i=void 0===l?[{type:6,id:a,key:n}]:[{type:3,id:a,data:{[n]:l}}],this.#J.set(n,o),this.invalidate(),ee(o)&&(o._setParentLink(this,n),o._attach(r,this._pool)),{reverse:i,modified:{node:this,type:"LiveObject",updates:{[n]:{type:"update"}}}}}_detachChild(t){if(t){let e=tp(this._id),i=tp(t._parentKey),r=t._toOps(e,i,this._pool);for(let[e,i]of this.#J)i===t&&(this.#J.delete(e),this.invalidate());return t._detach(),{modified:{node:this,type:"LiveObject",updates:{[i]:{type:"delete"}}},reverse:r}}return{modified:!1}}_detach(){for(let t of(super._detach(),this.#J.values()))ei(t)&&t._detach()}_apply(t,e){return 3===t.type?this.#Q(t,e):6===t.type?this.#Z(t,e):super._apply(t,e)}_serialize(){let t={};for(let[e,i]of this.#J)ei(i)||(t[e]=i);return"HasParent"===this.parent.type&&this.parent.node._id?{type:0,parentId:this.parent.node._id,parentKey:this.parent.key,data:t}:{type:0,data:t}}#Q(t,e){let i=!1,r=tp(this._id),s=[],n={type:3,id:r,data:{}};for(let e in t.data){let t=this.#J.get(e);ei(t)?(s.push(...t._toOps(r,e)),t._detach()):void 0!==t?n.data[e]=t:void 0===t&&s.push({type:6,id:r,key:e})}let o={};for(let r in t.data){let s=t.data[r];if(void 0===s)continue;if(e)this.#Y.set(r,tp(t.opId));else if(void 0===this.#Y.get(r))i=!0;else{if(this.#Y.get(r)!==t.opId)continue;this.#Y.delete(r);continue}let n=this.#J.get(r);ei(n)&&n._detach(),i=!0,o[r]={type:"update"},this.#J.set(r,s),this.invalidate()}return 0!==Object.keys(n.data).length&&s.unshift(n),i?{modified:{node:this,type:"LiveObject",updates:o},reverse:s}:{modified:!1}}#Z(t,e){let i=t.key;if(!1===this.#J.has(i)||!e&&void 0!==this.#Y.get(i))return{modified:!1};let r=this.#J.get(i),s=tp(this._id),n=[];return ei(r)?(n=r._toOps(s,t.key),r._detach()):void 0!==r&&(n=[{type:3,id:s,data:{[i]:r}}]),this.#J.delete(i),this.invalidate(),{modified:{node:this,type:"LiveObject",updates:{[t.key]:{type:"delete"}}},reverse:n}}toObject(){return Object.fromEntries(this.#J)}set(t,e){this._pool?.assertStorageIsWritable(),this.update({[t]:e})}get(t){return this.#J.get(t)}delete(t){let e;this._pool?.assertStorageIsWritable();let i=this.#J.get(t);if(void 0===i)return;if(void 0===this._pool||void 0===this._id){ei(i)&&i._detach(),this.#J.delete(t),this.invalidate();return}ei(i)?(i._detach(),e=i._toOps(this._id,t)):e=[{type:3,data:{[t]:i},id:this._id}],this.#J.delete(t),this.invalidate();let r=new Map;r.set(this._id,{node:this,type:"LiveObject",updates:{[t]:{type:"delete"}}}),this._pool.dispatch([{type:6,key:t,id:this._id,opId:this._pool.generateOpId()}],e,r)}update(t){if(this._pool?.assertStorageIsWritable(),void 0===this._pool||void 0===this._id){for(let e in t){let i=t[e];if(void 0===i)continue;let r=this.#J.get(e);ei(r)&&r._detach(),ei(i)&&i._setParentLink(this,e),this.#J.set(e,i),this.invalidate()}return}let e=[],i=[],r=this._pool.generateOpId(),s={},n={id:this._id,type:3,data:{}},o={};for(let a in t){let l=t[a];if(void 0===l)continue;let h=this.#J.get(a);if(ei(h)?(i.push(...h._toOps(this._id,a)),h._detach()):void 0===h?i.push({type:6,id:this._id,key:a}):n.data[a]=h,ei(l)){l._setParentLink(this,a),l._attach(this._pool.generateId(),this._pool);let t=l._toOps(this._id,a,this._pool),i=t.find(t=>t.parentId===this._id);i&&this.#Y.set(a,tp(i.opId)),e.push(...t)}else s[a]=l,this.#Y.set(a,r);this.#J.set(a,l),this.invalidate(),o[a]={type:"update"}}0!==Object.keys(n.data).length&&i.unshift(n),0!==Object.keys(s).length&&e.unshift({opId:r,id:this._id,type:3,data:s});let a=new Map;a.set(this._id,{node:this,type:"LiveObject",updates:o}),this._pool.dispatch(e,i,a)}toImmutable(){return super.toImmutable()}toTreeNode(t){return super.toTreeNode(t)}_toTreeNode(t){let e=this._id??te();return{type:"LiveObject",id:e,key:t,payload:Array.from(this.#J.entries()).map(([t,i])=>ei(i)?i.toTreeNode(t):{type:"Json",id:`${e}:${t}`,key:t,payload:i})}}_toImmutable(){let t={};for(let[e,i]of this.#J)t[e]=ee(i)?i.toImmutable():i;return t}clone(){return new t(Object.fromEntries(Array.from(this.#J).map(([t,e])=>[t,ee(e)?e.clone():P(e)])))}};function t9(t){return eo(t8(t))}function t8(t){switch(t.type){case 8:return t.data;case 4:return new t6(t.data);case 7:return new t4;case 2:return new tQ([]);default:return tc(t,"Unknown creation Op")}}function et([t,e],i,r){switch(e.type){case 0:return t6._deserialize([t,e],i,r);case 1:return tQ._deserialize([t,e],i,r);case 2:return t4._deserialize([t,e],i,r);case 3:return tY._deserialize([t,e],i,r);default:throw Error("Unexpected CRDT type")}}function ee(t){return er(t)||t instanceof t4||es(t)}function ei(t){return ee(t)||t instanceof tY}function er(t){return t instanceof tQ}function es(t){return t instanceof t6}function en(t){return t instanceof tY?t.data:t instanceof tQ||t instanceof t4||t instanceof t6?t:tc(t,"Unknown AbstractCrdt")}function eo(t){return t instanceof t6||t instanceof t4||t instanceof tQ?t:new tY(t)}Symbol.iterator;var ea=(t=>(t[t.UPDATE_PRESENCE=100]="UPDATE_PRESENCE",t[t.BROADCAST_EVENT=103]="BROADCAST_EVENT",t[t.FETCH_STORAGE=200]="FETCH_STORAGE",t[t.UPDATE_STORAGE=201]="UPDATE_STORAGE",t[t.FETCH_YDOC=300]="FETCH_YDOC",t[t.UPDATE_YDOC=301]="UPDATE_YDOC",t))(ea||{});function el(t,e,i,r,s){if("number"!=typeof e||e<i||void 0!==r&&e>r)throw Error(void 0!==r?`${t} should be between ${s??i} and ${r}.`:`${t} should be at least ${s??i}.`);return e}var eh={paragraph:function(t){return"type"in t&&"paragraph"===t.type},text:function(t){return!("type"in t)&&"text"in t&&"string"==typeof t.text},link:function(t){return"type"in t&&"link"===t.type},mention:function(t){return"type"in t&&"mention"===t.type}},ed={paragraph:"block",text:"inline",link:"inline",mention:"inline"},eu={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ec=RegExp(Object.keys(eu).map(t=>`\\${t}`).join("|"),"g"),ep=class{#tt;#te;constructor(t,e){this.#tt=t,this.#te=e}toString(){return this.#tt.reduce((t,e,i)=>t+function(t){if(t instanceof ep)return t.toString();if(Array.isArray(t))return(t.length<=0?new ep([""],[]):new ep(["",...Array(t.length-1).fill(""),""],t)).toString();return String(t).replace(ec,t=>eu[t])}(tp(this.#te[i-1]))+e)}};function ef(t,...e){return new ep(t,e)}var em={_:"\\_","*":"\\*","#":"\\#","`":"\\`","~":"\\~","!":"\\!","|":"\\|","(":"\\(",")":"\\)","{":"\\{","}":"\\}","[":"\\[","]":"\\]"},ey=RegExp(Object.keys(em).map(t=>`\\${t}`).join("|"),"g"),e_=class{#tt;#te;constructor(t,e){this.#tt=t,this.#te=e}toString(){return this.#tt.reduce((t,e,i)=>t+function(t){if(t instanceof e_)return t.toString();if(Array.isArray(t))return(t.length<=0?new e_([""],[]):new e_(["",...Array(t.length-1).fill(""),""],t)).toString();return String(t).replace(ey,t=>em[t])}(tp(this.#te[i-1]))+e)}};function ew(t,...e){return new e_(t,e)}function eg(t){let e={};for(let i in t){let r=t[i];void 0!==r&&(e[i]=ev(r))}return e}function ev(t){if(t instanceof t6)return eg(t.toObject());if(t instanceof tQ)return t.toArray().map(ev);if(t instanceof t4){let e={};for(let[i,r]of t.entries())e[i]=ev(r);return e}return t instanceof tY?t.data:Array.isArray(t)?t.map(ev):k(t)?eg(t):t}function eE(t){if(Array.isArray(t))return new tQ(t.map(eE));if(!k(t))return t;{let e={};for(let i in t){let r=t[i];void 0!==r&&(e[i]=eE(r))}return new t6(e)}}Symbol.iterator;var eS=(t=>(t.Lexical="lexical",t.TipTap="tiptap",t.BlockNote="blocknote",t))(eS||{});async function eb(t){let e=[];for await(let i of t)e.push(i);return e}async function eO(t,e,i){let r=new Set;for await(let s of t){r.size>=i&&await Promise.race(r);let t=(async()=>{try{await e(s)}finally{r.delete(t)}})();r.add(t)}r.size>0&&await Promise.all(r)}f(d,u,"esm"),i(64478),i(35392);var eT=class extends TransformStream{constructor(){let t="";super({transform(e,i){if((t+=e).includes("\n")){let e=t.split("\n");for(let t=0;t<e.length-1;t++)e[t].length>0&&i.enqueue(e[t]);t=e[e.length-1]}},flush(e){t.length>0&&e.enqueue(t)}})}},ek=class extends TransformStream{constructor(){super({transform(t,e){let i=JSON.parse(t);e.enqueue(i)}})}},eA=/^[\w-]+$/;async function eI(){return void 0!==globalThis.fetch?globalThis.fetch:(await i.e(7539).then(i.bind(i,77539))).default}function eC(t){return"string"==typeof t}function eR(t,e){if(!(eC(t)&&t.length>0))throw Error(`Invalid value for field '${e}'. Please provide a non-empty string. For more information: https://liveblocks.io/docs/api-reference/liveblocks-node#authorize`)}function eP(t){return t>=200&&t<300?200:t>=500?503:403}var eD=Object.freeze(["room:write","room:read","room:presence:write","comments:write","comments:read"]),ex=Object.freeze(["room:read","room:presence:write","comments:read"]),eN=Object.freeze(["room:write","comments:write"]),e$=/^([*]|[^*]{1,128}[*]?)$/,eL=class{FULL_ACCESS=eN;READ_ACCESS=ex;#ti;#tr;#ts;#tn=!1;#to=new Map;constructor(t,e,i){eR(e,"userId"),this.#ti=t,this.#tr=e,this.#ts=i}#ta(t){if(this.#tn)throw Error("You can no longer change these permissions.");let e=this.#to.get(t);if(e)return e;if(this.#to.size>=10)throw Error("You cannot add permissions for more than 10 rooms in a single token");return e=new Set,this.#to.set(t,e),e}allow(t,e){if("string"!=typeof t)throw Error("Room name or pattern must be a string");if(!e$.test(t))throw Error("Invalid room name or pattern");if(0===e.length)throw Error("Permission list cannot be empty");let i=this.#ta(t);for(let t of e){if(!eD.includes(t))throw Error(`Not a valid permission: ${t}`);i.add(t)}return this}hasPermissions(){return this.#to.size>0}seal(){if(this.#tn)throw Error("You cannot reuse Session instances. Please create a new session every time.");this.#tn=!0}serializePermissions(){return Object.fromEntries(Array.from(this.#to.entries()).map(([t,e])=>[t,Array.from(e)]))}async authorize(){this.seal(),this.hasPermissions()||console.warn("Access tokens without any permission will not be supported soon, you should use wildcards when the client requests a token for resources outside a room. See https://liveblocks.io/docs/errors/liveblocks-client/access-tokens-not-enough-permissions");try{let t=await this.#ti(tu`/v2/authorize-user`,{userId:this.#tr,permissions:this.serializePermissions(),userInfo:this.#ts});return{status:eP(t.status),body:await t.text()}}catch(t){return{status:503,body:'Call to /v2/authorize-user failed. See "error" for more information.',error:t}}}};function ej(t){let e=new Date(t.createdAt),i=t.lastConnectionAt?new Date(t.lastConnectionAt):void 0;return{...t,createdAt:e,lastConnectionAt:i}}var eM=class{#tl;#th;constructor(t){let e=t.secret;!function(t,e){if(!(eC(t)&&t.startsWith("sk_")))throw Error(`Invalid value for field '${e}'. Secret keys must start with 'sk_'. Please provide the secret key from your Liveblocks dashboard at https://liveblocks.io/dashboard/apikeys.`);if(!eA.test(t))throw Error(`Invalid chars found in field '${e}'. Please check that you correctly copied the secret key from your Liveblocks dashboard at https://liveblocks.io/dashboard/apikeys.`)}(e,"secret"),this.#tl=e,this.#th=new URL(function(t){return"string"==typeof t&&t.startsWith("http")?t:"https://api.liveblocks.io"}(t.baseUrl))}async #td(t,e,i){let r=td(this.#th,t),s={Authorization:`Bearer ${this.#tl}`,"Content-Type":"application/json"},n=await eI();return await n(r,{method:"POST",headers:s,body:JSON.stringify(e),signal:i?.signal})}async #tu(t,e,i){let r=td(this.#th,t),s={Authorization:`Bearer ${this.#tl}`,"Content-Type":"application/json"},n=await eI();return await n(r,{method:"PUT",headers:s,body:JSON.stringify(e),signal:i?.signal})}async #tc(t,e,i,r){let s=td(this.#th,t,i),n={Authorization:`Bearer ${this.#tl}`,"Content-Type":"application/octet-stream"},o=await eI();return await o(s,{method:"PUT",headers:n,body:e,signal:r?.signal})}async #tp(t,e){let i=td(this.#th,t),r={Authorization:`Bearer ${this.#tl}`},s=await eI();return await s(i,{method:"DELETE",headers:r,signal:e?.signal})}async #tf(t,e,i){let r=td(this.#th,t,e),s={Authorization:`Bearer ${this.#tl}`},n=await eI();return await n(r,{method:"GET",headers:s,signal:i?.signal})}prepareSession(t,...e){let i=e[0];return new eL(this.#td.bind(this),t,i?.userInfo)}async identifyUser(t,...e){let i=e[0],r=tu`/v2/identify-user`,s="string"==typeof t?t:t.userId,n="string"==typeof t?void 0:t.groupIds;eR(s,"userId");try{let t=await this.#td(r,{userId:s,groupIds:n,userInfo:i?.userInfo});return{status:eP(t.status),body:await t.text()}}catch(t){return{status:503,body:`Call to ${td(this.#th,r)} failed. See "error" for more information.`,error:t}}}async getRooms(t={},e){let i,r=tu`/v2/rooms`;"string"==typeof t.query?i=t.query:"object"==typeof t.query&&(i=tr(t.query));let s={limit:t.limit,startingAfter:t.startingAfter,userId:t.userId,groupIds:t.groupIds?t.groupIds.join(","):void 0,...Object.fromEntries(Object.entries(t.metadata??{}).map(([t,e])=>[`metadata.${t}`,e])),query:i},n=await this.#tf(r,s,e);if(!n.ok)throw await eU.from(n);let o=await n.json(),a=o.data.map(ej);return{...o,data:a}}async *iterRooms(t,e){let i,{signal:r}=e??{},s=el("pageSize",e?.pageSize??40,20);for(;;){let{nextCursor:e,data:n}=await this.getRooms({...t,startingAfter:i,limit:s},{signal:r});for(let t of n)yield t;if(!e)break;i=e}}async createRoom(t,e,i){let{defaultAccesses:r,groupsAccesses:s,usersAccesses:n,metadata:o}=e,a=await this.#td(i?.idempotent?tu`/v2/rooms?idempotent`:tu`/v2/rooms`,{id:t,defaultAccesses:r,groupsAccesses:s,usersAccesses:n,metadata:o},i);if(!a.ok)throw await eU.from(a);return ej(await a.json())}async getOrCreateRoom(t,e,i){return await this.createRoom(t,e,{...i,idempotent:!0})}async upsertRoom(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/upsert`,e,i);if(!r.ok)throw await eU.from(r);return ej(await r.json())}async getRoom(t,e){let i=await this.#tf(tu`/v2/rooms/${t}`,void 0,e);if(!i.ok)throw await eU.from(i);return ej(await i.json())}async updateRoom(t,e,i){let{defaultAccesses:r,groupsAccesses:s,usersAccesses:n,metadata:o}=e,a=await this.#td(tu`/v2/rooms/${t}`,{defaultAccesses:r,groupsAccesses:s,usersAccesses:n,metadata:o},i);if(!a.ok)throw await eU.from(a);return ej(await a.json())}async deleteRoom(t,e){let i=await this.#tp(tu`/v2/rooms/${t}`,e);if(!i.ok)throw await eU.from(i)}async getActiveUsers(t,e){let i=await this.#tf(tu`/v2/rooms/${t}/active_users`,void 0,e);if(!i.ok)throw await eU.from(i);return await i.json()}async broadcastEvent(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/broadcast_event`,e,i);if(!r.ok)throw await eU.from(r)}async getStorageDocument(t,e="plain-lson",i){let r=await this.#tf(tu`/v2/rooms/${t}/storage`,{format:e},i);if(!r.ok)throw await eU.from(r);return await r.json()}async #tm(t,e){let i=await this.#td(tu`/v2/rooms/${t}/request-storage-mutation`,{},e);if(!i.ok)throw await eU.from(i);if("application/x-ndjson"!==i.headers.get("content-type"))throw Error("Unexpected response content type");if(null===i.body)throw Error("Unexpected null body in response");let r=i.body.pipeThrough(new TextDecoderStream).pipeThrough(new eT).pipeThrough(new ek)[Symbol.asyncIterator](),s=(await r.next()).value;if(!k(s)||"number"!=typeof s.actor)throw Error("Failed to obtain a unique session");let n=await eb(r);return{actor:s.actor,nodes:n}}async initializeStorageDocument(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/storage`,e,i);if(!r.ok)throw await eU.from(r);return await r.json()}async deleteStorageDocument(t,e){let i=await this.#tp(tu`/v2/rooms/${t}/storage`,e);if(!i.ok)throw await eU.from(i)}async getYjsDocument(t,e={},i){let{format:r,key:s,type:n}=e,o=tu`v2/rooms/${t}/ydoc`,a=await this.#tf(o,{formatting:r?"true":void 0,key:s,type:n},i);if(!a.ok)throw await eU.from(a);return await a.json()}async sendYjsBinaryUpdate(t,e,i={},r){let s=await this.#tc(tu`/v2/rooms/${t}/ydoc`,e,{guid:i.guid},r);if(!s.ok)throw await eU.from(s)}async getYjsDocumentAsBinaryUpdate(t,e={},i){let r=await this.#tf(tu`/v2/rooms/${t}/ydoc-binary`,{guid:e.guid},i);if(!r.ok)throw await eU.from(r);return r.arrayBuffer()}async createSchema(t,e,i){let r=await this.#td(tu`/v2/schemas`,{name:t,body:e},i);if(!r.ok)throw await eU.from(r);let s=await r.json(),n=new Date(s.createdAt),o=new Date(s.updatedAt);return{...s,createdAt:n,updatedAt:o}}async getSchema(t,e){let i=await this.#tf(tu`/v2/schemas/${t}`,void 0,e);if(!i.ok)throw await eU.from(i);let r=await i.json(),s=new Date(r.createdAt),n=new Date(r.updatedAt);return{...r,createdAt:s,updatedAt:n}}async updateSchema(t,e,i){let r=await this.#tu(tu`/v2/schemas/${t}`,{body:e},i);if(!r.ok)throw await eU.from(r);let s=await r.json(),n=new Date(s.createdAt),o=new Date(s.updatedAt);return{...s,createdAt:n,updatedAt:o}}async deleteSchema(t,e){let i=await this.#tp(tu`/v2/schemas/${t}`,e);if(!i.ok)throw await eU.from(i)}async getSchemaByRoomId(t,e){let i=await this.#tf(tu`/v2/rooms/${t}/schema`,void 0,e);if(!i.ok)throw await eU.from(i);let r=await i.json(),s=new Date(r.createdAt),n=new Date(r.updatedAt);return{...r,createdAt:s,updatedAt:n}}async attachSchemaToRoom(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/schema`,{schema:e},i);if(!r.ok)throw await eU.from(r);return await r.json()}async detachSchemaFromRoom(t,e){let i=await this.#tp(tu`/v2/rooms/${t}/schema`,e);if(!i.ok)throw await eU.from(i)}async getThreads(t,e){let i,{roomId:r}=t;"string"==typeof t.query?i=t.query:"object"==typeof t.query&&(i=tr(t.query));let s=await this.#tf(tu`/v2/rooms/${r}/threads`,{query:i},e);if(!s.ok)throw await eU.from(s);let{data:n}=await s.json();return{data:n.map(t=>y(t))}}async getThread(t,e){let{roomId:i,threadId:r}=t,s=await this.#tf(tu`/v2/rooms/${i}/threads/${r}`,void 0,e);if(!s.ok)throw await eU.from(s);return y(await s.json())}async getThreadParticipants(t,e){let{roomId:i,threadId:r}=t,s=await this.#tf(tu`/v2/rooms/${i}/threads/${r}/participants`,void 0,e);if(!s.ok)throw await eU.from(s);return await s.json()}async getThreadSubscriptions(t,e){let{roomId:i,threadId:r}=t,s=await this.#tf(tu`/v2/rooms/${i}/threads/${r}/subscriptions`,void 0,e);if(!s.ok)throw await eU.from(s);let{data:n}=await s.json();return{data:n.map(w)}}async getComment(t,e){let{roomId:i,threadId:r,commentId:s}=t,n=await this.#tf(tu`/v2/rooms/${i}/threads/${r}/comments/${s}`,void 0,e);if(!n.ok)throw await eU.from(n);return m(await n.json())}async createComment(t,e){let{roomId:i,threadId:r,data:s}=t,n=await this.#td(tu`/v2/rooms/${i}/threads/${r}/comments`,{...s,createdAt:s.createdAt?.toISOString()},e);if(!n.ok)throw await eU.from(n);return m(await n.json())}async editComment(t,e){let{roomId:i,threadId:r,commentId:s,data:n}=t,o=await this.#td(tu`/v2/rooms/${i}/threads/${r}/comments/${s}`,{...n,editedAt:n.editedAt?.toISOString()},e);if(!o.ok)throw await eU.from(o);return m(await o.json())}async deleteComment(t,e){let{roomId:i,threadId:r,commentId:s}=t,n=await this.#tp(tu`/v2/rooms/${i}/threads/${r}/comments/${s}`,e);if(!n.ok)throw await eU.from(n)}async createThread(t,e){let{roomId:i,data:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads`,{...r,comment:{...r.comment,createdAt:r.comment.createdAt?.toISOString()}},e);if(!s.ok)throw await eU.from(s);return y(await s.json())}async deleteThread(t,e){let{roomId:i,threadId:r}=t,s=await this.#tp(tu`/v2/rooms/${i}/threads/${r}`,e);if(!s.ok)throw await eU.from(s)}async markThreadAsResolved(t,e){let{roomId:i,threadId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads/${r}/mark-as-resolved`,{userId:t.data.userId},e);if(!s.ok)throw await eU.from(s);return y(await s.json())}async markThreadAsUnresolved(t,e){let{roomId:i,threadId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads/${r}/mark-as-unresolved`,{userId:t.data.userId},e);if(!s.ok)throw await eU.from(s);return y(await s.json())}async subscribeToThread(t,e){let{roomId:i,threadId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads/${r}/subscribe`,{userId:t.data.userId},e);if(!s.ok)throw await eU.from(s);var n=await s.json();let o=new Date(n.createdAt);return{...n,createdAt:o}}async unsubscribeFromThread(t,e){let{roomId:i,threadId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/threads/${r}/unsubscribe`,{userId:t.data.userId},e);if(!s.ok)throw await eU.from(s)}async editThreadMetadata(t,e){let{roomId:i,threadId:r,data:s}=t,n=await this.#td(tu`/v2/rooms/${i}/threads/${r}/metadata`,{...s,updatedAt:s.updatedAt?.toISOString()},e);if(!n.ok)throw await eU.from(n);return await n.json()}async addCommentReaction(t,e){var i;let{roomId:r,threadId:s,commentId:n,data:o}=t,a=await this.#td(tu`/v2/rooms/${r}/threads/${s}/comments/${n}/add-reaction`,{...o,createdAt:o.createdAt?.toISOString()},e);if(!a.ok)throw await eU.from(a);return{...i=await a.json(),createdAt:new Date(i.createdAt)}}async removeCommentReaction(t,e){let{roomId:i,threadId:r,data:s}=t,n=await this.#td(tu`/v2/rooms/${i}/threads/${r}/comments/${t.commentId}/remove-reaction`,{...s,removedAt:s.removedAt?.toISOString()},e);if(!n.ok)throw await eU.from(n)}async getInboxNotification(t,e){let{userId:i,inboxNotificationId:r}=t,s=await this.#tf(tu`/v2/users/${i}/inbox-notifications/${r}`,void 0,e);if(!s.ok)throw await eU.from(s);return _(await s.json())}async getInboxNotifications(t,e){let i,{userId:r}=t;"string"==typeof t.query?i=t.query:"object"==typeof t.query&&(i=tr(t.query));let s=await this.#tf(tu`/v2/users/${r}/inbox-notifications`,{query:i,limit:t?.limit,startingAfter:t?.startingAfter},e);if(!s.ok)throw await eU.from(s);let n=await s.json();return{...n,data:n.data.map(_)}}async *iterInboxNotifications(t,e){let i,{signal:r}=e??{},s=el("pageSize",e?.pageSize??50,10);for(;;){let{nextCursor:e,data:n}=await this.getInboxNotifications({...t,startingAfter:i,limit:s},{signal:r});for(let t of n)yield t;if(!e)break;i=e}}async getRoomNotificationSettings(t,e){return this.getRoomSubscriptionSettings(t,e)}async getUserRoomSubscriptionSettings(t,e){let{userId:i,startingAfter:r,limit:s}=t,n=await this.#tf(tu`/v2/users/${i}/room-subscription-settings`,{startingAfter:r,limit:s},e);if(!n.ok)throw await eU.from(n);return await n.json()}async getRoomSubscriptionSettings(t,e){let{userId:i,roomId:r}=t,s=await this.#tf(tu`/v2/rooms/${r}/users/${i}/subscription-settings`,void 0,e);if(!s.ok)throw await eU.from(s);return await s.json()}async updateRoomNotificationSettings(t,e){return this.updateRoomSubscriptionSettings(t,e)}async updateRoomSubscriptionSettings(t,e){let{userId:i,roomId:r,data:s}=t,n=await this.#td(tu`/v2/rooms/${r}/users/${i}/subscription-settings`,s,e);if(!n.ok)throw await eU.from(n);return await n.json()}async deleteRoomNotificationSettings(t,e){return this.deleteRoomSubscriptionSettings(t,e)}async deleteRoomSubscriptionSettings(t,e){let{userId:i,roomId:r}=t,s=await this.#tp(tu`/v2/rooms/${r}/users/${i}/subscription-settings`,e);if(!s.ok)throw await eU.from(s)}async updateRoomId(t,e){let{currentRoomId:i,newRoomId:r}=t,s=await this.#td(tu`/v2/rooms/${i}/update-room-id`,{newRoomId:r},e);if(!s.ok)throw await eU.from(s);return ej(await s.json())}async triggerInboxNotification(t,e){let i=await this.#td(tu`/v2/inbox-notifications/trigger`,t,e);if(!i.ok)throw await eU.from(i)}async deleteInboxNotification(t,e){let{userId:i,inboxNotificationId:r}=t,s=await this.#tp(tu`/v2/users/${i}/inbox-notifications/${r}`,e);if(!s.ok)throw await eU.from(s)}async deleteAllInboxNotifications(t,e){let{userId:i}=t,r=await this.#tp(tu`/v2/users/${i}/inbox-notifications`,e);if(!r.ok)throw await eU.from(r)}async getNotificationSettings(t,e){let{userId:i}=t,r=await this.#tf(tu`/v2/users/${i}/notification-settings`,void 0,e);if(!r.ok)throw await eU.from(r);return tj(await r.json())}async updateNotificationSettings(t,e){let{userId:i,data:r}=t,s=await this.#td(tu`/v2/users/${i}/notification-settings`,r,e);if(!s.ok)throw await eU.from(s);return tj(await s.json())}async deleteNotificationSettings(t,e){let{userId:i}=t,r=await this.#tp(tu`/v2/users/${i}/notification-settings`,e);if(!r.ok)throw await eU.from(r)}async mutateStorage(t,e,i){return this.#ty(t,void 0,e,i)}async massMutateStorage(t,e,i){let r=el("concurrency",i?.concurrency??8,1,20),s=Math.max(20,4*r),{signal:n}=i??{},o=this.iterRooms(t,{pageSize:s,signal:n}),a={signal:n};await eO(o,t=>this.#ty(t.id,t,e,a),r)}async #ty(t,e,i,r){let s,{signal:n,abort:o}=function(t){let e=new AbortController;return{signal:t?AbortSignal.any([e.signal,t]):e.signal,abort:e.abort.bind(e)}}(r?.signal),a=[],l=performance.now(),h=e=>{if(0===a.length||s)return;let i=performance.now();if(!(e||i-l>200))return;l=i;let r=a;a=[],s=this.#t_(t,[{type:ea.UPDATE_STORAGE,ops:r}],{signal:n}).catch(t=>{o(t)}).finally(()=>{s=void 0})};try{let{actor:r,nodes:s}=await this.#tm(t,{signal:n}),o=function(t,e){let{getCurrentConnectionId:i,onDispatch:r,isStorageWritable:s=()=>!0}=e,n=0,o=0,a=new Map;return{roomId:t,nodes:a,getNode:t=>a.get(t),addNode:(t,e)=>void a.set(t,e),deleteNode:t=>void a.delete(t),generateId:()=>`${i()}:${n++}`,generateOpId:()=>`${i()}:${o++}`,dispatch(t,e,i){r?.(t,e,i)},assertStorageIsWritable:()=>{if(!s())throw Error("Cannot write to storage with a read only user, please ensure the user has write permissions")}}}(t,{getCurrentConnectionId:()=>r,onDispatch:(t,e,i)=>{if(0!==t.length){for(let e of t)a.push(e);h(!1)}}}),l=t6._fromItems(s,o),d=i({room:e,root:l});h(!0),await d}catch(t){throw o(),t}finally{await s,h(!0),await s}}async #t_(t,e,i){let r=await this.#td(tu`/v2/rooms/${t}/send-message`,{messages:e},{signal:i?.signal});if(!r.ok)throw await eU.from(r)}},eU=class t extends Error{status;details;constructor(t,e,i){super(t),this.name="LiveblocksError",this.status=e,this.details=i}toString(){let t=`${this.name}: ${this.message} (status ${this.status})`;return this.details&&(t+=`
${this.details}`),t}static async from(e){let i,r=Error();Error.captureStackTrace(r,t.from);let s="An error happened without an error message";try{i=await e.text()}catch{i=s}let n=R(i)??{message:i},o=n.message||s,a=[n.suggestion?`Suggestion: ${String(n.suggestion)}`:void 0,n.docs?`See also: ${String(n.docs)}`:void 0].filter(Boolean).join("\n")||void 0,l=new t(o,e.status,a);return l.stack=r.stack,l}};f("@liveblocks/node","2.24.2","esm");let eK=(0,i(90647).H)().LIVEBLOCKS_SECRET,ez=async({userId:t,orgId:e,userInfo:i})=>{if(!eK)throw Error("LIVEBLOCKS_SECRET is not set");let r=new eM({secret:eK}).prepareSession(t,{userInfo:i});r.allow(`${e}:*`,r.FULL_ACCESS);let{status:s,body:n}=await r.authorize();return new Response(n,{status:s})},eq=["var(--color-red-500)","var(--color-orange-500)","var(--color-amber-500)","var(--color-yellow-500)","var(--color-lime-500)","var(--color-green-500)","var(--color-emerald-500)","var(--color-teal-500)","var(--color-cyan-500)","var(--color-sky-500)","var(--color-blue-500)","var(--color-indigo-500)","var(--color-violet-500)","var(--color-purple-500)","var(--color-fuchsia-500)","var(--color-pink-500)","var(--color-rose-500)"],eB=async()=>{let t=await (0,a.N)(),{orgId:e}=await (0,l.j)();return t&&e?ez({userId:t.id,orgId:e,userInfo:{name:t.fullName??t.emailAddresses.at(0)?.emailAddress??void 0,avatar:t.imageUrl??void 0,color:eq[Math.floor(Math.random()*eq.length)]}}):new Response("Unauthorized",{status:401})},eH=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/collaboration/auth/route",pathname:"/api/collaboration/auth",filename:"route",bundlePath:"app/api/collaboration/auth/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\collaboration\\auth\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:eV,workUnitAsyncStorage:eW,serverHooks:eF}=eH;function eJ(){return(0,o.patchFetch)({workAsyncStorage:eV,workUnitAsyncStorage:eW})}},74075:t=>{"use strict";t.exports=require("zlib")},76315:(t,e,i)=>{"use strict";i.d(e,{n:()=>o});var r=i(26790),s=i(54726);let n={secretKey:s.rB,publishableKey:s.At,apiUrl:s.H$,apiVersion:s.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:s.Rg,domain:s.V2,isSatellite:s.fS,sdkMetadata:s.tm,telemetry:{disabled:s.nN,debug:s.Mh}},o=t=>(0,r.z)({...n,...t})},76760:t=>{"use strict";t.exports=require("node:path")},77598:t=>{"use strict";t.exports=require("node:crypto")},79551:t=>{"use strict";t.exports=require("url")},81630:t=>{"use strict";t.exports=require("http")},89259:()=>{},90647:(t,e,i)=>{"use strict";i.d(e,{H:()=>n});var r=i(71166),s=i(25);let n=()=>(0,r.w)({server:{LIVEBLOCKS_SECRET:s.z.string().startsWith("sk_").optional()},runtimeEnv:{LIVEBLOCKS_SECRET:process.env.LIVEBLOCKS_SECRET}})}};var e=require("../../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),r=e.X(0,[5319,6239,2923,25,903,7838,1826],()=>i(73545));module.exports=r})();