(()=>{var e={};e.id=3414,e.ids=[3414],e.modules={1359:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});var r=s(12901),i=s(37838);async function n(){s(1447);let{userId:e}=await (0,i.j)();return e?(await (0,r.$)()).users.getUser(e):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12901:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var r=s(8741),i=s(23056),n=s(76315),a=s(97495);let o=new(s(16698)).AsyncLocalStorage;var l=s(60606);let c=async()=>{var e,t;let s;try{let e=await (0,i.TG)(),t=(0,a._b)(e,r.AA.Headers.ClerkRequestData);s=(0,l.Kk)(t)}catch(e){if(e&&(0,i.Sz)(e))throw e}let c=null!=(t=null==(e=o.getStore())?void 0:e.get("requestData"))?t:s;return(null==c?void 0:c.secretKey)||(null==c?void 0:c.publishableKey)?(0,n.n)(c):(0,n.n)({})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},26790:(e,t,s)=>{"use strict";s.d(t,{z:()=>q});var r,i,n,a,o,l,c,u,d,h,p,f,S,m,y,w,g,k,v,b=s(45940);s(92867);var A=s(37081);s(27322),s(6264);var x=s(49530),C=s(57136),K=s(94051),O=class{constructor(){(0,K.VK)(this,n),(0,K.VK)(this,r,"clerk_telemetry_throttler"),(0,K.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,K.S7)(this,n,l))return!1;let t=Date.now(),s=(0,K.jq)(this,n,a).call(this,e),c=(0,K.S7)(this,n,o)?.[s];if(!c){let e={...(0,K.S7)(this,n,o),[s]:t};localStorage.setItem((0,K.S7)(this,r),JSON.stringify(e))}if(c&&t-c>(0,K.S7)(this,i)){let e=(0,K.S7)(this,n,o);delete e[s],localStorage.setItem((0,K.S7)(this,r),JSON.stringify(e))}return!!c}};r=new WeakMap,i=new WeakMap,n=new WeakSet,a=function(e){let{sk:t,pk:s,payload:r,...i}=e,n={...r,...i};return JSON.stringify(Object.keys({...r,...i}).sort().map(e=>n[e]))},o=function(){let e=localStorage.getItem((0,K.S7)(this,r));return e?JSON.parse(e):{}},l=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,K.S7)(this,r)),!1}};var R={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},j=class{constructor(e){(0,K.VK)(this,f),(0,K.VK)(this,c),(0,K.VK)(this,u),(0,K.VK)(this,d,{}),(0,K.VK)(this,h,[]),(0,K.VK)(this,p),(0,K.OV)(this,c,{maxBufferSize:e.maxBufferSize??R.maxBufferSize,samplingRate:e.samplingRate??R.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:R.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,K.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,K.S7)(this,d).clerkVersion="",(0,K.S7)(this,d).sdk=e.sdk,(0,K.S7)(this,d).sdkVersion=e.sdkVersion,(0,K.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,C.q5)(e.publishableKey);t&&((0,K.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,K.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,K.OV)(this,u,new O)}get isEnabled(){return!("development"!==(0,K.S7)(this,d).instanceType||(0,K.S7)(this,c).disabled||"undefined"!=typeof process&&(0,x.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,K.S7)(this,c).debug||"undefined"!=typeof process&&(0,x.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,K.jq)(this,f,v).call(this,e.event,e.payload);(0,K.jq)(this,f,g).call(this,t.event,t),(0,K.jq)(this,f,S).call(this,t,e.eventSamplingRate)&&((0,K.S7)(this,h).push(t),(0,K.jq)(this,f,y).call(this))}};c=new WeakMap,u=new WeakMap,d=new WeakMap,h=new WeakMap,p=new WeakMap,f=new WeakSet,S=function(e,t){return this.isEnabled&&!this.isDebug&&(0,K.jq)(this,f,m).call(this,e,t)},m=function(e,t){let s=Math.random();return!!(s<=(0,K.S7)(this,c).samplingRate&&(void 0===t||s<=t))&&!(0,K.S7)(this,u).isEventThrottled(e)},y=function(){if("undefined"==typeof window)return void(0,K.jq)(this,f,w).call(this);if((0,K.S7)(this,h).length>=(0,K.S7)(this,c).maxBufferSize){(0,K.S7)(this,p)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,K.S7)(this,p)),(0,K.jq)(this,f,w).call(this);return}(0,K.S7)(this,p)||("requestIdleCallback"in window?(0,K.OV)(this,p,requestIdleCallback(()=>{(0,K.jq)(this,f,w).call(this)})):(0,K.OV)(this,p,setTimeout(()=>{(0,K.jq)(this,f,w).call(this)},0)))},w=function(){fetch(new URL("/v1/event",(0,K.S7)(this,c).endpoint),{method:"POST",body:JSON.stringify({events:(0,K.S7)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,K.OV)(this,h,[])}).catch(()=>void 0)},g=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},k=function(){let e={name:(0,K.S7)(this,d).sdk,version:(0,K.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},v=function(e,t){let s=(0,K.jq)(this,f,k).call(this);return{event:e,cv:(0,K.S7)(this,d).clerkVersion??"",it:(0,K.S7)(this,d).instanceType??"",sdk:s.name,sdkv:s.version,...(0,K.S7)(this,d).publishableKey?{pk:(0,K.S7)(this,d).publishableKey}:{},...(0,K.S7)(this,d).secretKey?{sk:(0,K.S7)(this,d).secretKey}:{},payload:t}};function q(e){let t={...e},s=(0,b.y3)(t),r=(0,b.Bs)({options:t,apiClient:s}),i=new j({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...r,telemetry:i}}(0,A.C)(b.nr)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74929:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>m,routeModule:()=>h,serverHooks:()=>S,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{GET:()=>u,OPTIONS:()=>d});var i=s(26142),n=s(94327),a=s(34862),o=s(37838),l=s(1359),c=s(26239);async function u(){try{let{userId:e}=await (0,o.j)();if(!e){let e=c.NextResponse.json({error:"Not authenticated"},{status:401});return e.headers.set("Access-Control-Allow-Origin","https://cubent.dev"),e.headers.set("Access-Control-Allow-Credentials","true"),e}let t=await (0,l.N)();if(!t){let e=c.NextResponse.json({error:"User not found"},{status:404});return e.headers.set("Access-Control-Allow-Origin","https://cubent.dev"),e.headers.set("Access-Control-Allow-Credentials","true"),e}let s={id:t.id,fullName:t.fullName,firstName:t.firstName,lastName:t.lastName,emailAddresses:t.emailAddresses.map(e=>({emailAddress:e.emailAddress})),imageUrl:t.imageUrl},r=c.NextResponse.json(s);return r.headers.set("Access-Control-Allow-Origin","https://cubent.dev"),r.headers.set("Access-Control-Allow-Credentials","true"),r}catch(t){console.error("Error fetching user:",t);let e=c.NextResponse.json({error:"Internal server error"},{status:500});return e.headers.set("Access-Control-Allow-Origin","https://cubent.dev"),e.headers.set("Access-Control-Allow-Credentials","true"),e}}async function d(){return new c.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"https://cubent.dev","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type","Access-Control-Allow-Credentials":"true"}})}let h=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/user/route",pathname:"/api/auth/user",filename:"route",bundlePath:"app/api/auth/user/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\auth\\user\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:S}=h;function m(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},76315:(e,t,s)=>{"use strict";s.d(t,{n:()=>a});var r=s(26790),i=s(54726);let n={secretKey:i.rB,publishableKey:i.At,apiUrl:i.H$,apiVersion:i.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i.Rg,domain:i.V2,isSatellite:i.fS,sdkMetadata:i.tm,telemetry:{disabled:i.nN,debug:i.Mh}},a=e=>(0,r.z)({...n,...e})},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},89259:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,903,7838],()=>s(74929));module.exports=r})();