{"common": {"save": "Speichern", "done": "<PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "reset": "Z<PERSON>ücksetzen", "select": "Auswählen", "add": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "remove": "Entfernen"}, "header": {"title": "Einstellungen", "saveButtonTooltip": "Änderungen speichern", "nothingChangedTooltip": "Nichts geändert", "doneButtonTooltip": "Ungespeicherte Änderungen verwerfen und Einstellungsbereich schließen"}, "unsavedChangesDialog": {"title": "Ungespeicherte Änderungen", "description": "Möchtest du die Änderungen verwerfen und fortfahren?", "cancelButton": "Abbrechen", "discardButton": "Änderungen verwerfen"}, "sections": {"providers": "<PERSON><PERSON><PERSON>", "autoApprove": "Auto-Genehmigung", "browser": "Computerzugriff", "checkpoints": "Kontrollpunkte", "notifications": "Benachrichtigungen", "contextManagement": "Kontext", "terminal": "Terminal", "prompts": "Eingabeaufforderungen", "experimental": "Experimentell", "language": "<PERSON><PERSON><PERSON>", "about": "Über Roo Code"}, "prompts": {"description": "Konfiguriere Support-Prompts, die für schnelle Aktionen wie das Verbessern von Prompts, das Erklären von Code und das Beheben von Problemen verwendet werden. Diese Prompts helf<PERSON> <PERSON><PERSON> da<PERSON>, bessere Unterstützung für häufige Entwicklungsaufgaben zu bieten."}, "codeIndex": {"title": "Codebase-Indexierung", "enableLabel": "Codebase-Indexierung aktivieren", "enableDescription": "<0>Codebase-Indexierung</0> ist eine experimentelle Funktion, die einen semantischen Suchindex deines Projekts mit KI-Embeddings erstellt. Dies ermöglicht es Roo Code, große Codebasen besser zu verstehen und zu navigieren, indem relevanter Code basierend auf Bedeutung statt nur Schlüsselwörtern gefunden wird.", "providerLabel": "Embeddings-<PERSON><PERSON><PERSON>", "selectProviderPlaceholder": "Anbieter auswählen", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "openaiKeyLabel": "OpenAI-Schlüssel:", "modelLabel": "<PERSON><PERSON>", "selectModelPlaceholder": "<PERSON><PERSON> auswählen", "ollamaUrlLabel": "Ollama-URL:", "qdrantUrlLabel": "Qdrant-URL", "qdrantKeyLabel": "Qdrant-Schlüssel:", "startIndexingButton": "Indexierung starten", "clearIndexDataButton": "Indexdaten löschen", "unsavedSettingsMessage": "Bitte speichere deine Einstellungen, bevor du den Indexierungsprozess startest.", "clearDataDialog": {"title": "Sind Sie sicher?", "description": "Diese Aktion kann nicht rückgängig gemacht werden. Dies wird Ihre Codebase-Indexdaten dauerhaft löschen.", "cancelButton": "Abbrechen", "confirmButton": "Daten löschen"}}, "autoApprove": {"description": "<PERSON><PERSON><PERSON><PERSON> Roo, Operationen automatisch ohne Genehmigung durchzuführen. Aktiviere diese Einstellungen nur, wenn du der KI vollständig vertraust und die damit verbundenen Sicherheitsrisiken verstehst.", "readOnly": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON> a<PERSON>, wird Roo automatisch Verzeichnisinhalte anzeigen und Dateien lesen, ohne dass du auf die Genehmigen-Schaltfläche klicken musst.", "outsideWorkspace": {"label": "Dateien außerhalb des Arbeitsbereichs einbeziehen", "description": "<PERSON><PERSON>, <PERSON><PERSON> außerhalb des aktuellen Arbeitsbereichs ohne Genehmigung zu lesen."}}, "write": {"label": "Schreiben", "description": "Dateien automatisch erstellen und bearbeiten ohne Genehmigung", "delayLabel": "Verzögerung nach Schreibvorgängen, damit Diagnosefunktionen potenzielle Probleme erkennen können", "outsideWorkspace": {"label": "Dateien außerhalb des Arbeitsbereichs einbeziehen", "description": "<PERSON><PERSON>, <PERSON><PERSON> außerhalb des aktuellen Arbeitsbereichs ohne Genehmigung zu erstellen und zu bearbeiten."}}, "browser": {"label": "Browser", "description": "Browser-Aktionen automatisch ohne Genehmigung durchführen. Hinweis: <PERSON><PERSON> nur, wenn das Modell Computer-Nutzung unterstützt"}, "retry": {"label": "Wiederholung", "description": "Fehlgeschlagene API-Anfragen automatisch wiederholen, wenn der Server eine Fehlerantwort zurückgibt", "delayLabel": "Verzögerung vor dem Wiederholen der Anfrage"}, "mcp": {"label": "MCP", "description": "Automatische Genehmigung einzelner MCP-Tools in der MCP-Server-Ansicht aktivieren (er<PERSON><PERSON> sowohl diese Einstellung als auch das 'Immer erlauben'-Kontrollkästchen des Tools)"}, "modeSwitch": {"label": "Modus", "description": "Automatisch zwischen verschiedenen Modi wechseln ohne Genehmigung"}, "subtasks": {"label": "Teilaufgaben", "description": "Erstellung und Abschluss von Unteraufgaben ohne Genehmigung erlauben"}, "execute": {"label": "Ausführen", "description": "Erlaubte Terminal-Befehle automatisch ohne Genehmigung ausführen", "allowedCommands": "Erlaubte Auto-Ausführungsbefehle", "allowedCommandsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die automatisch ausgeführt werden können, wenn 'Ausführungsoperationen immer genehmigen' aktiviert ist. Fügen Si<PERSON> * hinzu, um alle Befehle zu erlauben (mit Vorsicht verwenden).", "commandPlaceholder": "Befehlsprä<PERSON><PERSON> (z.B. 'git ')", "addButton": "Hinzufügen"}, "apiRequestLimit": {"title": "Maximale Anfragen", "description": "Automatisch so viele API-Anfragen stellen, bevor du um die Erlaubnis gebeten wirst, mit der Aufgabe fortzufahren.", "unlimited": "Unbegrenzt"}}, "providers": {"providerDocumentation": "{{provider}}-Do<PERSON><PERSON>", "configProfile": "Konfigurationsprofil", "description": "Speichern Sie verschiedene API-Konfigurationen, um schnell zwischen Anbietern und Einstellungen zu wechseln.", "apiProvider": "API-Anbieter", "model": "<PERSON><PERSON>", "nameEmpty": "Name darf nicht leer sein", "nameExists": "Ein Profil mit diesem Namen existiert bereits", "deleteProfile": "<PERSON><PERSON>", "invalidArnFormat": "Ungültiges ARN-Format. Überprüfen Sie die obigen Beispiele.", "enterNewName": "Neuen Namen e<PERSON>ben", "addProfile": "<PERSON><PERSON>", "renameProfile": "<PERSON><PERSON>", "newProfile": "Neues Konfigurationsprofil", "enterProfileName": "Profilnamen e<PERSON>ben", "createProfile": "<PERSON><PERSON>", "cannotDeleteOnlyProfile": "Das einzige Profil kann nicht gelöscht werden", "searchPlaceholder": "Profile durchsuchen", "noMatchFound": "<PERSON><PERSON> passenden Profile gefunden", "vscodeLmDescription": "Die VS Code Language Model API ermöglicht das Ausführen von Modellen, die von anderen VS Code-Erweiterungen bereitgestellt werden (eins<PERSON><PERSON><PERSON>lich, aber nicht beschränkt auf GitHub Copilot). Der einfachste Weg, um zu starten, besteht darin, die Erweiterungen Copilot und Copilot Chat aus dem VS Code Marketplace zu installieren.", "awsCustomArnUse": "Geben Sie eine gültige Amazon Bedrock ARN für das Modell ein, das Si<PERSON> verwenden möchten. Formatbeispiele:", "awsCustomArnDesc": "<PERSON><PERSON><PERSON>, dass die Region in der ARN mit Ihrer oben ausgewählten AWS-Region übereinstimmt.", "openRouterApiKey": "OpenRouter API-Schlüssel", "getOpenRouterApiKey": "OpenRouter API-Schlüssel erhalten", "apiKeyStorageNotice": "API-Schlüssel werden sicher im VSCode Secret Storage gespeichert", "glamaApiKey": "Glama API-Schlüssel", "getGlamaApiKey": "Glama API-Schlüssel erhalten", "useCustomBaseUrl": "Benutzerdefinierte Basis-URL verwenden", "useReasoning": "Reasoning aktivieren", "useHostHeader": "Benutzerdefinierten Host-Header verwenden", "useLegacyFormat": "Altes OpenAI API-Format verwenden", "customHeaders": "Benutzerdefinierte Headers", "headerName": "Header-Name", "headerValue": "Header-<PERSON><PERSON>", "noCustomHeaders": "<PERSON><PERSON> benutzerdefinierten Headers definiert. Klicke auf die + Schaltfläche, um einen hinzuzufügen.", "requestyApiKey": "Requesty API-Schlüssel", "refreshModels": {"label": "Modelle aktualisieren", "hint": "Bitte öffne die Einstellungen erneut, um die neuesten Modelle zu sehen.", "loading": "Modellliste wird aktualisiert...", "success": "Modellliste erfolgreich aktualisiert!", "error": "Fehler beim Aktualisieren der Modellliste. Bitte versuche es erneut."}, "getRequestyApiKey": "Requesty API-Schlüssel erhalten", "openRouterTransformsText": "Prompts und Nachrichtenketten auf Kontextgröße komprimieren (<a>OpenRouter Transformationen</a>)", "anthropicApiKey": "Anthropic API-Schlüssel", "getAnthropicApiKey": "Anthropic API-Schlüssel erhalten", "anthropicUseAuthToken": "Anthropic API-Schlüssel als Authorization-Header an<PERSON><PERSON> von X-Api-Key übergeben", "chutesApiKey": "Chutes API-Schlüssel", "getChutesApiKey": "Chutes API-Schlüssel erhalten", "deepSeekApiKey": "DeepSeek API-Schl<PERSON>ssel", "getDeepSeekApiKey": "DeepSeek API-Schlüssel erhalten", "geminiApiKey": "Gemini API-Schlüssel", "getGroqApiKey": "Groq API-Schlüssel erhalten", "groqApiKey": "Groq API-Schlüssel", "getGeminiApiKey": "Gemini API-Schlüssel erhalten", "openAiApiKey": "OpenAI API-Schlüssel", "apiKey": "API-Schlüssel", "openAiBaseUrl": "Basis-URL", "getOpenAiApiKey": "OpenAI API-Schlüssel erhalten", "mistralApiKey": "Mistral API-Schlüssel", "getMistralApiKey": "Mistral / Codestral API-Schlüssel erhalten", "codestralBaseUrl": "Codestral Basis-URL (Optional)", "codestralBaseUrlDesc": "Legen Sie eine alternative URL für das Codestral-Modell fest.", "xaiApiKey": "xAI API-Schlüssel", "getXaiApiKey": "xAI API-Schlüssel erhalten", "litellmApiKey": "LiteLLM API-Schlüssel", "litellmBaseUrl": "LiteLLM Basis-URL", "awsCredentials": "AWS Anmeldedaten", "awsProfile": "AWS Profil", "awsProfileName": "AWS Profilname", "awsAccessKey": "AWS Zugangsschlüssel", "awsSecretKey": "AWS Geheimschlüssel", "awsSessionToken": "AWS Sitzungstoken", "awsRegion": "AWS Region", "awsCrossRegion": "Regionsübergreifende Inferenz verwenden", "awsBedrockVpc": {"useCustomVpcEndpoint": "Benutzerdefinierten VPC-Endpunkt verwenden", "vpcEndpointUrlPlaceholder": "VPC-Endpunkt-URL eingeben (optional)", "examples": "Beispiele:"}, "enablePromptCaching": "Prompt-Caching aktivieren", "enablePromptCachingTitle": "Prompt-Caching aktivieren, um die Leistung zu verbessern und Kosten für unterstützte Modelle zu reduzieren.", "cacheUsageNote": "Hinweis: <PERSON><PERSON><PERSON> keine Cache-Nutzung sehen, versuchen Si<PERSON> ein anderes Modell auszuwählen und dann Ihr gewünschtes Modell erneut auszuwählen.", "vscodeLmModel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vscodeLmWarning": "Hinweis: Dies ist eine sehr experimentelle Integration und die Anbieterunterstützung variiert. Wenn Sie einen Fehler über ein nicht unterstütztes Modell erhalten, liegt das Problem auf Anbieterseite.", "googleCloudSetup": {"title": "Um Google Cloud Vertex AI zu verwenden, m<PERSON><PERSON> Si<PERSON>:", "step1": "1. Ein Google Cloud-<PERSON><PERSON> erstellen, die Vertex AI API aktivieren & die gewünschten Claude-Modelle aktivieren.", "step2": "2. Die Google Cloud CLI installieren & Standardanmeldeinformationen für die Anwendung konfigurieren.", "step3": "3. <PERSON>der ein Servicekonto mit Anmeldeinformationen erstellen."}, "googleCloudCredentials": "Google Cloud Anmeldedaten", "googleCloudKeyFile": "Google Cloud Schlüsseldateipfad", "googleCloudProjectId": "Google Cloud Projekt-ID", "googleCloudRegion": "Google Cloud Region", "lmStudio": {"baseUrl": "Basis-URL (optional)", "modelId": "Modell-ID", "speculativeDecoding": "Spekulatives Dekodieren aktivieren", "draftModelId": "Entwurfsmodell-ID", "draftModelDesc": "<PERSON> Entwurfsmodell muss aus derselben Modellfamilie stammen, damit das spekulative Dekodieren korrekt funktioniert.", "selectDraftModel": "Entwurfsmodell auswählen", "noModelsFound": "<PERSON><PERSON>twurfsmodelle gefunden. <PERSON><PERSON> stelle sicher, dass LM Studio mit aktiviertem Servermodus läuft.", "description": "LM Studio ermöglicht es dir, Modelle lokal auf deinem Computer auszuführen. Eine Anleitung zum Einstieg findest du in ihrem <a>Schnellstart-Guide</a>. Du musst auch die <b>lokale Server</b>-Funktion von LM Studio starten, um es mit dieser Erweiterung zu verwenden. <span>Hinweis:</span> Roo Code verwendet komplexe Prompts und funktioniert am besten mit Claude-Modellen. Weniger leistungsfähige Modelle funktionieren möglicherweise nicht wie erwartet."}, "ollama": {"baseUrl": "Basis-URL (optional)", "modelId": "Modell-ID", "description": "Ollama ermöglicht es dir, <PERSON>le lokal auf deinem Computer auszuführen. Eine Anleitung zum Einstieg findest du im Schnellstart-Guide.", "warning": "Hinweis: Roo Code verwendet komplexe Prompts und funktioniert am besten mit Claude-Modellen. Weniger leistungsfähige Modelle funktionieren möglicherweise nicht wie erwartet."}, "unboundApiKey": "Unbound API-Schlüssel", "getUnboundApiKey": "Unbound API-Schlüssel erhalten", "unboundRefreshModelsSuccess": "Modellliste aktualisiert! Sie können jetzt aus den neuesten Modellen auswählen.", "unboundInvalidApiKey": "Ungültiger API-Schlüssel. Bitte überprüfen Sie Ihren API-Schlüssel und versuchen Sie es erneut.", "humanRelay": {"description": "Es ist kein API-<PERSON><PERSON><PERSON><PERSON>, aber der Benutzer muss beim Kopieren und Einfügen der Informationen in den Web-Chat-KI helfen.", "instructions": "Während der Verwendung wird ein Dialogfeld angezeigt und die aktuelle Nachricht wird automatisch in die Zwischenablage kopiert. Du musst diese in Web-Versionen von KI (wie ChatGPT oder Claude) einfügen, dann die Antwort der KI zurück in das Dialogfeld kopieren und auf die Bestätigungsschaltfläche klicken."}, "openRouter": {"providerRouting": {"title": "OpenRout<PERSON> <PERSON><PERSON><PERSON>-Routing", "description": "OpenRouter leitet Anfragen an die besten verfügbaren Anbieter für dein Modell weiter. Standardmäßig werden Anfragen über die Top-Anbieter lastverteilt, um maximale Verfügbarkeit zu gewährleisten. Du kannst jedoch einen bestimmten Anbieter für dieses Modell auswählen.", "learnMore": "Mehr über Anbieter-Routing erfahren"}}, "customModel": {"capabilities": "Konfiguriere die Fähigkeiten und Preise für dein benutzerdefiniertes OpenAI-kompatibles Modell. Sei vorsichtig bei der Angabe der Modellfähigkeiten, da diese beeinf<PERSON>sen können, wie Roo Code funktioniert.", "maxTokens": {"label": "Maximale Ausgabe-Tokens", "description": "Maximale Anzahl <PERSON>, die das Modell in einer Antwort generieren kann. (Geben Sie -1 an, damit der <PERSON> die maximalen Tokens festlegt.)"}, "contextWindow": {"label": "Kontextfenstergröße", "description": "<PERSON><PERSON><PERSON><PERSON> (Eingabe + Ausgabe), die das Modell verarbeiten kann."}, "imageSupport": {"label": "Bildunterstützung", "description": "Ist dieses Modell in der Lage, Bilder zu verarbeiten und zu verstehen?"}, "computerUse": {"label": "Computer-Nutzung", "description": "<PERSON>t dies<PERSON> Modell in der Lage, mit einem Browser zu interagieren? (<PERSON><PERSON><PERSON><PERSON> 3.7 Sonnet)"}, "promptCache": {"label": "Prompt-Caching", "description": "Ist dieses Modell in der Lage, Prompts zu cachen?"}, "pricing": {"input": {"label": "Eingabepreis", "description": "Kosten pro Million Tokens in der Eingabe/Prompt. Dies beeinflusst die Kosten für das Senden von Kontext und Anweisungen an das Modell."}, "output": {"label": "Ausgabepreis", "description": "Kosten pro Million Tokens in der Modellantwort. Dies beeinflusst die Kosten für generierte Inhalte und Vervollständigungen."}, "cacheReads": {"label": "<PERSON>ache<PERSON><PERSON>", "description": "Kosten pro Million Tokens für das Lesen aus dem Cache. Dies ist der Preis, der beim Abrufen einer gecachten Antwort berechnet wird."}, "cacheWrites": {"label": "Cache-Schreibpreis", "description": "Kosten pro Million Tokens für das Schreiben in den Cache. Dies ist der Preis, der beim ersten Cachen eines Prompts berechnet wird."}}, "resetDefaults": "Auf Standardwerte zurücksetzen"}, "rateLimitSeconds": {"label": "Ratenbegrenzung", "description": "Minimale Zeit zwischen API-Anfragen."}, "reasoningEffort": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "high": "Hoch", "medium": "<PERSON><PERSON><PERSON>", "low": "<PERSON><PERSON><PERSON>"}, "setReasoningLevel": "Denkaufwand aktivieren"}, "browser": {"enable": {"label": "Browser-Tool aktivieren", "description": "<PERSON><PERSON><PERSON><PERSON>, kann <PERSON><PERSON> einen <PERSON> verwenden, um mit Websites zu interagieren, wenn Modelle verwendet werden, die Computer-Nutzung unterstützen. <0><PERSON>hr erfahren</0>"}, "viewport": {"label": "Viewport-Größe", "description": "Wählen Sie die Viewport-Größe für Browser-Interaktionen. Dies beeinflusst, wie Websites angezeigt und mit ihnen interagiert wird.", "options": {"largeDesktop": "G<PERSON>ßer Desktop (1280x800)", "smallDesktop": "<PERSON><PERSON> Desktop (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Mobil (360x640)"}}, "screenshotQuality": {"label": "Screenshot-Qualität", "description": "Passen Sie die WebP-Qualität von Browser-Screenshots an. Höhere Werte bieten klarere Screenshots, erhöhen aber den Token-Verbrauch."}, "remote": {"label": "Remote-Browser-Verbindung verwenden", "description": "Verbindung zu einem Chrome-Browser herstellen, der mit aktiviertem Remote-Debugging läuft (--remote-debugging-port=9222).", "urlPlaceholder": "Benutzerdefinierte URL (z.B. http://localhost:9222)", "testButton": "Verbindung testen", "testingButton": "Teste...", "instructions": "Geben Sie die DevTools-Protokoll-Host-Adresse ein oder lassen Si<PERSON> das Feld leer, um Chrome lokale Instanzen automatisch zu erkennen. Die Schaltfläche 'Verbindung testen' versucht die benutzerdefinierte URL, wenn angegeben, oder erkennt automatisch, wenn das Feld leer ist."}}, "checkpoints": {"enable": {"label": "Automatische Kontrollpunkte aktivieren", "description": "<PERSON><PERSON>, erstellt Roo automatisch Kontrollpunkte während der Aufgabenausführung, was die Überprüfung von Änderungen oder die Rückkehr zu früheren Zuständen erleichtert. <0><PERSON>hr erfahren</0>"}}, "notifications": {"sound": {"label": "Soundeffekte aktivieren", "description": "<PERSON><PERSON> a<PERSON><PERSON><PERSON>, spielt Roo Soundeffekte für Benachrichtigungen und Ereignisse ab.", "volumeLabel": "Lautstärke"}, "tts": {"label": "Text-zu-Sprache aktivieren", "description": "<PERSON><PERSON> akt<PERSON><PERSON>, liest Roo seine Antworten mit Text-zu-Sprache laut vor.", "speedLabel": "Geschwindigkeit"}}, "contextManagement": {"description": "Steuern Sie, welche Informationen im KI-Kontextfenster enthalten sind, was den Token-Verbrauch und die Antwortqualität beeinflusst", "autoCondenseContextPercent": {"label": "Schwellenwert für intelligente Kontextkomprimierung", "description": "<PERSON><PERSON> das Kontextfenster diesen Schwellenwert erreicht, wird Roo es automatisch komprimieren."}, "condensingApiConfiguration": {"label": "API-Konfiguration für Kontextkomprimierung", "description": "<PERSON><PERSON><PERSON><PERSON> Sie, welche API-Konfiguration für Kontextkomprimierungsoperationen verwendet werden soll. Lassen Sie unausgewählt, um die aktuelle aktive Konfiguration zu verwenden.", "useCurrentConfig": "Aktuelle Konfiguration verwenden"}, "customCondensingPrompt": {"label": "Benutzerdefinierter Kontextkomprimierungs-Prompt", "description": "Passen Sie den System-Prompt an, der für die Kontextkomprimierung verwendet wird. <PERSON><PERSON>, um den Standard-Prompt zu verwenden.", "placeholder": "G<PERSON>en Sie hier Ihren benutzerdefinierten Komprimierungs-Prompt ein...\n\nSie können die gleiche Struktur wie der Standard-Prompt verwenden:\n- Vorherige Konversation\n- Aktuelle Arbeit\n- Wichtige technische Konzepte\n- Relevante Dateien und Code\n- Problemlösung\n- Ausstehende Aufgaben und nächste Schritte", "reset": "Auf Standard zurücksetzen", "hint": "Leer = Standard-Prompt verwenden"}, "autoCondenseContext": {"name": "Intelligente Kontextkomprimierung automatisch auslösen"}, "openTabs": {"label": "Geöffnete Tabs Kontextlimit", "description": "Maximale Anzahl von geöffneten VSCode-Tabs, die im Kontext enthalten sein sollen. Höhere Werte bieten mehr Kontext, erhöhen aber den Token-Verbrauch."}, "workspaceFiles": {"label": "Workspace-<PERSON><PERSON>", "description": "Maximale Anzahl <PERSON>, die in den Details des aktuellen Arbeitsverzeichnisses enthalten sein sollen. Höhere Werte bieten mehr Kontext, erhöhen aber den Token-Verbrauch."}, "rooignore": {"label": ".r<PERSON><PERSON><PERSON>-<PERSON><PERSON> in Listen und Suchen anzeigen", "description": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON>, werden <PERSON>, die mit Mustern in .roo<PERSON><PERSON>, in Listen mit einem Schlosssymbol angezeigt. <PERSON><PERSON> de<PERSON>, werden diese Dateien vollständig aus Dateilisten und Suchen ausgeblendet."}, "maxConcurrentFileReads": {"label": "Concurrent file reads limit", "description": "Maximum number of files the 'read_file' tool can process concurrently. Higher values may speed up reading multiple small files but increase memory usage."}, "maxReadFile": {"label": "Schwellenwert für automatische Dateilesekürzung", "description": "Roo liest diese <PERSON><PERSON><PERSON>, wenn das Modell keine Start-/Endwerte angibt. Wenn diese Zahl kleiner als die Gesamtzahl der Zeilen ist, erstellt Roo einen Zeilennummernindex der Codedefinitionen. Spezialfälle: -1 weist Roo an, die gesamte Datei zu lesen (ohne Indexierung), und 0 weist an, keine Zeilen zu lesen und nur Zeilenindizes für minimalen Kontext bereitzustellen. Niedrigere Werte minimieren die anfängliche Kontextnutzung und ermöglichen präzise nachfolgende Zeilenbereich-Lesungen. Explizite Start-/End-Anfragen sind von dieser Einstellung nicht begrenzt.", "lines": "<PERSON><PERSON><PERSON>", "always_full_read": "Immer die gesamte Datei lesen"}}, "terminal": {"basic": {"label": "Terminal-Einstellungen: Grundlegend", "description": "Grundlegende Terminal-Einstellungen"}, "advanced": {"label": "Terminal-Einstellungen: Erweitert", "description": "Die folgenden Optionen erfordern möglicherweise einen Terminal-Neustart, um die Einstellung zu übernehmen."}, "outputLineLimit": {"label": "Terminal-Ausgabelimit", "description": "Maximale Anzahl von <PERSON>, die in der Terminal-Ausgabe bei der Ausführung von Befehlen enthalten sein sollen. Bei Überschreitung werden Zeilen aus der Mitte entfernt, wodurch Token gespart werden. <0><PERSON>hr erfahren</0>"}, "shellIntegrationTimeout": {"label": "Terminal-Shell-Integrationszeit-Limit", "description": "Maximale Wartezeit für die Shell-Integration, bevor <PERSON>hle ausgeführt werden. <PERSON><PERSON><PERSON> mit langen Shell-Startzeiten musst du diesen Wert möglicherweise erhöhen, wenn du Fehler vom Typ \"Shell Integration Unavailable\" im Terminal siehst. <0>Mehr erfahren</0>"}, "shellIntegrationDisabled": {"label": "Terminal-Shell-Integration deaktivieren", "description": "Aktivier<PERSON> dies, wenn Terminalbefehle nicht korrekt funktionieren oder du Fehler wie 'Shell Integration Unavailable' siehst. Dies verwendet eine einfachere Methode zur Ausführung von Befehlen und umgeht einige erweiterte Terminalfunktionen. <0>Mehr erfahren</0>"}, "commandDelay": {"label": "Terminal-Befehlsverzögerung", "description": "Verzögerung in Millisekunden, die nach der Befehlsausführung hinzugefügt wird. Die Standardeinstellung von 0 deaktiviert die Verzögerung vollständig. Dies kann dazu beitragen, dass die Befehlsausgabe in Terminals mit Timing-Problemen vollständig erfasst wird. In den meisten Terminals wird dies durch Setzen von `PROMPT_COMMAND='sleep N'` implementiert, und Powershell fügt `start-sleep` am Ende jedes Befehls hinzu. Ursprünglich war dies eine Lösung für VSCode-Bug#237208 und ist möglicherweise nicht mehr erforderlich. <0>Mehr erfahren</0>"}, "compressProgressBar": {"label": "Fortschrittsbalken-Ausgabe komprimieren", "description": "<PERSON><PERSON> akt<PERSON><PERSON><PERSON>, verarbeitet diese Option Terminal-Ausgaben mit Wagenrücklaufzeichen (\\r), um zu simulieren, wie ein echtes Terminal Inhalte anzeigen würde. Dies entfernt Zwischenzustände von Fortschrittsbalken und behält nur den Endzustand bei, wodurch Kontextraum für relevantere Informationen gespart wird. <0><PERSON>hr erfahren</0>"}, "powershellCounter": {"label": "PowerShell-Zähler-Workaround aktivieren", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, fügt e<PERSON>hler zu PowerShell-Be<PERSON>hl<PERSON> hinzu, um die korrekte Befehlsausführung sicherzustellen. Dies hilft bei PowerShell-Terminals, die Probleme mit der Ausgabeerfassung haben könnten. <0><PERSON>hr erfahren</0>"}, "zshClearEolMark": {"label": "ZSH-Zeilenende-Markierung löschen", "description": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON>, wird die ZSH-Zeilenende-Markierung durch Setzen von PROMPT_EOL_MARK='' gelöscht. Dies verhindert Probleme bei der Interpretation der Befehlsausgabe, wenn diese mit Sonderzeichen wie '%' endet. <0><PERSON>hr erfahren</0>"}, "zshOhMy": {"label": "Oh My Zsh-Integration aktivieren", "description": "<PERSON><PERSON> aktiv<PERSON><PERSON>, wird ITERM_SHELL_INTEGRATION_INSTALLED=Yes gesetzt, um die Shell-Integrationsfunktionen von Oh My Zsh zu aktivieren. Das Anwenden dieser Einstellung erfordert möglicherweise einen Neustart der IDE. <0><PERSON><PERSON> erfahren</0>"}, "zshP10k": {"label": "Powerlevel10k-Integration aktivieren", "description": "<PERSON><PERSON> akt<PERSON><PERSON><PERSON>, wird POWERLEVEL9K_TERM_SHELL_INTEGRATION=true gesetzt, um die Shell-Integrationsfunktionen von Powerlevel10k zu aktivieren. <0><PERSON>hr erfahren</0>"}, "zdotdir": {"label": "ZDOTDIR-Behandlung aktivieren", "description": "Erstellt bei Aktivierung ein temporäres Verzeichnis für ZDOTDIR, um die zsh-Shell-Integration korrekt zu handhaben. Dies stellt sicher, dass die VSCode-Shell-Integration mit zsh funktioniert und dabei deine zsh-Konfiguration erhalten bleibt. <0>Mehr erfahren</0>"}, "inheritEnv": {"label": "Umgebungsvariablen übernehmen", "description": "<PERSON><PERSON> akt<PERSON><PERSON><PERSON>, übernimmt das Terminal Umgebungsvariablen vom übergeordneten VSCode-Prozess, wie z.B. in Benutzerprofilen definierte Shell-Integrationseinstellungen. Dies schaltet direkt die globale VSCode-Einstellung `terminal.integrated.inheritEnv` um. <0>Mehr erfahren</0>"}}, "advanced": {"diff": {"label": "Bearbeitung durch Diffs aktivieren", "description": "<PERSON><PERSON> a<PERSON><PERSON><PERSON>, kann <PERSON>oo <PERSON>ien schneller bearbeiten und lehnt automatisch gekürzte vollständige Dateischreibvorgänge ab. Funktioniert am besten mit dem neuesten Claude 3.7 Sonnet-Modell.", "strategy": {"label": "Diff-Strategie", "options": {"standard": "Standard (Einzelner Block)", "multiBlock": "Experimentell: Multi-Block-Diff", "unified": "Experimentell: <PERSON>erein<PERSON><PERSON><PERSON><PERSON>"}, "descriptions": {"standard": "Die Standard-Diff-Strategie wendet Änderungen auf einen einzelnen Codeblock gleichzeitig an.", "unified": "Die vereinheitlichte Diff-Strategie verwendet mehrere Ansätze zum Anwenden von <PERSON> und wählt den besten Ansatz aus.", "multiBlock": "Die Multi-Block-Diff-Strategie ermöglicht die Aktualisierung mehrerer Codeblöcke in einer Datei in einer Anfrage."}}, "matchPrecision": {"label": "Übereinstimmungsgenauigkeit", "description": "<PERSON><PERSON> ste<PERSON>, wie genau Codeabschnitte beim Anwenden von Diffs übereinstimmen müssen. Niedrigere Werte ermöglichen flexiblere Übereinstimmungen, erhöhen aber das Risiko falscher Ersetzungen. Verwende Werte unter 100% mit äußerster Vorsicht."}}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Experimentelle einheitliche Diff-Strategie verwenden", "description": "Aktiviert die experimentelle einheitliche Diff-Strategie. Diese Strategie könnte die Anzahl der durch Modellfehler verursachten Wiederholungen reduzieren, kann aber unerwartetes Verhalten oder falsche Bearbeitungen verursachen. Nur aktivieren, wenn du die Risiken verstehst und bereit bist, alle Änderungen sorgfältig zu überprüfen."}, "SEARCH_AND_REPLACE": {"name": "Experimentelles Such- und Ersetzungswerkzeug verwenden", "description": "Aktiviert das experimentelle Such- und Ersetzungswerkzeug, das Roo ermöglicht, mehrere Instanzen eines Suchbegriffs in einer Anfrage zu ersetzen."}, "INSERT_BLOCK": {"name": "Experimentelles Inhalts-Einfüge-Werkzeug verwenden", "description": "Aktiviert das experimentelle Inhalts-Einfüge-Werkzeug, das Roo ermöglicht, Inhalte an bestimmten Zeilennummern einzufügen, ohne einen Diff erstellen zu müssen."}, "POWER_STEERING": {"name": "Experimentellen \"Servolenkung\"-Modus verwenden", "description": "<PERSON><PERSON> akt<PERSON><PERSON><PERSON>, wird <PERSON><PERSON> das Modell häufiger an die Details seiner aktuellen Modusdefinition erinnern. Dies führt zu einer stärkeren Einhaltung von Rollendefinitionen und benutzerdefinierten Anweisungen, verwendet aber mehr Tokens pro Nachricht."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Experimentelles Multi-Block-Diff-Werkzeug verwenden", "description": "<PERSON><PERSON> a<PERSON><PERSON>, verwen<PERSON> Roo das Multi-Block-Diff-Werkzeug. <PERSON>s versucht, mehrere Codeblöcke in der Datei in einer Anfrage zu aktualisieren."}, "CONCURRENT_FILE_READS": {"name": "Gleichzeitiges Lesen von Dateien aktivieren", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, kann <PERSON>oo mehrere Dateien in einer einzigen Anfrage lesen (bis zu 15 Dateien). <PERSON><PERSON>, muss Roo Dateien nacheinander lesen. Das Deaktivieren kann helfen, wenn Sie mit weniger leistungsfähigen Modellen arbeiten oder mehr Kontrolle über den Dateizugriff wünschen."}}, "promptCaching": {"label": "Prompt-Caching deaktivieren", "description": "<PERSON><PERSON>, wird <PERSON>oo für dieses Modell kein Prompt-Caching verwenden."}, "temperature": {"useCustom": "Benutzerdefinierte Temperatur verwenden", "description": "Steuert die Zufälligkeit in den Antworten des Modells.", "rangeDescription": "<PERSON><PERSON><PERSON> Werte machen die Ausgabe zufälliger, niedrigere Werte machen sie deterministischer."}, "modelInfo": {"supportsImages": "Unterstützt Bilder", "noImages": "Unterstützt keine Bilder", "supportsComputerUse": "Unterstützt Computer-Nutzung", "noComputerUse": "Unterstützt keine Computer-Nutzung", "supportsPromptCache": "Unterstützt Prompt-Caching", "noPromptCache": "Unterstützt kein Prompt-Caching", "maxOutput": "Maximale Ausgabe", "inputPrice": "Eingabepreis", "outputPrice": "Ausgabepreis", "cacheReadsPrice": "<PERSON>ache<PERSON><PERSON>", "cacheWritesPrice": "Cache-Schreibpreis", "enableStreaming": "Streaming aktivieren", "enableR1Format": "R1-Modellparameter aktivieren", "enableR1FormatTips": "Muss aktiviert werden, wenn R1-<PERSON>le wie QWQ verwendet werden, um 400-<PERSON><PERSON> zu vermeiden", "useAzure": "Azure verwenden", "azureApiVersion": "Azure API-Version festlegen", "gemini": {"freeRequests": "* Kostenlos bis zu {{count}} Anfragen pro Minute. Danach hängt die Abrechnung von der Prompt-Größe ab.", "pricingDetails": "Weitere Informationen finden Sie in den Preisdetails.", "billingEstimate": "* Die Abrechnung ist eine Schätzung - die genauen Kosten hängen von der Prompt-Größe ab."}}, "modelPicker": {"automaticFetch": "Die Erweiterung ruft automatisch die neueste Liste der verfügbaren Modelle von <serviceLink>{{serviceName}}</serviceLink> ab. Wenn du dir nicht sicher bist, welches Modell du wählen sollst, funktioniert Roo Code am besten mit <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Du kannst auch nach \"free\" suchen, um derzeit verfügbare kostenlose Optionen zu finden.", "label": "<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON>", "noMatchFound": "<PERSON><PERSON>einstimmung gefunden", "useCustomModel": "Benutzerdefiniert verwenden: {{modelId}}"}, "footer": {"feedback": "<PERSON><PERSON> du Fragen oder Fe<PERSON>back hast, kannst du gerne ein Issue auf <githubLink>github.com/RooCodeInc/Roo-Code</githubLink> öffnen oder <redditLink>reddit.com/r/RooCode</redditLink> oder <discordLink>discord.gg/roocode</discordLink> beitreten", "telemetry": {"label": "Anonyme Fehler- und Nutzungsberichte zulassen", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON> zu verbessern, indem Sie anonyme Nutzungsdaten und Fehlerberichte senden. Es werden niemals Code, Prompts oder persönliche Informationen gesendet. Weitere Details finden Sie in unserer Datenschutzrichtlinie."}, "settings": {"import": "Importieren", "export": "Exportieren", "reset": "Z<PERSON>ücksetzen"}}, "thinkingBudget": {"maxTokens": "Maximale Tokens", "maxThinkingTokens": "Maximale Thinking-Tokens"}, "validation": {"apiKey": "Du musst einen gültigen API-Schlüssel angeben.", "awsRegion": "Du musst eine Region für Amazon Bedrock auswählen.", "googleCloud": "Du musst eine gültige Google Cloud Projekt-ID und Region angeben.", "modelId": "Du musst eine gültige Modell-ID angeben.", "modelSelector": "Du musst einen gültigen Modell-Selektor angeben.", "openAi": "Du musst eine gültige Basis-URL, API-Schlüssel und Modell-ID angeben.", "arn": {"invalidFormat": "Ungültiges ARN-Format. Bitte überprüfen Sie die Formatanforderungen.", "regionMismatch": "Warnung: Die Region in deiner ARN ({{arnRegion}}) stimmt nicht mit deiner ausgewählten Region ({{region}}) überein. Dies kann zu Zugriffsproblemen führen. Der Provider wird die Region aus der ARN verwenden."}, "modelAvailability": "Die von dir angegebene Modell-ID ({{modelId}}) ist nicht verfügbar. Bitte wähle ein anderes Modell.", "providerNotAllowed": "Anbieter '{{provider}}' ist von deiner Organisation nicht erlaubt", "modelNotAllowed": "Modell '{{model}}' ist für Anbieter '{{provider}}' von deiner Organisation nicht erlaubt", "profileInvalid": "Dieses Profil enthält einen Anbieter oder ein Modell, das von deiner Organisation nicht erlaubt ist"}, "placeholders": {"apiKey": "API-Schlüssel eingeben...", "profileName": "Profilnamen e<PERSON>ben", "accessKey": "Zugriffsschlüssel eingeben...", "secretKey": "Geheimschlüssel eingeben...", "sessionToken": "Sitzungstoken eingeben...", "credentialsJson": "Anmeldedaten-JSON eingeben...", "keyFilePath": "Schlüsseldateipfad eingeben...", "projectId": "Projekt-ID eingeben...", "customArn": "ARN eingeben (z.B. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Basis-URL eingeben...", "modelId": {"lmStudio": "z.B. meta-llama-3.1-8b-instruct", "lmStudioDraft": "z.B. lmstudio-community/llama-3.2-1b-instruct", "ollama": "z.B. llama3.1"}, "numbers": {"maxTokens": "z.B. 4096", "contextWindow": "z.B. 128000", "inputPrice": "z.B. 0.0001", "outputPrice": "z.B. 0.0002", "cacheWritePrice": "z.B. 0.00005"}}, "defaults": {"ollamaUrl": "Standard: http://localhost:11434", "lmStudioUrl": "Standard: http://localhost:1234", "geminiUrl": "Standard: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Benutzerdefinierte ARN", "useCustomArn": "Benutzerdefinierte ARN verwenden..."}}