{"extension.displayName": "qapt coder", "extension.description": "在你的編輯器中提供完整的 AI 代理開發團隊。", "command.newTask.title": "新建任務", "command.explainCode.title": "解釋程式碼", "command.fixCode.title": "修復程式碼", "command.improveCode.title": "改進程式碼", "command.addToContext.title": "新增到上下文", "command.openInNewTab.title": "在新分頁中開啟", "command.focusInput.title": "聚焦輸入框", "command.setCustomStoragePath.title": "設定自訂儲存路徑", "command.terminal.addToContext.title": "將終端內容新增到上下文", "command.terminal.fixCommand.title": "修復此命令", "command.terminal.explainCommand.title": "解釋此命令", "command.acceptInput.title": "接受輸入/建議", "views.activitybar.title": "qapt coder", "views.contextMenu.label": "qapt coder", "views.terminalMenu.label": "qapt coder", "views.sidebar.name": "qapt coder", "command.mcpServers.title": "MCP 伺服器", "command.prompts.title": "模式", "command.history.title": "歷史記錄", "command.openInEditor.title": "在編輯器中開啟", "command.settings.title": "設定", "command.documentation.title": "文件", "configuration.title": "qapt coder", "commands.allowedCommands.description": "當啟用'始終批准執行操作'時可以自動執行的命令", "settings.vsCodeLmModelSelector.description": "VSCode 語言模型 API 的設定", "settings.vsCodeLmModelSelector.vendor.description": "語言模型供應商（例如：copilot）", "settings.vsCodeLmModelSelector.family.description": "語言模型系列（例如：gpt-4）", "settings.customStoragePath.description": "自訂儲存路徑。留空以使用預設位置。支援絕對路徑（例如：'D:\\qaptCoderStorage'）", "settings.qaptCoderCloudEnabled.description": "啟用 qapt coder Cloud。"}