{"greeting": "<PERSON><PERSON><PERSON>, eu sou o Roo!", "introduction": "<strong>Roo Code é o principal agente de codificação autônomo.</strong> Prepare-se para arquitetar, codificar, depurar e aumentar sua produtividade como nunca antes. Para continuar, o Roo Code necessita de uma chave API.", "notice": "Para começar, esta extensão precisa de um provedor de API.", "start": "Vamos lá!", "chooseProvider": "Escolha um provedor de API para começar:", "routers": {"requesty": {"description": "Seu roteador LLM otimizado", "incentive": "$1 de crédit<PERSON> g<PERSON>"}, "openrouter": {"description": "Uma interface unificada para LLMs"}}, "startRouter": "Configuração rápida através de um roteador", "startCustom": "Use sua própria chave API", "telemetry": {"title": "Ajude a melhorar o Roo Code", "anonymousTelemetry": "Envie dados de uso e erros anônimos para nos ajudar a corrigir bugs e melhorar a extensão. Nenhum código, texto ou informação pessoal é enviado.", "changeSettings": "Você sempre pode mudar isso na parte inferior das <settingsLink>configurações</settingsLink>", "settings": "configuraç<PERSON><PERSON>", "allow": "<PERSON><PERSON><PERSON>", "deny": "<PERSON><PERSON><PERSON>"}, "or": "ou", "importSettings": "Importar configurações"}