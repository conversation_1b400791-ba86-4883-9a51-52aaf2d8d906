{"extension.displayName": "qapt coder", "extension.description": "Un equipo completo de desarrollo de agentes de IA en tu editor.", "command.newTask.title": "Nueva Tarea", "command.explainCode.title": "Explicar Código", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "<PERSON><PERSON><PERSON> Contexto", "command.openInNewTab.title": "Abrir en Nueva Pestaña", "command.focusInput.title": "Enfocar Campo de Entrada", "command.setCustomStoragePath.title": "Establecer <PERSON> de Almacenamiento Personalizada", "command.terminal.addToContext.title": "Añadir Contenido de Terminal al Contexto", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar Este Comando", "command.acceptInput.title": "Aceptar Entrada/Sugerencia", "views.activitybar.title": "qapt coder", "views.contextMenu.label": "qapt coder", "views.terminalMenu.label": "qapt coder", "views.sidebar.name": "qapt coder", "command.mcpServers.title": "Servidores MCP", "command.prompts.title": "Modos", "command.history.title": "Historial", "command.openInEditor.title": "Abrir en Editor", "command.settings.title": "Configuración", "command.documentation.title": "Documentación", "configuration.title": "qapt coder", "commands.allowedCommands.description": "Comandos que pueden ejecutarse automáticamente cuando 'Aprobar siempre operaciones de ejecución' está activado", "settings.vsCodeLmModelSelector.description": "Configuración para la API del modelo de lenguaje VSCode", "settings.vsCodeLmModelSelector.vendor.description": "El proveedor del modelo de lenguaje (ej. copilot)", "settings.vsCodeLmModelSelector.family.description": "La familia del modelo de lenguaje (ej. gpt-4)", "settings.customStoragePath.description": "Ruta de almacenamiento personalizada. Dejar vacío para usar la ubicación predeterminada. Admite rutas absolutas (ej. 'D:\\qaptCoderStorage')", "settings.qaptCoderCloudEnabled.description": "Habilitar qapt coder Cloud."}