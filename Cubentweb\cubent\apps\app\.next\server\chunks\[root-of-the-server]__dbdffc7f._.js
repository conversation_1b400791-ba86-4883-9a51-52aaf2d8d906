module.exports = {

"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/packages/auth/server.ts [app-route] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "auth": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["auth"]),
    "buildClerkProps": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildClerkProps"]),
    "clerkClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clerkClient"]),
    "clerkMiddleware": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clerkMiddleware"]),
    "createClerkClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClerkClient"]),
    "createRouteMatcher": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createRouteMatcher"]),
    "currentUser": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["currentUser"]),
    "getAuth": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAuth"]),
    "reverificationError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["reverificationError"]),
    "reverificationErrorResponse": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["reverificationErrorResponse"]),
    "verifyToken": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyToken"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$clerk$2b$nextjs$40$6$2e$20$2e$0_next$40$1_c77f473bcb010f4557dab79e25c1da72$2f$node_modules$2f40$clerk$2f$nextjs$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@clerk+nextjs@6.20.0_next@1_c77f473bcb010f4557dab79e25c1da72/node_modules/@clerk/nextjs/dist/esm/server/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/packages/auth/server.ts [app-route] (ecmascript) <locals>");
}}),
"[project]/packages/auth/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "auth": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["auth"]),
    "buildClerkProps": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["buildClerkProps"]),
    "clerkClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["clerkClient"]),
    "clerkMiddleware": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["clerkMiddleware"]),
    "createClerkClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createClerkClient"]),
    "createRouteMatcher": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createRouteMatcher"]),
    "currentUser": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["currentUser"]),
    "getAuth": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getAuth"]),
    "reverificationError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["reverificationError"]),
    "reverificationErrorResponse": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["reverificationErrorResponse"]),
    "verifyToken": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__["verifyToken"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/packages/auth/server.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$packages$2f$auth$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/packages/auth/server.ts [app-route] (ecmascript) <exports>");
}}),

};