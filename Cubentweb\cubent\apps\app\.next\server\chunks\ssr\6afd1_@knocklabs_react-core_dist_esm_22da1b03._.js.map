{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "de.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/i18n/languages/de.ts"], "sourcesContent": ["import { I18nContent } from \".\";\n\nconst de: I18nContent = {\n  translations: {\n    archiveNotification: \"Benachrichtigung archivieren\",\n    archiveRead: \"Gelesenes Archivieren\",\n    markAllAsRead: \"Alle als gelesen markieren\",\n    notifications: \"Benachrichtigungen\",\n    emptyFeedTitle: \"Noch keine Benachrichtigungen\",\n    emptyFeedBody:\n      \"Wir werden dich benachrichtigen, sobald wir etwas Neues für dich haben.\",\n    all: \"Alle\",\n    unread: \"Ungelesen\",\n    read: \"<PERSON><PERSON><PERSON>\",\n    unseen: \"Ungesehen\",\n    slackConnectChannel: \"Kanal verbinden\",\n    slackChannelId: \"Slack Kanal ID\",\n    slackConnecting: \"Verbinden mit Slack...\",\n    slackDisconnecting: \"Trennen der Verbindung...\",\n    slackConnect: \"Mit Slack verbinden\",\n    slackConnected: \"Verbunden\",\n    slackConnectContainerDescription:\n      \"Verbinden, um Benachrichtigungen in Ihrem Slack-Arbeitsberei<PERSON> zu erhalten.\",\n    slackSearchbarDisconnected: \"Slack ist nicht verbunden.\",\n    slackSearchbarNoChannelsConnected: \"Suchkanäle\",\n    slackSearchbarNoChannelsFound: \"Keine schlaffen Kanäle.\",\n    slackSearchbarChannelsError: \"Fehler beim Abrufen von Kanälen.\",\n    slackSearchChannels: \"Suchkanäle\",\n    slackConnectionErrorExists:\n      \"Versuchen Sie, sich erneut mit Slack zu verbinden, um Kanäle in Ihrem Arbeitsbereich zu finden und auszuwählen.\",\n    slackConnectionErrorOccurred:\n      \"Es ist ein Fehler beim Verbinden mit Slack aufgetreten. Versuchen Sie, sich erneut zu verbinden, um Kanäle in Ihrem Arbeitsbereich zu finden und auszuwählen.\",\n    slackChannelAlreadyConnected: \"Fehler: bereits verbunden\",\n    slackError: \"Fehler\",\n    slackDisconnect: \"Trennen Sie die Verbindung.\",\n    slackChannelSetError: \"Fehlereinstellung Kanal.\",\n    slackAccessTokenNotSet: \"Zugriffstoken nicht gesetzt.\",\n    slackReconnect: \"Neu verbinden\",\n  },\n  locale: \"de\",\n};\n\nexport default de;\n"], "names": ["de", "translations", "archiveNotification", "archiveRead", "markAllAsRead", "notifications", "emptyFeedTitle", "emptyFeedBody", "all", "unread", "read", "unseen", "slackConnectChannel", "slackChannelId", "slackConnecting", "slackDisconnecting", "slackConnect", "slackConnected", "slackConnectContainerDescription", "slackSearchbarDisconnected", "slackSearchbarNoChannelsConnected", "slackSearchbarNoChannelsFound", "slackSearchbarChannelsError", "slackSearchChannels", "slackConnectionErrorExists", "slackConnectionErrorOccurred", "slackChannelAlreadyConnected", "slackError", "slackDisconnect", "slackChannelSetError", "slackAccessTokenNotSet", "slackReconnect", "locale"], "mappings": ";;;AAEA,MAAMA,IAAkB;IACtBC,cAAc;QACZC,qBAAqB;QACrBC,aAAa;QACbC,eAAe;QACfC,eAAe;QACfC,gBAAgB;QAChBC,eACE;QACFC,KAAK;QACLC,QAAQ;QACRC,MAAM;QACNC,QAAQ;QACRC,qBAAqB;QACrBC,gBAAgB;QAChBC,iBAAiB;QACjBC,oBAAoB;QACpBC,cAAc;QACdC,gBAAgB;QAChBC,kCACE;QACFC,4BAA4B;QAC5BC,mCAAmC;QACnCC,+BAA+B;QAC/BC,6BAA6B;QAC7BC,qBAAqB;QACrBC,4BACE;QACFC,8BACE;QACFC,8BAA8B;QAC9BC,YAAY;QACZC,iBAAiB;QACjBC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;IAClB;IACAC,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "file": "en.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/i18n/languages/en.ts"], "sourcesContent": ["import { I18nContent } from \".\";\n\nconst en: I18nContent = {\n  translations: {\n    archiveNotification: \"Archive this notification\",\n    archiveRead: \"Archive Read\",\n    markAllAsRead: \"Mark all as read\",\n    notifications: \"Notifications\",\n    emptyFeedTitle: \"No notifications yet\",\n    emptyFeedBody: \"We'll let you know when we've got something new for you.\",\n    all: \"All\",\n    unread: \"Unread\",\n    read: \"Read\",\n    unseen: \"Unseen\",\n    msTeamsChannelSetError: \"Error setting channel.\",\n    msTeamsConnect: \"Connect to Microsoft Teams\",\n    msTeamsConnected: \"Connected\",\n    msTeamsConnecting: \"Connecting to Microsoft Teams…\",\n    msTeamsConnectionErrorExists:\n      \"Try reconnecting to Microsoft Teams to find and select channels from your teams.\",\n    msTeamsConnectionErrorOccurred:\n      \"There was an error connecting to Microsoft Teams. Try reconnecting to find and select channels from your teams.\",\n    msTeamsConnectContainerDescription:\n      \"Connect to get notifications in Microsoft Teams\",\n    msTeamsDisconnect: \"Disconnect\",\n    msTeamsDisconnecting: \"Disconnecting from Microsoft Teams…\",\n    msTeamsError: \"Error\",\n    msTeamsReconnect: \"Reconnect\",\n    msTeamsTenantIdNotSet: \"Microsoft Teams tenant ID not set.\",\n    slackConnectChannel: \"Connect channel\",\n    slackChannelId: \"Slack channel ID\",\n    slackConnecting: \"Connecting to Slack...\",\n    slackDisconnecting: \"Disconnecting...\",\n    slackConnect: \"Connect to Slack\",\n    slackConnected: \"Connected\",\n    slackConnectContainerDescription:\n      \"Connect to get notifications in your Slack workspace.\",\n    slackSearchbarDisconnected: \"Slack is not connected.\",\n    slackSearchbarNoChannelsConnected: \"Search channels\",\n    slackSearchbarNoChannelsFound: \"No slack channels.\",\n    slackSearchbarChannelsError: \"Error fetching channels.\",\n    slackSearchChannels: \"Search channels\",\n    slackConnectionErrorExists:\n      \"Try reconnecting to Slack to find and select channels from your workspace.\",\n    slackConnectionErrorOccurred:\n      \"There was an error connecting to Slack. Try reconnecting to find and select channels from your workspace.\",\n    slackChannelAlreadyConnected: \"Error: already connected\",\n    slackError: \"Error\",\n    slackDisconnect: \"Disconnect\",\n    slackChannelSetError: \"Error setting channel.\",\n    slackAccessTokenNotSet: \"Access token not set.\",\n    slackReconnect: \"Reconnect\",\n  },\n  locale: \"en\",\n};\n\nexport default en;\n"], "names": ["en", "translations", "archiveNotification", "archiveRead", "markAllAsRead", "notifications", "emptyFeedTitle", "emptyFeedBody", "all", "unread", "read", "unseen", "msTeamsChannelSetError", "msTeamsConnect", "msTeamsConnected", "msTeamsConnecting", "msTeamsConnectionErrorExists", "msTeamsConnectionErrorOccurred", "msTeamsConnectContainerDescription", "msTeamsDisconnect", "msTeamsDisconnecting", "msTeamsError", "msTeamsReconnect", "msTeamsTenantIdNotSet", "slackConnectChannel", "slackChannelId", "slackConnecting", "slackDisconnecting", "slackConnect", "slackConnected", "slackConnectContainerDescription", "slackSearchbarDisconnected", "slackSearchbarNoChannelsConnected", "slackSearchbarNoChannelsFound", "slackSearchbarChannelsError", "slackSearchChannels", "slackConnectionErrorExists", "slackConnectionErrorOccurred", "slackChannelAlreadyConnected", "slackError", "slackDisconnect", "slackChannelSetError", "slackAccessTokenNotSet", "slackReconnect", "locale"], "mappings": ";;;AAEA,MAAMA,IAAkB;IACtBC,cAAc;QACZC,qBAAqB;QACrBC,aAAa;QACbC,eAAe;QACfC,eAAe;QACfC,gBAAgB;QAChBC,eAAe;QACfC,KAAK;QACLC,QAAQ;QACRC,MAAM;QACNC,QAAQ;QACRC,wBAAwB;QACxBC,gBAAgB;QAChBC,kBAAkB;QAClBC,mBAAmB;QACnBC,8BACE;QACFC,gCACE;QACFC,oCACE;QACFC,mBAAmB;QACnBC,sBAAsB;QACtBC,cAAc;QACdC,kBAAkB;QAClBC,uBAAuB;QACvBC,qBAAqB;QACrBC,gBAAgB;QAChBC,iBAAiB;QACjBC,oBAAoB;QACpBC,cAAc;QACdC,gBAAgB;QAChBC,kCACE;QACFC,4BAA4B;QAC5BC,mCAAmC;QACnCC,+BAA+B;QAC/BC,6BAA6B;QAC7BC,qBAAqB;QACrBC,4BACE;QACFC,8BACE;QACFC,8BAA8B;QAC9BC,YAAY;QACZC,iBAAiB;QACjBC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;IAClB;IACAC,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/i18n/languages/index.ts"], "sourcesContent": ["import de from \"./de\";\nimport en from \"./en\";\n\nexport interface Translations {\n  readonly archiveRead: string;\n  readonly emptyFeedTitle: string;\n  readonly emptyFeedBody: string;\n  readonly notifications: string;\n  readonly poweredBy: string;\n  readonly markAllAsRead: string;\n  readonly archiveNotification: string;\n  readonly all: string;\n  readonly unread: string;\n  readonly read: string;\n  readonly unseen: string;\n  readonly msTeamsChannelSetError: string;\n  readonly msTeamsConnect: string;\n  readonly msTeamsConnected: string;\n  readonly msTeamsConnecting: string;\n  readonly msTeamsConnectionErrorExists: string;\n  readonly msTeamsConnectionErrorOccurred: string;\n  readonly msTeamsConnectContainerDescription: string;\n  readonly msTeamsDisconnect: string;\n  readonly msTeamsDisconnecting: string;\n  readonly msTeamsError: string;\n  readonly msTeamsReconnect: string;\n  readonly msTeamsTenantIdNotSet: string;\n  readonly slackConnectChannel: string;\n  readonly slackChannelId: string;\n  readonly slackConnecting: string;\n  readonly slackDisconnecting: string;\n  readonly slackConnect: string;\n  readonly slackConnected: string;\n  readonly slackConnectContainerDescription: string;\n  readonly slackSearchbarDisconnected: string;\n  readonly slackSearchbarNoChannelsConnected: string;\n  readonly slackSearchbarNoChannelsFound: string;\n  readonly slackSearchbarChannelsError: string;\n  readonly slackSearchChannels: string;\n  readonly slackConnectionErrorOccurred: string;\n  readonly slackConnectionErrorExists: string;\n  readonly slackChannelAlreadyConnected: string;\n  readonly slackError: string;\n  readonly slackDisconnect: string;\n  readonly slackChannelSetError: string;\n  readonly slackAccessTokenNotSet: string;\n  readonly slackReconnect: string;\n}\n\nexport interface I18nContent {\n  readonly translations: Partial<Translations>;\n  readonly locale: string;\n}\n\nexport const locales = { en, de };\n"], "names": ["locales", "en", "de"], "mappings": ";;;;;;;AAsDO,MAAMA,IAAU;IAAEC,iTAAAA,UAAAA;IAAIC,iTAAAA,UAAAA;AAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "file": "KnockI18nProvider.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/i18n/context/KnockI18nProvider.tsx"], "sourcesContent": ["import React from \"react\";\nimport { FunctionComponent, PropsWithChildren } from \"react\";\n\nimport { I18nContent, locales } from \"../languages\";\n\nexport const I18nContext = React.createContext<I18nContent>(locales.en);\n\nexport interface KnockI18nProviderProps {\n  i18n?: I18nContent;\n}\n\nexport const KnockI18nProvider: FunctionComponent<\n  PropsWithChildren<KnockI18nProviderProps>\n> = ({ i18n = locales.en, ...props }) => {\n  return <I18nContext.Provider {...props} value={i18n} />;\n};\n"], "names": ["I18nContext", "React", "createContext", "locales", "en", "KnockI18nProvider", "i18n", "props"], "mappings": ";;;;;;;;AAKO,MAAMA,yTAAcC,UAAAA,CAAMC,aAAAA,iTAA2BC,UAAAA,CAAQC,EAAE,GAMzDC,IAETA,CAAC,EAAEC,MAAAA,oTAAOH,UAAAA,CAAQC,EAAAA,EAAI,GAAGG,GAAM,GAAA,aAAA,GAAA,oTAAA,CAAA,UAAA,CAAA,aAAA,CACzBP,EAAY,QAAA,EAAZ;QAAyBO,GAAAA,CAAAA;QAAO,OAAOD;IAAAA,CAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "file": "useStableOptions.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/core/hooks/useStableOptions.ts"], "sourcesContent": ["import fastDeepEqual from \"fast-deep-equal\";\nimport { useMemo, useRef } from \"react\";\n\nexport default function useStableOptions<T>(options: T): T {\n  const optionsRef = useRef<T>();\n\n  return useMemo(() => {\n    const currentOptions = optionsRef.current;\n\n    if (currentOptions && fastDeepEqual(options, currentOptions)) {\n      return currentOptions;\n    }\n\n    optionsRef.current = options;\n    return options;\n  }, [options]);\n}\n"], "names": ["useStableOptions", "options", "optionsRef", "useRef", "useMemo", "currentOptions", "current", "fastDeepEqual"], "mappings": ";;;;;;;AAGA,SAAwBA,EAAoBC,CAAAA,EAAe;IACzD,MAAMC,sUAAaC,CAAU;IAE7B,0UAAOC,EAAQ,MAAM;QACnB,MAAMC,IAAiBH,EAAWI,OAAAA;QAElC,OAAID,oOAAkBE,EAAcN,GAASI,CAAc,IAClDA,IAAAA,CAGTH,EAAWI,OAAAA,GAAUL,GACdA,CAAAA;IAAAA,GACN;QAACA,CAAO;KAAC;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "file": "useAuthenticatedKnockClient.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/core/hooks/useAuthenticatedKnockClient.ts"], "sourcesContent": ["import Knock, { AuthenticateOptions, KnockOptions } from \"@knocklabs/client\";\nimport React from \"react\";\n\nimport { useStableOptions } from \"../../core\";\n\nfunction authenticateWithOptions(\n  knock: Knock,\n  userId: Knock[\"userId\"],\n  userToken?: Knock[\"userToken\"],\n  options: AuthenticateOptions = {},\n) {\n  knock.authenticate(userId, userToken, {\n    onUserTokenExpiring: options?.onUserTokenExpiring,\n    timeBeforeExpirationInMs: options?.timeBeforeExpirationInMs,\n  });\n}\n\nexport type AuthenticatedKnockClientOptions = KnockOptions &\n  AuthenticateOptions;\n\nfunction useAuthenticatedKnockClient(\n  apiKey: string,\n  userId: Knock[\"userId\"],\n  userToken?: Knock[\"userToken\"],\n  options: AuthenticatedKnockClientOptions = {},\n) {\n  const knockRef = React.useRef<Knock | undefined>();\n  const stableOptions = useStableOptions(options);\n\n  return React.useMemo(() => {\n    const currentKnock = knockRef.current;\n\n    // If the userId and the userToken changes then just reauth\n    if (\n      currentKnock &&\n      currentKnock.isAuthenticated() &&\n      (currentKnock.userId !== userId || currentKnock.userToken !== userToken)\n    ) {\n      authenticateWithOptions(currentKnock, userId, userToken, stableOptions);\n      return currentKnock;\n    }\n\n    if (currentKnock) {\n      currentKnock.teardown();\n    }\n\n    // Otherwise instantiate a new Knock client\n    const knock = new Knock(apiKey, {\n      host: stableOptions.host,\n      logLevel: stableOptions.logLevel,\n    });\n\n    authenticateWithOptions(knock, userId, userToken, stableOptions);\n    knockRef.current = knock;\n\n    return knock;\n  }, [apiKey, userId, userToken, stableOptions]);\n}\n\nexport default useAuthenticatedKnockClient;\n"], "names": ["authenticateWithOptions", "knock", "userId", "userToken", "options", "authenticate", "onUserTokenExpiring", "timeBeforeExpirationInMs", "useAuthenticatedKnockClient", "<PERSON><PERSON><PERSON><PERSON>", "knockRef", "React", "useRef", "stableOptions", "useStableOptions", "useMemo", "currentKnock", "current", "isAuthenticated", "teardown", "Knock", "host", "logLevel"], "mappings": ";;;;;;;;;;;;;AAKA,SAASA,EACPC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAC,IAA+B,CAAA,CAAA,EAC/B;IACMC,EAAAA,YAAAA,CAAaH,GAAQC,GAAW;QACpCG,qBAAqBF,KAAAA,OAAAA,KAAAA,IAAAA,EAASE,mBAAAA;QAC9BC,0BAA0BH,KAAAA,OAAAA,KAAAA,IAAAA,EAASG,wBAAAA;IAAAA,CACpC;AACH;AAKA,SAASC,EACPC,CAAAA,EACAP,CAAAA,EACAC,CAAAA,EACAC,IAA2C,CAAA,CAAA,EAC3C;IACMM,MAAAA,yTAAWC,UAAAA,CAAMC,MAAAA,CAA0B,GAC3CC,IAAgBC,qUAAAA,EAAiBV,CAAO;IAEvCO,4TAAAA,UAAAA,CAAMI,OAAAA,CAAQ,MAAM;QACzB,MAAMC,IAAeN,EAASO,OAAAA;QAI5BD,IAAAA,KACAA,EAAaE,eAAAA,CAAgB,KAAA,CAC5BF,EAAad,MAAAA,KAAWA,KAAUc,EAAab,SAAAA,KAAcA,CAAAA,GAEtCa,OAAAA,EAAAA,GAAcd,GAAQC,GAAWU,CAAa,GAC/DG;QAGLA,KACFA,EAAaG,QAAAA,CAAS;QAIlBlB,MAAAA,IAAQ,+RAAImB,UAAAA,CAAMX,GAAQ;YAC9BY,MAAMR,EAAcQ,IAAAA;YACpBC,UAAUT,EAAcS,QAAAA;QAAAA,CACzB;QAEuBrB,OAAAA,EAAAA,GAAOC,GAAQC,GAAWU,CAAa,GAC/DH,EAASO,OAAAA,GAAUhB,GAEZA;IAAAA,GACN;QAACQ;QAAQP;QAAQC;QAAWU,CAAa;KAAC;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "file": "KnockProvider.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/core/context/KnockProvider.tsx"], "sourcesContent": ["import Knock, { AuthenticateOptions, LogLevel } from \"@knocklabs/client\";\nimport * as React from \"react\";\nimport { PropsWithChildren } from \"react\";\n\nimport { I18nContent, KnockI18nProvider } from \"../../i18n\";\nimport { useAuthenticatedKnockClient } from \"../hooks\";\n\nexport interface KnockProviderState {\n  knock: Knock;\n}\n\nconst KnockContext = React.createContext<KnockProviderState | null>(null);\n\nexport interface KnockProviderProps {\n  // Knock client props\n  apiKey: string | undefined;\n  host?: string;\n  // Authentication props\n  userId: Knock[\"userId\"];\n  userToken?: Knock[\"userToken\"];\n  onUserTokenExpiring?: AuthenticateOptions[\"onUserTokenExpiring\"];\n  timeBeforeExpirationInMs?: AuthenticateOptions[\"timeBeforeExpirationInMs\"];\n  // i18n translations\n  i18n?: I18nContent;\n  logLevel?: LogLevel;\n}\n\nexport const KnockProvider: React.FC<PropsWithChildren<KnockProviderProps>> = ({\n  apiKey,\n  host,\n  logLevel,\n  userId,\n  userToken,\n  onUserTokenExpiring,\n  timeBeforeExpirationInMs,\n  children,\n  i18n,\n}) => {\n  // We memoize the options here so that we don't create a new object on every re-render\n  const authenticateOptions = React.useMemo(\n    () => ({\n      host,\n      onUserTokenExpiring,\n      timeBeforeExpirationInMs,\n      logLevel,\n    }),\n    [host, onUserTokenExpiring, timeBeforeExpirationInMs, logLevel],\n  );\n\n  const knock = useAuthenticatedKnockClient(\n    apiKey ?? \"\",\n    userId,\n    userToken,\n    authenticateOptions,\n  );\n\n  return (\n    <KnockContext.Provider value={{ knock }}>\n      <KnockI18nProvider i18n={i18n}>{children}</KnockI18nProvider>\n    </KnockContext.Provider>\n  );\n};\n\nexport const useKnockClient = (): Knock => {\n  const context = React.useContext(KnockContext);\n  if (!context) {\n    throw new Error(\"useKnockClient must be used within a KnockProvider\");\n  }\n  return context.knock;\n};\n"], "names": ["KnockContext", "React", "createContext", "KnockProvider", "<PERSON><PERSON><PERSON><PERSON>", "host", "logLevel", "userId", "userToken", "onUserTokenExpiring", "timeBeforeExpirationInMs", "children", "i18n", "authenticateOptions", "useMemo", "knock", "useAuthenticatedKnockClient", "KnockI18nProvider", "useKnockClient", "context", "useContext", "Error"], "mappings": ";;;;;;;;;;;;AAWA,MAAMA,6TAAeC,EAAMC,cAAAA,EAAyC,IAAI,GAgB3DC,IAAiEA,CAAC,EAC7EC,QAAAA,CAAAA,EACAC,MAAAA,CAAAA,EACAC,UAAAA,CAAAA,EACAC,QAAAA,CAAAA,EACAC,WAAAA,CAAAA,EACAC,qBAAAA,CAAAA,EACAC,0BAAAA,CAAAA,EACAC,UAAAA,CAAAA,EACAC,MAAAA,CAAAA,EACF,KAAM;IAEEC,MAAAA,6TAAsBZ,EAAMa,QAAAA,EAChC,IAAA,CAAO;YACLT,MAAAA;YACAI,qBAAAA;YACAC,0BAAAA;YACAJ,UAAAA;QAAAA,CAAAA,GAEF;QAACD;QAAMI;QAAqBC;QAA0BJ,CAAQ;KAChE,GAEMS,KAAQC,+UAAAA,EACZZ,KAAU,IACVG,GACAC,GACAK,CACF;IAEA,OACG,aAAA,4TAAAZ,EAAA,cAAA,EAAAD,EAAa,QAAA,EAAb;QAAsB,OAAO;YAAEe,OAAAA;QAAAA;IAC9B,GAAA,aAAA,4TAAAd,EAAA,cAAA,2TAACgB,qBAAAA,EAAkB;QAAA,MAAAL;IAAA,GAAaD,CAAS,CAC3C;AAEJ,GAEaO,IAAiBA,MAAa;IACnCC,MAAAA,6TAAUlB,EAAMmB,WAAAA,EAAWpB,CAAY;IAC7C,IAAI,CAACmB,GACG,MAAA,IAAIE,MAAM,oDAAoD;IAEtE,OAAOF,EAAQJ,KAAAA;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "file": "constants.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/core/constants.ts"], "sourcesContent": ["export enum FilterStatus {\n  All = \"all\",\n  Read = \"read\",\n  Unseen = \"unseen\",\n  Unread = \"unread\",\n}\n\nexport type ColorMode = \"light\" | \"dark\";\n"], "names": ["FilterStatus", "All", "Read", "Unseen", "Unread"], "mappings": ";;;AAAYA,IAAAA,IAAAA,aAAAA,GAAAA,CAAAA,CAAAA,IAAAA,CACVC,EAAAA,GAAAA,GAAM,OACNC,EAAAA,IAAAA,GAAO,QACPC,EAAAA,MAAAA,GAAS,UACTC,EAAAA,MAAAA,GAAS,UAJCJ,CAAAA,CAAAA,EAAAA,KAAAA,CAAAA,CAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "file": "utils.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/core/utils.ts"], "sourcesContent": ["import Knock, { FeedClientOptions } from \"@knocklabs/client\";\nimport { intlFormatDistance, parseISO } from \"date-fns\";\nimport { ReactNode } from \"react\";\n\nexport function formatBadgeCount(count: number): string | number {\n  return count > 9 ? \"9+\" : count;\n}\n\ntype FormatTimestampOptions = {\n  locale?: string | string[];\n};\n\nexport function formatTimestamp(\n  ts: string,\n  options: FormatTimestampOptions = {},\n) {\n  try {\n    const parsedTs = parseISO(ts);\n    const formatted = intlFormatDistance(parsedTs, new Date(), {\n      locale: options.locale,\n    });\n\n    return formatted;\n  } catch (_e) {\n    return ts;\n  }\n}\n\nexport function toSentenceCase(string: string): string {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\n\nexport function renderNodeOrFallback(node: ReactNode, fallback: ReactNode) {\n  return node !== undefined ? node : fallback;\n}\n\n/*\n  Used to build a consistent key for the KnockFeedProvider so that <PERSON><PERSON> knows when\n  to trigger a re-render of the context when a key property changes.\n*/\nexport function feedProviderKey(\n  userId: Knock[\"userId\"],\n  feedId: string,\n  options: FeedClientOptions = {},\n) {\n  return [\n    userId,\n    feedId,\n    options.source,\n    options.tenant,\n    options.has_tenant,\n    options.archived,\n  ]\n    .filter((f) => f !== null && f !== undefined)\n    .join(\"-\");\n}\n\n/*\n  Used to build a consistent key for the KnockSlackProvider so that React knows when\n  to trigger a re-render of the context when a key property changes.\n*/\nexport function slackProviderKey({\n  knockSlackChannelId,\n  tenantId,\n  connectionStatus,\n  errorLabel,\n}: {\n  knockSlackChannelId: string;\n  tenantId: string;\n  connectionStatus: string;\n  errorLabel: string | null;\n}) {\n  return [knockSlackChannelId, tenantId, connectionStatus, errorLabel]\n    .filter((f) => f !== null && f !== undefined)\n    .join(\"-\");\n}\n\n/*\n  Used to build a consistent key for the KnockMsTeamsProvider so that React knows when\n  to trigger a re-render of the context when a key property changes.\n*/\nexport function msTeamsProviderKey({\n  knockMsTeamsChannelId,\n  tenantId,\n  connectionStatus,\n  errorLabel,\n}: {\n  knockMsTeamsChannelId: string;\n  tenantId: string;\n  connectionStatus: string;\n  errorLabel: string | null;\n}) {\n  return [knockMsTeamsChannelId, tenantId, connectionStatus, errorLabel]\n    .filter((f) => f !== null && f !== undefined)\n    .join(\"-\");\n}\n"], "names": ["formatBadgeCount", "count", "formatTimestamp", "ts", "options", "parsedTs", "parseISO", "formatted", "intlFormatDistance", "Date", "locale", "toSentenceCase", "string", "char<PERSON>t", "toUpperCase", "slice", "renderNodeOrFallback", "node", "fallback", "undefined", "feedProviderKey", "userId", "feedId", "source", "tenant", "has_tenant", "archived", "filter", "f", "join", "slackProviderKey", "knockSlackChannelId", "tenantId", "connectionStatus", "error<PERSON><PERSON><PERSON>", "msTeamsProviderKey", "knockMsTeamsChannelId"], "mappings": ";;;;;;;;;;;;AAIO,SAASA,EAAiBC,CAAAA,EAAgC;IACxDA,OAAAA,IAAQ,IAAI,OAAOA;AAC5B;AAMO,SAASC,EACdC,CAAAA,EACAC,IAAkC,CAAA,CAAA,EAClC;IACI,IAAA;QACIC,MAAAA,QAAWC,2MAAAA,EAASH,CAAE;QAKrBI,0OAJWC,EAAmBH,GAAU,aAAA,GAAA,IAAII,QAAQ;YACzDC,QAAQN,EAAQM,MAAAA;QAAAA,CACjB;IAAA,EAAA,OAGU;QACJP,OAAAA;IAAAA;AAEX;AAEO,SAASQ,EAAeC,CAAAA,EAAwB;IAC9CA,OAAAA,EAAOC,MAAAA,CAAO,CAAC,EAAEC,WAAAA,KAAgBF,EAAOG,KAAAA,CAAM,CAAC;AACxD;AAEgBC,SAAAA,EAAqBC,CAAAA,EAAiBC,CAAAA,EAAqB;IAClED,OAAAA,MAASE,KAAAA,IAAYF,IAAOC;AACrC;AAMO,SAASE,EACdC,CAAAA,EACAC,CAAAA,EACAlB,IAA6B,CAAA,CAAA,EAC7B;IACO,OAAA;QACLiB;QACAC;QACAlB,EAAQmB,MAAAA;QACRnB,EAAQoB,MAAAA;QACRpB,EAAQqB,UAAAA;QACRrB,EAAQsB,QAAQ;KAAA,CAEfC,MAAAA,CAAQC,CAAAA,IAAMA,KAAM,IAAuB,EAC3CC,IAAAA,CAAK,GAAG;AACb;AAMO,SAASC,EAAiB,EAC/BC,qBAAAA,CAAAA,EACAC,UAAAA,CAAAA,EACAC,kBAAAA,CAAAA,EACAC,YAAAA,CAAAA,EAMF,EAAG;IACD,OAAO;QAACH;QAAqBC;QAAUC;QAAkBC,CAAU;KAAA,CAChEP,MAAAA,CAAQC,CAAMA,IAAAA,KAAM,IAAuB,EAC3CC,IAAAA,CAAK,GAAG;AACb;AAMO,SAASM,EAAmB,EACjCC,uBAAAA,CAAAA,EACAJ,UAAAA,CAAAA,EACAC,kBAAAA,CAAAA,EACAC,YAAAA,CAAAA,EAMF,EAAG;IACD,OAAO;QAACE;QAAuBJ;QAAUC;QAAkBC,CAAU;KAAA,CAClEP,MAAAA,CAAQC,CAAMA,IAAAA,KAAM,IAAuB,EAC3CC,IAAAA,CAAK,GAAG;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "file": "useNotifications.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/feed/hooks/useNotifications.ts"], "sourcesContent": ["import Knock, { Feed, FeedClientOptions } from \"@knocklabs/client\";\nimport { useMemo, useRef } from \"react\";\n\nimport { useStableOptions } from \"../../core\";\n\nfunction useNotifications(\n  knock: Knock,\n  feedChannelId: string,\n  options: FeedClientOptions = {},\n) {\n  const feedClientRef = useRef<Feed>();\n  const stableOptions = useStableOptions(options);\n\n  return useMemo(() => {\n    if (feedClientRef.current) {\n      feedClientRef.current.dispose();\n    }\n\n    feedClientRef.current = knock.feeds.initialize(\n      feedChannelId,\n      stableOptions,\n    );\n\n    // In development, we need to introduce this extra set state to force a render\n    // for Zustand as otherwise the state doesn't get reflected correctly\n    feedClientRef.current.store.subscribe((t) =>\n      feedClientRef?.current?.store.setState(t),\n    );\n\n    feedClientRef.current.listenForUpdates();\n\n    return feedClientRef.current;\n  }, [knock, feedChannelId, stableOptions]);\n}\n\nexport default useNotifications;\n"], "names": ["useNotifications", "knock", "feedChannelId", "options", "feedClientRef", "useRef", "stableOptions", "useStableOptions", "useMemo", "current", "dispose", "feeds", "initialize", "store", "subscribe", "t", "setState", "listenForUpdates"], "mappings": ";;;;;;;;;;;;AAKA,SAASA,EACPC,CAAAA,EACAC,CAAAA,EACAC,IAA6B,CAAA,CAAA,EAC7B;IACA,MAAMC,sUAAgBC,CAAa,IAC7BC,yUAAgBC,EAAiBJ,CAAO;IAE9C,0UAAOK,EAAQ,IAAA,CACTJ,EAAcK,OAAAA,IAChBL,EAAcK,OAAAA,CAAQC,OAAAA,CAAQ,GAGhCN,EAAcK,OAAAA,GAAUR,EAAMU,KAAAA,CAAMC,UAAAA,CAClCV,GACAI,CACF,GAIcG,EAAAA,OAAAA,CAAQI,KAAAA,CAAMC,SAAAA,CAAWC,CAAAA;;YACrCX,OAAAA,CAAAA,IAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAeK,OAAAA,KAAfL,OAAAA,KAAAA,IAAAA,EAAwBS,KAAAA,CAAMG,QAAAA,CAASD;QAAAA,CACzC,GAEAX,EAAcK,OAAAA,CAAQQ,gBAAAA,CAAiB,GAEhCb,EAAcK,OAAAA,GACpB;QAACR;QAAOC;QAAeI,CAAa;KAAC;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "file": "useNotificationStore.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/feed/hooks/useNotificationStore.ts"], "sourcesContent": ["import { Feed, type FeedStoreState } from \"@knocklabs/client\";\nimport { type <PERSON><PERSON><PERSON>, type UseBoundStore, useStore } from \"zustand\";\n\nexport type Selector<T> = (state: FeedStoreState) => T;\n\n/**\n * Access a Bounded Store instance by converting our vanilla store to a UseBoundStore\n * https://zustand.docs.pmnd.rs/guides/typescript#bounded-usestore-hook-for-vanilla-stores\n * Allow passing a selector down from useCreateNotificationStore OR useNotificationStore\n * We'll favor the the one passed later outside of useCreateNotificationStore instantiation\n */\nfunction useCreateNotificationStore<T>(\n  feedClient: Feed,\n): UseBoundStore<StoreApi<FeedStoreState>> {\n  // Keep selector optional for external use\n  // useStore requires a selector so we'll pass in a default one when not provided\n  const useBoundedStore = (selector?: Selector<T>) =>\n    useStore(feedClient.store, selector ?? ((state) => state as T));\n  return useBoundedStore as UseBoundStore<StoreApi<FeedStoreState>>;\n}\n\n/**\n * A hook used to access content within the notification store.\n *\n * @example\n *\n * ```ts\n * const { items, metadata } = useNotificationStore(feedClient);\n * ```\n *\n * A selector can be used to access a subset of the store state.\n *\n * @example\n *\n * ```ts\n * const { items, metadata } = useNotificationStore(feedClient, (state) => ({\n *   items: state.items,\n *   metadata: state.metadata,\n * }));\n * ```\n */\nfunction useNotificationStore(feedClient: Feed): FeedStoreState;\nfunction useNotificationStore<T>(feedClient: Feed, selector: Selector<T>): T;\nfunction useNotificationStore<T>(\n  feedClient: Feed,\n  selector?: Selector<T>,\n): T | FeedStoreState {\n  const useStoreLocal = useCreateNotificationStore(feedClient);\n  return useStoreLocal(selector ?? ((state) => state as T));\n}\n\nexport { useCreateNotificationStore };\nexport default useNotificationStore;\n"], "names": ["useCreateNotificationStore", "feedClient", "useBoundedStore", "selector", "useStore", "store", "state", "useNotificationStore", "useStoreLocal"], "mappings": ";;;;;;AAWA,SAASA,EACPC,CAAAA,EACyC;IAKlCC,OAFiBA,CAACC,yRACvBC,EAASH,EAAWI,KAAAA,EAAOF,KAAAA,CAAcG,CAAAA,IAAUA,CAAAA,CAAW;AAElE;AAwBA,SAASC,EACPN,CAAAA,EACAE,CAAAA,EACoB;IAEbK,OADeR,EAA2BC,CAAU,EACtCE,KAAAA,CAAcG,CAAAA,IAAUA,CAAAA,CAAW;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "file": "KnockFeedProvider.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/feed/context/KnockFeedProvider.tsx"], "sourcesContent": ["import Knock, {\n  Feed,\n  FeedClientOptions,\n  FeedStoreState,\n} from \"@knocklabs/client\";\nimport * as React from \"react\";\nimport { PropsWithChildren } from \"react\";\nimport type { StoreApi, UseBoundStore } from \"zustand\";\n\nimport { useKnockClient } from \"../../core\";\nimport { ColorMode } from \"../../core/constants\";\nimport { feedProviderKey } from \"../../core/utils\";\nimport { useCreateNotificationStore } from \"../hooks\";\nimport useNotifications from \"../hooks/useNotifications\";\n\nexport interface KnockFeedProviderState {\n  knock: Knock;\n  feedClient: Feed;\n  useFeedStore: UseBoundStore<StoreApi<FeedStoreState>>;\n  colorMode: ColorMode;\n}\n\nconst KnockFeedContext = React.createContext<\n  KnockFeedProviderState | undefined\n>(undefined);\n\nexport interface KnockFeedProviderProps {\n  // Feed props\n  feedId: string | undefined;\n\n  // Extra options\n  colorMode?: ColorMode;\n\n  // Feed client options\n  defaultFeedOptions?: FeedClientOptions;\n}\n\nexport const KnockFeedProvider: React.FC<\n  PropsWithChildren<KnockFeedProviderProps>\n> = ({ feedId, children, defaultFeedOptions = {}, colorMode = \"light\" }) => {\n  let knock: Knock;\n  try {\n    knock = useKnockClient();\n  } catch (_) {\n    throw new Error(\"KnockFeedProvider must be used within a KnockProvider.\");\n  }\n\n  const feedClient = useNotifications(knock, feedId ?? \"\", defaultFeedOptions);\n  const useFeedStore = useCreateNotificationStore(feedClient);\n\n  return (\n    <KnockFeedContext.Provider\n      key={feedProviderKey(knock.userId, feedId ?? \"\", defaultFeedOptions)}\n      value={{\n        knock,\n        feedClient,\n        useFeedStore,\n        colorMode,\n      }}\n    >\n      {children}\n    </KnockFeedContext.Provider>\n  );\n};\n\nexport const useKnockFeed = (): KnockFeedProviderState => {\n  const context = React.useContext(KnockFeedContext);\n  if (!context) {\n    throw new Error(\"useKnockFeed must be used within a KnockFeedProvider\");\n  }\n\n  return context;\n};\n"], "names": ["KnockFeedContext", "React", "createContext", "undefined", "KnockFeedProvider", "feedId", "children", "defaultFeedOptions", "colorMode", "knock", "useKnockClient", "Error", "feedClient", "useNotifications", "useFeedStore", "useCreateNotificationStore", "feedProviderKey", "userId", "useKnockFeed", "context", "useContext"], "mappings": ";;;;;;;;;;;;;;;;;;AAsBA,MAAMA,6TAAmBC,EAAMC,cAAAA,EAE7BC,KAAAA,CAAS,GAaEC,IAETA,CAAC,EAAEC,QAAAA,CAAAA,EAAQC,UAAAA,CAAAA,EAAUC,oBAAAA,IAAqB,CAAC,CAAA,EAAGC,WAAAA,IAAY,OAAA,EAAQ,KAAM;IACtEC,IAAAA;IACA,IAAA;QACFA,KAAQC,0UAAAA,CAAe;IAAA,EAAA,OACb;QACJ,MAAA,IAAIC,MAAM,wDAAwD;IAAA;IAG1E,MAAMC,IAAaC,qUAAAA,EAAiBJ,GAAOJ,KAAU,IAAIE,CAAkB,GACrEO,gWAAeC,EAA2BH,CAAU;IAE1D,OACG,aAAA,GAAAX,EAAA,uUAAA,EAAAD,EAAiB,QAAA,EAAjB;QACC,MAAKgB,wTAAAA,EAAgBP,EAAMQ,MAAAA,EAAQZ,KAAU,IAAIE,CAAkB;QACnE,OAAO;YACLE,OAAAA;YACAG,YAAAA;YACAE,cAAAA;YACAN,WAAAA;QAAAA;IAAAA,GAGDF,CACH;AAEJ,GAEaY,IAAeA,MAA8B;IAClDC,MAAAA,6TAAUlB,EAAMmB,WAAAA,EAAWpB,CAAgB;IACjD,IAAI,CAACmB,GACG,MAAA,IAAIR,MAAM,sDAAsD;IAGjEQ,OAAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "file": "useFeedSettings.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/feed/hooks/useFeedSettings.ts"], "sourcesContent": ["import { Feed } from \"@knocklabs/client\";\nimport { useEffect, useState } from \"react\";\n\nexport type FeedSettings = {\n  features: {\n    branding_required: boolean;\n  };\n};\n\nfunction useFeedSettings(feedClient: Feed): {\n  settings: FeedSettings | null;\n  loading: boolean;\n} {\n  const [settings, setSettings] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // TODO: consider moving this into the feed client and into the feed store state when\n  // we're using this in other areas of the feed\n  useEffect(() => {\n    async function getSettings() {\n      const knock = feedClient.knock;\n      const apiClient = knock.client();\n      const feedSettingsPath = `/v1/users/${knock.userId}/feeds/${feedClient.feedId}/settings`;\n      setIsLoading(true);\n\n      const response = await apiClient.makeRequest({\n        method: \"GET\",\n        url: feedSettingsPath,\n      });\n\n      if (!response.error) {\n        setSettings(response.body);\n      }\n\n      setIsLoading(false);\n    }\n\n    getSettings();\n    // TODO: Check if we can remove this disable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  return { settings, loading: isLoading };\n}\n\nexport default useFeedSettings;\n"], "names": ["useFeedSettings", "feedClient", "settings", "setSettings", "useState", "isLoading", "setIsLoading", "useEffect", "getSettings", "knock", "apiClient", "client", "feedSettingsPath", "userId", "feedId", "response", "makeRequest", "method", "url", "error", "body", "loading"], "mappings": ";;;;;AASA,SAASA,EAAgBC,CAAAA,EAGvB;IACA,MAAM,CAACC,GAAUC,CAAW,CAAA,uUAAIC,EAAS,IAAI,GACvC,CAACC,GAAWC,CAAY,CAAA,uUAAIF,EAAS,CAAA,CAAK;IAIhDG,4UAAAA,EAAU,MAAM;QACd,eAAeC,IAAc;YAC3B,MAAMC,IAAQR,EAAWQ,KAAAA,EACnBC,IAAYD,EAAME,MAAAA,CAAO,GACzBC,IAAmB,CAAA,UAAA,EAAaH,EAAMI,MAAM,CAAA,OAAA,EAAUZ,EAAWa,MAAM,CAAA,SAAA,CAAA;YAC7ER,EAAa,CAAA,CAAI;YAEXS,MAAAA,IAAW,MAAML,EAAUM,WAAAA,CAAY;gBAC3CC,QAAQ;gBACRC,KAAKN;YAAAA,CACN;YAEIG,EAASI,KAAAA,IACZhB,EAAYY,EAASK,IAAI,GAG3Bd,EAAa,CAAA,CAAK;QAAA;QAGRE,EAAA;IAGd,GAAG,EAAE,GAEE;QAAEN,UAAAA;QAAUmB,SAAShB;IAAU;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "file": "KnockGuideProvider.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/guide/context/KnockGuideProvider.tsx"], "sourcesContent": ["import Knock, {\n  KnockGuideClient,\n  KnockGuideTargetParams,\n} from \"@knocklabs/client\";\nimport * as React from \"react\";\n\nimport { useKnockClient, useStableOptions } from \"../../core\";\nimport { ColorMode } from \"../../core/constants\";\n\ntype KnockGuideProviderValue = {\n  client: KnockGuideClient;\n  colorMode: ColorMode;\n};\n\nexport const KnockGuideContext = React.createContext<\n  KnockGuideProviderValue | undefined\n>(undefined);\n\ntype Props = {\n  channelId: string;\n  readyToTarget: boolean;\n  listenForUpdates?: boolean;\n  colorMode?: ColorMode;\n  targetParams?: KnockGuideTargetParams;\n  trackLocationFromWindow?: boolean;\n};\n\nexport const KnockGuideProvider: React.FC<React.PropsWithChildren<Props>> = ({\n  channelId,\n  readyToTarget,\n  listenForUpdates = true,\n  colorMode = \"light\",\n  targetParams = {},\n  trackLocationFromWindow = true,\n  children,\n}) => {\n  let knock: Knock;\n\n  try {\n    knock = useKnockClient();\n  } catch (_) {\n    throw new Error(\"KnockGuideProvider must be used within a KnockProvider\");\n  }\n\n  const stableTargetParams = useStableOptions(targetParams);\n\n  const knockGuideClient = React.useMemo(() => {\n    return new KnockGuideClient(knock, channelId, stableTargetParams, {\n      trackLocationFromWindow,\n    });\n  }, [knock, channelId, stableTargetParams, trackLocationFromWindow]);\n\n  React.useEffect(() => {\n    if (readyToTarget) {\n      knockGuideClient.fetch();\n      if (listenForUpdates) knockGuideClient.subscribe();\n    }\n\n    return () => knockGuideClient.cleanup();\n  }, [readyToTarget, listenForUpdates, knockGuideClient]);\n\n  return (\n    <KnockGuideContext.Provider\n      value={{\n        client: knockGuideClient,\n        colorMode,\n      }}\n    >\n      {children}\n    </KnockGuideContext.Provider>\n  );\n};\n"], "names": ["KnockGuideContext", "React", "createContext", "undefined", "KnockGuideProvider", "channelId", "readyToTarget", "listenForUpdates", "colorMode", "targetParams", "trackLocationFromWindow", "children", "knock", "useKnockClient", "Error", "stableTargetParams", "useStableOptions", "knockGuideClient", "useMemo", "KnockGuideClient", "useEffect", "fetch", "subscribe", "cleanup", "client"], "mappings": ";;;;;;;;;;;;;;AAcaA,MAAAA,6TAAoBC,EAAMC,cAAAA,EAErCC,KAAAA,CAAS,GAWEC,IAA+DA,CAAC,EAC3EC,WAAAA,CAAAA,EACAC,eAAAA,CAAAA,EACAC,kBAAAA,IAAmB,CAAA,CAAA,EACnBC,WAAAA,IAAY,OAAA,EACZC,cAAAA,IAAe,CAAC,CAAA,EAChBC,yBAAAA,IAA0B,CAAA,CAAA,EAC1BC,UAAAA,CAAAA,EACF,KAAM;IACAC,IAAAA;IAEA,IAAA;QACFA,+UAAQC,CAAe;IAAA,EAAA,OACb;QACJ,MAAA,IAAIC,MAAM,wDAAwD;IAAA;IAGpEC,MAAAA,yUAAqBC,EAAiBP,CAAY,GAElDQ,6TAAmBhB,EAAMiB,QAAAA,EAAQ,IAC9B,mTAAIC,oBAAAA,CAAiBP,GAAOP,GAAWU,GAAoB;YAChEL,yBAAAA;QAAAA,CACD,GACA;QAACE;QAAOP;QAAWU;QAAoBL,CAAuB;KAAC;IAElET,QAAAA,EAAMmB,kUAAAA,EAAU,IAAA,CACVd,KAAAA,CACFW,EAAiBI,KAAAA,CAAM,GACnBd,KAAAA,EAAmCe,SAAAA,CAAU,CAAA,GAG5C,IAAML,EAAiBM,OAAAA,CAAQ,CAAA,GACrC;QAACjB;QAAeC;QAAkBU,CAAgB;KAAC,GAGnD,aAAA,4TAAAhB,EAAA,cAAA,EAAAD,EAAkB,QAAA,EAAlB;QACC,OAAO;YACLwB,QAAQP;YACRT,WAAAA;QAAAA;IAAAA,GAGDG,CACH;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "file": "useGuideContext.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/guide/hooks/useGuideContext.ts"], "sourcesContent": ["import { KnockGuideClient } from \"@knocklabs/client\";\nimport * as React from \"react\";\n\nimport { KnockGuideContext } from \"../context\";\n\nexport interface UseGuideContextReturn {\n  client: KnockGuideClient;\n  colorMode: \"light\" | \"dark\";\n}\n\nexport const useGuideContext = (): UseGuideContextReturn => {\n  const context = React.useContext(KnockGuideContext);\n  if (!context) {\n    throw new Error(\"useGuide must be used within a KnockGuideProvider\");\n  }\n\n  return context;\n};\n"], "names": ["useGuideContext", "context", "React", "useContext", "KnockGuideContext", "Error"], "mappings": ";;;;;;;AAUO,MAAMA,IAAkBA,MAA6B;IACpDC,MAAAA,6TAAUC,EAAMC,WAAAA,8TAAWC,oBAAiB;IAClD,IAAI,CAACH,GACG,MAAA,IAAII,MAAM,mDAAmD;IAG9DJ,OAAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "file": "useGuide.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/guide/hooks/useGuide.ts"], "sourcesContent": ["import {\n  KnockGuide,\n  KnockGuideFilterPara<PERSON>,\n  KnockGuideStep,\n} from \"@knocklabs/client\";\nimport { useStore } from \"@tanstack/react-store\";\n\nimport { UseGuideContextReturn, useGuideContext } from \"./useGuideContext\";\n\ninterface UseGuideReturn extends UseGuideContextReturn {\n  guide: KnockGuide | undefined;\n  step: KnockGuideStep | undefined;\n}\n\nexport const useGuide = (filters: KnockGuideFilterParams): UseGuideReturn => {\n  const context = useGuideContext();\n\n  if (!filters.key && !filters.type) {\n    throw new Error(\n      \"useGuide must be used with at least one filter: either a guide key or a guide type\",\n    );\n  }\n\n  const { client, colorMode } = context;\n\n  const [guide] = useStore(client.store, (state) =>\n    client.select(state, filters),\n  );\n\n  const step = guide && guide.steps.find((s) => !s.message.archived_at);\n\n  return { client, colorMode, guide, step };\n};\n"], "names": ["useGuide", "filters", "context", "useGuideContext", "key", "type", "Error", "client", "colorMode", "guide", "useStore", "store", "state", "select", "step", "steps", "find", "s", "message", "archived_at"], "mappings": ";;;;;;;AAcaA,MAAAA,IAAWA,CAACC,MAAoD;IAC3E,MAAMC,iVAAUC,CAAgB;IAEhC,IAAI,CAACF,EAAQG,GAAAA,IAAO,CAACH,EAAQI,IAAAA,EACrB,MAAA,IAAIC,MACR,oFACF;IAGI,MAAA,EAAEC,QAAAA,CAAAA,EAAQC,WAAAA,CAAAA,EAAAA,GAAcN,GAExB,CAACO,CAAK,CAAA,IAAIC,6SAAAA,EAASH,EAAOI,KAAAA,EAAQC,CAAAA,IACtCL,EAAOM,MAAAA,CAAOD,GAAOX,CAAO,CAC9B,GAEMa,IAAOL,KAASA,EAAMM,KAAAA,CAAMC,IAAAA,CAAMC,CAAM,IAAA,CAACA,EAAEC,OAAAA,CAAQC,WAAW;IAE7D,OAAA;QAAEZ,QAAAA;QAAQC,WAAAA;QAAWC,OAAAA;QAAOK,MAAAA;IAAK;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "file": "useTranslations.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/i18n/hooks/useTranslations.ts"], "sourcesContent": ["import { useContext } from \"react\";\n\nimport { I18nContext } from \"../context/KnockI18nProvider\";\nimport { I18nContent, locales } from \"../languages\";\n\nexport function useTranslations() {\n  const { translations, locale } = useContext<I18nContent>(I18nContext);\n\n  return {\n    locale,\n    t: (key: keyof typeof translations) => {\n      // We always use english as the default translation when a key doesn't exist\n      return translations[key] || locales.en.translations[key];\n    },\n  };\n}\n"], "names": ["useTranslations", "translations", "locale", "useContext", "I18nContext", "t", "key", "locales", "en"], "mappings": ";;;;;;;;;AAKO,SAASA,IAAkB;IAC1B,MAAA,EAAEC,cAAAA,CAAAA,EAAcC,QAAAA,CAAAA,EAAAA,yUAAWC,4TAAwBC,cAAW;IAE7D,OAAA;QACLF,QAAAA;QACAG,GAAGA,CAACC,IAEKL,CAAAA,CAAaK,CAAG,CAAA,oTAAKC,UAAAA,CAAQC,EAAAA,CAAGP,YAAAA,CAAaK,CAAG,CAAA;IAE3D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "file": "useMsTeamsConnectionStatus.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/ms-teams/hooks/useMsTeamsConnectionStatus.ts"], "sourcesContent": ["import Knock from \"@knocklabs/client\";\nimport { useEffect, useState } from \"react\";\n\nimport { useTranslations } from \"../../i18n\";\n\nexport type ConnectionStatus =\n  | \"connecting\"\n  | \"connected\"\n  | \"disconnected\"\n  | \"error\"\n  | \"disconnecting\";\n\ntype UseMsTeamsConnectionStatusOutput = {\n  connectionStatus: ConnectionStatus;\n  setConnectionStatus: (status: ConnectionStatus) => void;\n  errorLabel: string | null;\n  setErrorLabel: (errorLabel: string) => void;\n  actionLabel: string | null;\n  setActionLabel: (actionLabel: string | null) => void;\n};\n\nfunction useMsTeamsConnectionStatus(\n  knock: Knock,\n  knockMsTeamsChannelId: string,\n  tenantId: string,\n): UseMsTeamsConnectionStatusOutput {\n  const { t } = useTranslations();\n\n  const [connectionStatus, setConnectionStatus] =\n    useState<ConnectionStatus>(\"connecting\");\n  const [errorLabel, setErrorLabel] = useState<string | null>(null);\n  const [actionLabel, setActionLabel] = useState<string | null>(null);\n\n  useEffect(() => {\n    const checkAuthStatus = async () => {\n      if (connectionStatus !== \"connecting\") return;\n\n      try {\n        const authRes = await knock.msTeams.authCheck({\n          tenant: tenantId,\n          knockChannelId: knockMsTeamsChannelId,\n        });\n\n        if (authRes.connection?.ok === true) {\n          return setConnectionStatus(\"connected\");\n        }\n\n        if (authRes.connection?.ok === false) {\n          return setConnectionStatus(\"disconnected\");\n        }\n\n        // This is a normal response for a tenant that doesn't have\n        // ms_teams_tenant_id set on it, meaning it's not connected to MS Teams,\n        // so we give it a \"disconnected\" status instead of an error status.\n        if (\n          authRes.code === \"ERR_BAD_REQUEST\" &&\n          authRes.response?.data?.message === t(\"msTeamsTenantIdNotSet\")\n        ) {\n          return setConnectionStatus(\"disconnected\");\n        }\n\n        // This is for any Knock errors that would require a reconnect.\n        setConnectionStatus(\"error\");\n      } catch (_error) {\n        setConnectionStatus(\"error\");\n      }\n    };\n\n    checkAuthStatus();\n  }, [connectionStatus, tenantId, knockMsTeamsChannelId, knock.msTeams, t]);\n\n  return {\n    connectionStatus,\n    setConnectionStatus,\n    errorLabel,\n    setErrorLabel,\n    actionLabel,\n    setActionLabel,\n  };\n}\n\nexport default useMsTeamsConnectionStatus;\n"], "names": ["useMsTeamsConnectionStatus", "knock", "knockMsTeamsChannelId", "tenantId", "t", "useTranslations", "connectionStatus", "setConnectionStatus", "useState", "error<PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "actionLabel", "setActionLabel", "useEffect", "authRes", "msTeams", "auth<PERSON><PERSON><PERSON>", "tenant", "knockChannelId", "connection", "ok", "code", "response", "data", "message"], "mappings": ";;;;;;;;;AAqBA,SAASA,EACPC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACkC;IAC5B,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,+UAAMC,CAAgB,IAExB,CAACC,GAAkBC,CAAmB,CAAA,GAC1CC,oUAAAA,EAA2B,YAAY,GACnC,CAACC,GAAYC,CAAa,CAAA,uUAAIF,EAAwB,IAAI,GAC1D,CAACG,GAAaC,CAAc,CAAA,uUAAIJ,EAAwB,IAAI;IAElEK,4UAAAA,EAAU,MAAM;QAmCE,CAlCQ,YAAY;;YAClC,IAAIP,MAAqB,cAErB,IAAA;gBACF,MAAMQ,IAAU,MAAMb,EAAMc,OAAAA,CAAQC,SAAAA,CAAU;oBAC5CC,QAAQd;oBACRe,gBAAgBhB;gBAAAA,CACjB;gBAEGY,IAAAA,CAAAA,CAAAA,IAAAA,EAAQK,UAAAA,KAARL,OAAAA,KAAAA,IAAAA,EAAoBM,EAAAA,MAAO,CAAA,GAC7B,OAAOb,EAAoB,WAAW;gBAWtCO,IAAAA,CAAAA,CAREA,IAAAA,EAAQK,UAAAA,KAARL,OAAAA,KAAAA,IAAAA,EAAoBM,EAAAA,MAAO,CAAA,KAQ7BN,EAAQO,IAAAA,KAAS,qBAAA,CAAA,CACjBP,IAAAA,CAAAA,IAAAA,EAAQQ,QAAAA,KAARR,OAAAA,KAAAA,IAAAA,EAAkBS,IAAAA,KAAlBT,OAAAA,KAAAA,IAAAA,EAAwBU,OAAAA,MAAYpB,EAAE,uBAAuB,GAE7D,OAAOG,EAAoB,cAAc;gBAI3CA,EAAoB,OAAO;YAAA,EAAA,OACZ;gBACfA,EAAoB,OAAO;YAAA;QAE/B,CAAA,EAEgB;IAAA,GACf;QAACD;QAAkBH;QAAUD;QAAuBD,EAAMc,OAAAA;QAASX,CAAC;KAAC,GAEjE;QACLE,kBAAAA;QACAC,qBAAAA;QACAE,YAAAA;QACAC,eAAAA;QACAC,aAAAA;QACAC,gBAAAA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "file": "KnockMsTeamsProvider.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/ms-teams/context/KnockMsTeamsProvider.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { PropsWithChildren } from \"react\";\n\nimport { useKnockClient } from \"../../core\";\nimport { msTeamsProviderKey } from \"../../core/utils\";\nimport { useMsTeamsConnectionStatus } from \"../hooks\";\nimport { ConnectionStatus } from \"../hooks/useMsTeamsConnectionStatus\";\n\nexport interface KnockMsTeamsProviderState {\n  knockMsTeamsChannelId: string;\n  tenantId: string;\n  connectionStatus: ConnectionStatus;\n  setConnectionStatus: (connectionStatus: ConnectionStatus) => void;\n  errorLabel: string | null;\n  setErrorLabel: (label: string) => void;\n  actionLabel: string | null;\n  setActionLabel: (label: string | null) => void;\n}\n\nconst MsTeamsProviderStateContext =\n  React.createContext<KnockMsTeamsProviderState | null>(null);\n\nexport interface KnockMsTeamsProviderProps {\n  knockMsTeamsChannelId: string;\n  tenantId: string;\n}\n\nexport const KnockMsTeamsProvider: React.FC<\n  PropsWithChildren<KnockMsTeamsProviderProps>\n> = ({ knockMsTeamsChannelId, tenantId, children }) => {\n  const knock = useKnockClient();\n\n  const {\n    connectionStatus,\n    setConnectionStatus,\n    errorLabel,\n    setErrorLabel,\n    actionLabel,\n    setActionLabel,\n  } = useMsTeamsConnectionStatus(knock, knockMsTeamsChannelId, tenantId);\n\n  return (\n    <MsTeamsProviderStateContext.Provider\n      key={msTeamsProviderKey({\n        knockMsTeamsChannelId,\n        tenantId,\n        connectionStatus,\n        errorLabel,\n      })}\n      value={{\n        connectionStatus,\n        setConnectionStatus,\n        errorLabel,\n        setErrorLabel,\n        actionLabel,\n        setActionLabel,\n        knockMsTeamsChannelId,\n        tenantId,\n      }}\n    >\n      {children}\n    </MsTeamsProviderStateContext.Provider>\n  );\n};\n\nexport const useKnockMsTeamsClient = (): KnockMsTeamsProviderState => {\n  const context = React.useContext(MsTeamsProviderStateContext);\n  if (!context) {\n    throw new Error(\n      \"useKnockMsTeamsClient must be used within a KnockMsTeamsProvider\",\n    );\n  }\n  return context;\n};\n"], "names": ["MsTeamsProviderStateContext", "React", "createContext", "KnockMsTeamsProvider", "knockMsTeamsChannelId", "tenantId", "children", "knock", "useKnockClient", "connectionStatus", "setConnectionStatus", "error<PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "actionLabel", "setActionLabel", "useMsTeamsConnectionStatus", "msTeamsProviderKey", "useKnockMsTeamsClient", "context", "useContext", "Error"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAmBA,MAAMA,6TACJC,EAAMC,cAAAA,EAAgD,IAAI,GAO/CC,IAETA,CAAC,EAAEC,uBAAAA,CAAAA,EAAuBC,UAAAA,CAAAA,EAAUC,UAAAA,CAAAA,EAAS,KAAM;IACrD,MAAMC,+UAAQC,CAAe,IAEvB,EACJC,kBAAAA,CAAAA,EACAC,qBAAAA,CAAAA,EACAC,YAAAA,CAAAA,EACAC,eAAAA,CAAAA,EACAC,aAAAA,CAAAA,EACAC,gBAAAA,CAAAA,EACEC,IAAAA,qVAAAA,EAA2BR,GAAOH,GAAuBC,CAAQ;IAErE,OACG,aAAA,4TAAAJ,EAAA,cAAA,EAAAD,EAA4B,QAAA,EAA5B;QACC,iUAAKgB,EAAmB;YACtBZ,uBAAAA;YACAC,UAAAA;YACAI,kBAAAA;YACAE,YAAAA;QACD,CAAA;QACD,OAAO;YACLF,kBAAAA;YACAC,qBAAAA;YACAC,YAAAA;YACAC,eAAAA;YACAC,aAAAA;YACAC,gBAAAA;YACAV,uBAAAA;YACAC,UAAAA;QAAAA;IAAAA,GAGDC,CACH;AAEJ,GAEaW,IAAwBA,MAAiC;IAC9DC,MAAAA,6TAAUjB,EAAMkB,WAAAA,EAAWnB,CAA2B;IAC5D,IAAI,CAACkB,GACG,MAAA,IAAIE,MACR,kEACF;IAEKF,OAAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "file": "useMsTeamsAuth.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/ms-teams/hooks/useMsTeamsAuth.ts"], "sourcesContent": ["import { useKnockMsTeamsClient } from \"..\";\nimport { TENANT_OBJECT_COLLECTION } from \"@knocklabs/client\";\nimport { useCallback, useMemo } from \"react\";\n\nimport { useKnockClient } from \"../../core\";\n\nconst MS_TEAMS_ADMINCONSENT_URL =\n  \"https://login.microsoftonline.com/organizations/adminconsent\";\n\nconst AUTH_REDIRECT_PATH = \"/providers/ms-teams/authenticate\";\n\ninterface UseMsTeamsAuthOutput {\n  buildMsTeamsAuthUrl: () => string;\n  disconnectFromMsTeams: () => void;\n}\n\nfunction useMsTeamsAuth(\n  msTeamsBotId: string,\n  redirectUrl?: string,\n): UseMsTeamsAuthOutput {\n  const knock = useKnockClient();\n  const {\n    setConnectionStatus,\n    knockMsTeamsChannelId,\n    tenantId,\n    setActionLabel,\n  } = useKnockMsTeamsClient();\n\n  const authRedirectUri = useMemo(\n    () => knock.host + AUTH_REDIRECT_PATH,\n    [knock.host],\n  );\n\n  const buildMsTeamsAuthUrl = useCallback(() => {\n    const rawParams = {\n      state: JSON.stringify({\n        redirect_url: redirectUrl,\n        ms_teams_tenant_object: {\n          object_id: tenantId,\n          collection: TENANT_OBJECT_COLLECTION,\n        },\n        channel_id: knockMsTeamsChannelId,\n        public_key: knock.apiKey,\n        user_token: knock.userToken,\n      }),\n      client_id: msTeamsBotId,\n      redirect_uri: authRedirectUri,\n    };\n    return `${MS_TEAMS_ADMINCONSENT_URL}?${new URLSearchParams(rawParams)}`;\n  }, [\n    redirectUrl,\n    tenantId,\n    knockMsTeamsChannelId,\n    knock.apiKey,\n    knock.userToken,\n    msTeamsBotId,\n    authRedirectUri,\n  ]);\n\n  const disconnectFromMsTeams = useCallback(async () => {\n    setActionLabel(null);\n    setConnectionStatus(\"disconnecting\");\n    try {\n      const revokeResult = await knock.msTeams.revokeAccessToken({\n        tenant: tenantId,\n        knockChannelId: knockMsTeamsChannelId,\n      });\n\n      setConnectionStatus(revokeResult === \"ok\" ? \"disconnected\" : \"error\");\n    } catch (_error) {\n      setConnectionStatus(\"error\");\n    }\n  }, [\n    setConnectionStatus,\n    knock.msTeams,\n    tenantId,\n    knockMsTeamsChannelId,\n    setActionLabel,\n  ]);\n\n  return { buildMsTeamsAuthUrl, disconnectFromMsTeams };\n}\n\nexport default useMsTeamsAuth;\n"], "names": ["MS_TEAMS_ADMINCONSENT_URL", "AUTH_REDIRECT_PATH", "useMsTeamsAuth", "msTeamsBotId", "redirectUrl", "knock", "useKnockClient", "setConnectionStatus", "knockMsTeamsChannelId", "tenantId", "setActionLabel", "useKnockMsTeamsClient", "authRedirectUri", "useMemo", "host", "buildMsTeamsAuthUrl", "useCallback", "rawParams", "state", "JSON", "stringify", "redirect_url", "ms_teams_tenant_object", "object_id", "collection", "TENANT_OBJECT_COLLECTION", "channel_id", "public_key", "<PERSON><PERSON><PERSON><PERSON>", "user_token", "userToken", "client_id", "redirect_uri", "URLSearchParams", "disconnectFromMsTeams", "revokeResult", "msTeams", "revokeAccessToken", "tenant", "knockChannelId"], "mappings": ";;;;;;;;;;;;;;;;;;;AAMA,MAAMA,IACJ,gEAEIC,IAAqB;AAO3B,SAASC,EACPC,CAAAA,EACAC,CAAAA,EACsB;IACtB,MAAMC,+UAAQC,CAAe,IACvB,EACJC,qBAAAA,CAAAA,EACAC,uBAAAA,CAAAA,EACAC,UAAAA,CAAAA,EACAC,gBAAAA,CAAAA,EAAAA,mWACEC,CAAsB,IAEpBC,uUAAkBC,EACtB,IAAMR,EAAMS,IAAAA,GAAOb,GACnB;QAACI,EAAMS,IAAI;KACb,GAEMC,2UAAsBC,EAAY,MAAM;QAC5C,MAAMC,IAAY;YAChBC,OAAOC,KAAKC,SAAAA,CAAU;gBACpBC,cAAcjB;gBACdkB,wBAAwB;oBACtBC,WAAWd;oBACXe,iUAAYC,2BAAAA;gBACd;gBACAC,YAAYlB;gBACZmB,YAAYtB,EAAMuB,MAAAA;gBAClBC,YAAYxB,EAAMyB,SAAAA;YAAAA,CACnB;YACDC,WAAW5B;YACX6B,cAAcpB;QAChB;QACA,OAAO,GAAGZ,CAAyB,CAAA,CAAA,EAAI,IAAIiC,gBAAgBhB,CAAS,CAAC,EAAA;IACvE,GAAG;QACDb;QACAK;QACAD;QACAH,EAAMuB,MAAAA;QACNvB,EAAMyB,SAAAA;QACN3B;QACAS,CAAe;KAChB,GAEKsB,2UAAwBlB,EAAY,YAAY;QACpDN,EAAe,IAAI,GACnBH,EAAoB,eAAe;QAC/B,IAAA;YACF,MAAM4B,IAAe,MAAM9B,EAAM+B,OAAAA,CAAQC,iBAAAA,CAAkB;gBACzDC,QAAQ7B;gBACR8B,gBAAgB/B;YAAAA,CACjB;YAEmB2B,EAAAA,MAAiB,OAAO,iBAAiB,OAAO;QAAA,EAAA,OACrD;YACf5B,EAAoB,OAAO;QAAA;IAC7B,GACC;QACDA;QACAF,EAAM+B,OAAAA;QACN3B;QACAD;QACAE,CAAc;KACf;IAEM,OAAA;QAAEK,qBAAAA;QAAqBmB,uBAAAA;IAAsB;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "file": "useMsTeamsTeams.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/ms-teams/hooks/useMsTeamsTeams.ts"], "sourcesContent": ["import { GetMsTeamsTeamsResponse, MsTeamsTeam } from \"@knocklabs/client\";\nimport { useEffect, useMemo } from \"react\";\nimport useSWRInfinite from \"swr/infinite\";\n\nimport { useKnockClient } from \"../../core\";\nimport { useKnockMsTeamsClient } from \"../context\";\nimport { MsTeamsTeamQueryOptions } from \"../interfaces\";\n\nconst MAX_COUNT = 1000;\n\nconst QUERY_KEY = \"MS_TEAMS_TEAMS\";\n\ntype UseMsTeamsTeamsOptions = {\n  queryOptions?: MsTeamsTeamQueryOptions;\n};\n\ntype UseMsTeamsTeamsOutput = {\n  data: MsTeamsTeam[];\n  isLoading: boolean;\n  refetch: () => void;\n};\n\ntype QueryKey = [key: string, skiptoken: string] | null;\n\nfunction getQueryKey(\n  pageIndex: number,\n  previousPageData: GetMsTeamsTeamsResponse,\n): QueryKey {\n  // First page so just pass empty\n  if (pageIndex === 0) {\n    return [QUERY_KEY, \"\"];\n  }\n\n  // If there's no more data then return an empty next skiptoken\n  if (previousPageData && [\"\", null].includes(previousPageData.skip_token)) {\n    return null;\n  }\n\n  // Next skiptoken exists so pass it\n  return [QUERY_KEY, previousPageData.skip_token ?? \"\"];\n}\n\nfunction useMsTeamsTeams({\n  queryOptions = {},\n}: UseMsTeamsTeamsOptions): UseMsTeamsTeamsOutput {\n  const knock = useKnockClient();\n  const { knockMsTeamsChannelId, tenantId, connectionStatus } =\n    useKnockMsTeamsClient();\n\n  const fetchTeams = (queryKey: QueryKey) =>\n    knock.msTeams.getTeams({\n      knockChannelId: knockMsTeamsChannelId,\n      tenant: tenantId,\n      queryOptions: {\n        $skiptoken: queryKey?.[1],\n        $top: queryOptions?.limitPerPage,\n        $filter: queryOptions?.filter,\n        $select: queryOptions?.select,\n      },\n    });\n\n  const { data, error, isLoading, isValidating, setSize, mutate } =\n    useSWRInfinite<GetMsTeamsTeamsResponse>(getQueryKey, fetchTeams, {\n      initialSize: 0,\n      revalidateOnFocus: false,\n      revalidateFirstPage: false,\n    });\n\n  const lastPage = data?.at(-1);\n  const hasNextPage = lastPage === undefined || !!lastPage.skip_token;\n\n  const teams = useMemo(\n    () =>\n      (data ?? [])\n        .flatMap((page) => page?.ms_teams_teams)\n        .filter((team) => !!team),\n    [data],\n  );\n\n  const maxCount = queryOptions?.maxCount || MAX_COUNT;\n\n  useEffect(() => {\n    if (\n      connectionStatus === \"connected\" &&\n      !error &&\n      hasNextPage &&\n      !isLoading &&\n      !isValidating &&\n      teams.length < maxCount\n    ) {\n      // Fetch a page at a time until we have nothing else left to fetch\n      // or we've already hit the max amount of teams to fetch\n      setSize((size) => size + 1);\n    }\n  }, [\n    teams.length,\n    setSize,\n    hasNextPage,\n    isLoading,\n    isValidating,\n    maxCount,\n    error,\n    connectionStatus,\n  ]);\n\n  return {\n    data: teams,\n    isLoading: isLoading || isValidating,\n    refetch: () => mutate(),\n  };\n}\n\nexport default useMsTeamsTeams;\n"], "names": ["MAX_COUNT", "QUERY_KEY", "get<PERSON><PERSON>y<PERSON>ey", "pageIndex", "previousPageData", "includes", "skip_token", "useMsTeamsTeams", "queryOptions", "knock", "useKnockClient", "knockMsTeamsChannelId", "tenantId", "connectionStatus", "useKnockMsTeamsClient", "fetchTeams", "query<PERSON><PERSON>", "msTeams", "getTeams", "knockChannelId", "tenant", "$skiptoken", "$top", "limitPerPage", "$filter", "filter", "$select", "select", "data", "error", "isLoading", "isValidating", "setSize", "mutate", "useSWRInfinite", "initialSize", "revalidateOnFocus", "revalidateFirstPage", "lastPage", "at", "hasNextPage", "undefined", "teams", "useMemo", "flatMap", "page", "ms_teams_teams", "team", "maxCount", "useEffect", "length", "size", "refetch"], "mappings": ";;;;;;;;;;;;;;;;AAQA,MAAMA,IAAY,KAEZC,IAAY;AAclB,SAASC,EACPC,CAAAA,EACAC,CAAAA,EACU;IAEV,OAAID,MAAc,IACT;QAACF;QAAW,EAAE;KAAA,GAInBG,KAAoB;QAAC;QAAI,IAAI;KAAA,CAAEC,QAAAA,CAASD,EAAiBE,UAAU,IAC9D,OAIF;QAACL;QAAWG,EAAiBE,UAAAA,IAAc,EAAE;KAAA;AACtD;AAEA,SAASC,EAAgB,EACvBC,cAAAA,IAAe,CAAA,CAAA,EACO,EAA0B;IAChD,MAAMC,+UAAQC,CAAe,IACvB,EAAEC,uBAAAA,CAAAA,EAAuBC,UAAAA,CAAAA,EAAUC,kBAAAA,CAAAA,EAAAA,mWACvCC,CAAsB,IAElBC,IAAaA,CAACC,IAClBP,EAAMQ,OAAAA,CAAQC,QAAAA,CAAS;YACrBC,gBAAgBR;YAChBS,QAAQR;YACRJ,cAAc;gBACZa,YAAYL,KAAAA,OAAAA,KAAAA,IAAAA,CAAAA,CAAW,EAAA;gBACvBM,MAAMd,KAAAA,OAAAA,KAAAA,IAAAA,EAAce,YAAAA;gBACpBC,SAAShB,KAAAA,OAAAA,KAAAA,IAAAA,EAAciB,MAAAA;gBACvBC,SAASlB,KAAAA,OAAAA,KAAAA,IAAAA,EAAcmB,MAAAA;YAAAA;QACzB,CACD,GAEG,EAAEC,MAAAA,CAAAA,EAAMC,OAAAA,CAAAA,EAAOC,WAAAA,CAAAA,EAAWC,cAAAA,CAAAA,EAAcC,SAAAA,CAAAA,EAASC,QAAAA,CAAAA,EAAAA,yOACrDC,EAAwChC,GAAaa,GAAY;QAC/DoB,aAAa;QACbC,mBAAmB,CAAA;QACnBC,qBAAqB,CAAA;IAAA,CACtB,GAEGC,IAAWV,KAAAA,OAAAA,KAAAA,IAAAA,EAAMW,EAAAA,CAAG,CAAA,IACpBC,IAAcF,MAAaG,KAAAA,KAAa,CAAC,CAACH,EAAShC,UAAAA,EAEnDoC,KAAQC,kUAAAA,EACZ,IAAA,CACGf,KAAQ,EAAA,EACNgB,OAAAA,CAASC,CAASA,IAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAMC,cAAc,EACtCrB,MAAAA,CAAQsB,CAAAA,IAAS,CAAC,CAACA,CAAI,GAC5B;QAACnB,CAAI;KACP,GAEMoB,IAAAA,CAAWxC,KAAAA,OAAAA,KAAAA,IAAAA,EAAcwC,QAAAA,KAAYhD;IAE3CiD,4UAAAA,EAAU,MAAM;QAEZpC,MAAqB,eACrB,CAACgB,KACDW,KACA,CAACV,KACD,CAACC,KACDW,EAAMQ,MAAAA,GAASF,KAING,EAAAA,CAAAA,IAASA,IAAO,CAAC;IAE9B,GAAG;QACDT,EAAMQ,MAAAA;QACNlB;QACAQ;QACAV;QACAC;QACAiB;QACAnB;QACAhB,CAAgB;KACjB,GAEM;QACLe,MAAMc;QACNZ,WAAWA,KAAaC;QACxBqB,SAASA,IAAMnB,EAAO;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "file": "useMsTeamsChannels.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/ms-teams/hooks/useMsTeamsChannels.ts"], "sourcesContent": ["import { GetMsTeamsChannelsResponse, MsTeamsChannel } from \"@knocklabs/client\";\nimport useS<PERSON> from \"swr\";\n\nimport { useKnockClient } from \"../../core\";\nimport { useKnockMsTeamsClient } from \"../context\";\nimport { MsTeamsChannelQueryOptions } from \"../interfaces\";\n\nconst QUERY_KEY = \"MS_TEAMS_CHANNELS\";\n\ntype UseMsTeamsChannelsProps = {\n  teamId?: string;\n  queryOptions?: MsTeamsChannelQueryOptions;\n};\n\ntype UseMsTeamsChannelsOutput = {\n  data: MsTeamsChannel[];\n  isLoading: boolean;\n  refetch: () => void;\n};\n\nfunction useMsTeamsChannels({\n  teamId,\n  queryOptions,\n}: UseMsTeamsChannelsProps): UseMsTeamsChannelsOutput {\n  const knock = useKnockClient();\n  const { knockMsTeamsChannelId, tenantId } = useKnockMsTeamsClient();\n\n  const fetchChannels = () =>\n    knock.msTeams.getChannels({\n      knockChannelId: knockMsTeamsChannelId,\n      tenant: tenantId,\n      teamId: teamId!,\n      queryOptions: {\n        $filter: queryOptions?.filter,\n        $select: queryOptions?.select,\n      },\n    });\n\n  const { data, isLoading, isValidating, mutate } =\n    useSWR<GetMsTeamsChannelsResponse>(\n      teamId ? [QUERY_KEY, teamId] : null,\n      fetchChannels,\n      { revalidateOnFocus: false },\n    );\n\n  return {\n    data: data?.ms_teams_channels ?? [],\n    isLoading: isLoading || isValidating,\n    refetch: () => mutate(),\n  };\n}\n\nexport default useMsTeamsChannels;\n"], "names": ["QUERY_KEY", "useMsTeamsChannels", "teamId", "queryOptions", "knock", "useKnockClient", "knockMsTeamsChannelId", "tenantId", "useKnockMsTeamsClient", "fetchChannels", "msTeams", "getChannels", "knockChannelId", "tenant", "$filter", "filter", "$select", "select", "data", "isLoading", "isValidating", "mutate", "useSWR", "revalidateOnFocus", "ms_teams_channels", "refetch"], "mappings": ";;;;;;;;;;;;;;;;AAOA,MAAMA,IAAY;AAalB,SAASC,EAAmB,EAC1BC,QAAAA,CAAAA,EACAC,cAAAA,CAAAA,EACuB,EAA6B;IACpD,MAAMC,QAAQC,uUAAAA,CAAe,IACvB,EAAEC,uBAAAA,CAAAA,EAAuBC,UAAAA,CAAAA,EAAAA,mWAAaC,CAAsB,IAE5DC,IAAgBA,IACpBL,EAAMM,OAAAA,CAAQC,WAAAA,CAAY;YACxBC,gBAAgBN;YAChBO,QAAQN;YACRL,QAAAA;YACAC,cAAc;gBACZW,SAASX,KAAAA,OAAAA,KAAAA,IAAAA,EAAcY,MAAAA;gBACvBC,SAASb,KAAAA,OAAAA,KAAAA,IAAAA,EAAcc,MAAAA;YAAAA;QACzB,CACD,GAEG,EAAEC,MAAAA,CAAAA,EAAMC,WAAAA,CAAAA,EAAWC,cAAAA,CAAAA,EAAcC,QAAAA,CAAAA,EAAAA,sPACrCC,EACEpB,IAAS;QAACF;QAAWE,CAAM;KAAA,GAAI,MAC/BO,GACA;QAAEc,mBAAmB,CAAA;IAAA,CACvB;IAEK,OAAA;QACLL,MAAAA,CAAMA,KAAAA,OAAAA,KAAAA,IAAAA,EAAMM,iBAAAA,KAAqB,CAAE,CAAA;QACnCL,WAAWA,KAAaC;QACxBK,SAASA,IAAMJ,EAAO;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "file": "useConnectedMsTeamsChannels.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/ms-teams/hooks/useConnectedMsTeamsChannels.ts"], "sourcesContent": ["import { useKnockMsTeamsClient } from \"..\";\nimport { MsTeamsChannelConnection } from \"@knocklabs/client\";\nimport { GenericData } from \"@knocklabs/types\";\nimport { useState } from \"react\";\nimport useSWR from \"swr\";\n\nimport { RecipientObject } from \"../../..\";\nimport { useKnockClient } from \"../../core\";\nimport { useTranslations } from \"../../i18n\";\n\nconst QUERY_KEY = \"MS_TEAMS_CONNECTED_CHANNELS\";\n\ntype UseConnectedMsTeamsChannelsProps = {\n  msTeamsChannelsRecipientObject: RecipientObject;\n};\n\ntype UseConnectedMsTeamsChannelsOutput = {\n  data: MsTeamsChannelConnection[] | null;\n  updateConnectedChannels: (\n    connectedChannels: MsTeamsChannelConnection[],\n  ) => Promise<void>;\n  loading: boolean;\n  error: string | null;\n  updating: boolean;\n};\n\nfunction useConnectedMsTeamsChannels({\n  msTeamsChannelsRecipientObject: { objectId, collection },\n}: UseConnectedMsTeamsChannelsProps): UseConnectedMsTeamsChannelsOutput {\n  const { t } = useTranslations();\n  const knock = useKnockClient();\n  const { connectionStatus, knockMsTeamsChannelId } = useKnockMsTeamsClient();\n\n  const [error, setError] = useState<string | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  const {\n    data: connectedChannels,\n    mutate,\n    isValidating,\n    isLoading,\n  } = useSWR<MsTeamsChannelConnection[]>(\n    // Only fetch when Microsoft Teams is connected\n    connectionStatus === \"connected\"\n      ? [QUERY_KEY, knockMsTeamsChannelId, collection, objectId]\n      : null,\n    async () => {\n      return knock.objects\n        .getChannelData({\n          collection,\n          objectId,\n          channelId: knockMsTeamsChannelId,\n        })\n        .then((res) => res.data?.connections ?? [])\n        .catch(() => []);\n    },\n    {\n      onSuccess: () => {\n        setError(null);\n      },\n    },\n  );\n\n  const updateConnectedChannels = async (\n    channelsToSendToKnock: MsTeamsChannelConnection[],\n  ) => {\n    setIsUpdating(true);\n    try {\n      await mutate(\n        () =>\n          knock.objects\n            .setChannelData({\n              objectId,\n              collection,\n              channelId: knockMsTeamsChannelId,\n              data: { connections: channelsToSendToKnock },\n            })\n            .then((res) => (res as GenericData).data?.connections ?? []),\n        {\n          populateCache: true,\n          revalidate: false,\n          optimisticData: channelsToSendToKnock,\n        },\n      );\n    } catch (_error) {\n      setError(t(\"msTeamsChannelSetError\") || \"\");\n    }\n    setIsUpdating(false);\n  };\n\n  return {\n    data: connectedChannels ?? null,\n    updateConnectedChannels,\n    updating: isUpdating,\n    loading: isLoading || isValidating,\n    error,\n  };\n}\n\nexport default useConnectedMsTeamsChannels;\n"], "names": ["QUERY_KEY", "useConnectedMsTeamsChannels", "msTeamsChannelsRecipientObject", "objectId", "collection", "t", "useTranslations", "knock", "useKnockClient", "connectionStatus", "knockMsTeamsChannelId", "useKnockMsTeamsClient", "error", "setError", "useState", "isUpdating", "setIsUpdating", "data", "connectedChannels", "mutate", "isValidating", "isLoading", "useSWR", "objects", "getChannelData", "channelId", "then", "res", "connections", "catch", "onSuccess", "updateConnectedChannels", "channelsToSendToKnock", "setChannelData", "populateCache", "revalidate", "optimisticData", "updating", "loading"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAUA,MAAMA,IAAY;AAgBlB,SAASC,EAA4B,EACnCC,gCAAgC,EAAEC,UAAAA,CAAAA,EAAUC,YAAAA,CAAAA,EAAAA,EACZ,EAAsC;IAChE,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,+UAAMC,CAAgB,IACxBC,QAAQC,uUAAAA,CAAe,IACvB,EAAEC,kBAAAA,CAAAA,EAAkBC,uBAAAA,CAAAA,EAAAA,OAA0BC,4VAAAA,CAAsB,IAEpE,CAACC,GAAOC,CAAQ,CAAA,uUAAIC,EAAwB,IAAI,GAChD,CAACC,GAAYC,CAAa,CAAA,GAAIF,oUAAAA,EAAS,CAAA,CAAK,GAE5C,EACJG,MAAMC,CAAAA,EACNC,QAAAA,CAAAA,EACAC,cAAAA,CAAAA,EACAC,WAAAA,CAAAA,EAAAA,sPACEC,EAAAA,+CAAAA;IAEFb,MAAqB,cACjB;QAACT;QAAWU;QAAuBN;QAAYD,CAAQ;KAAA,GACvD,MACJ,UACSI,EAAMgB,OAAAA,CACVC,cAAAA,CAAe;YACdpB,YAAAA;YACAD,UAAAA;YACAsB,WAAWf;QACZ,CAAA,EACAgB,IAAAA,CAAMC,CAAAA,MAAAA;;YAAQA,OAAAA,CAAAA,CAAAA,IAAAA,EAAIV,IAAAA,KAAJU,OAAAA,KAAAA,IAAAA,EAAUC,WAAAA,KAAe,EAAA;SAAE,EACzCC,KAAAA,CAAM,IAAM,EAAE,GAEnB;QACEC,WAAWA,MAAM;YACfjB,EAAS,IAAI;QAAA;IACf;IA+BG,OAAA;QACLI,MAAMC,KAAqB;QAC3Ba,yBA7B8B,OAC9BC,MACG;YACHhB,EAAc,CAAA,CAAI;YACd,IAAA;gBACF,MAAMG,EACJ,IACEZ,EAAMgB,OAAAA,CACHU,cAAAA,CAAe;wBACd9B,UAAAA;wBACAC,YAAAA;wBACAqB,WAAWf;wBACXO,MAAM;4BAAEW,aAAaI;wBAAAA;oBAAsB,CAC5C,EACAN,IAAAA,CAAMC,CAAAA,MAAAA;;wBAASA,OAAAA,CAAAA,CAAAA,IAAAA,EAAoBV,IAAAA,KAApBU,OAAAA,KAAAA,IAAAA,EAA0BC,WAAAA,KAAe,CAAA,CAAA;oBAAA,CAAE,GAC/D;oBACEM,eAAe,CAAA;oBACfC,YAAY,CAAA;oBACZC,gBAAgBJ;gBAAAA,CAEpB;YAAA,EAAA,OACe;gBACN3B,EAAAA,EAAE,wBAAwB,KAAK,EAAE;YAAA;YAE5CW,EAAc,CAAA,CAAK;QACrB;QAKEqB,UAAUtB;QACVuB,SAASjB,KAAaD;QACtBR,OAAAA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "file": "useSlackConnectionStatus.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/slack/hooks/useSlackConnectionStatus.ts"], "sourcesContent": ["import Knock from \"@knocklabs/client\";\nimport { useEffect, useState } from \"react\";\n\nimport { useTranslations } from \"../../i18n\";\n\nexport type ConnectionStatus =\n  | \"connecting\"\n  | \"connected\"\n  | \"disconnected\"\n  | \"error\"\n  | \"disconnecting\";\n\ntype UseSlackConnectionStatusOutput = {\n  connectionStatus: ConnectionStatus;\n  setConnectionStatus: (status: ConnectionStatus) => void;\n  errorLabel: string | null;\n  setErrorLabel: (errorLabel: string) => void;\n  actionLabel: string | null;\n  setActionLabel: (actionLabel: string | null) => void;\n};\n\n/**\n * Transforms a slack error message into\n * a formatted one. Slack error messages: https://api.slack.com/methods/auth.test#errors\n *\n * Ex.: \"account_inactive\" -> \"Account inactive\"\n */\nconst formatSlackErrorMessage = (errorMessage: string) => {\n  const firstLetter = errorMessage.substring(0, 1).toUpperCase();\n  const rest = errorMessage.substring(1);\n  return firstLetter?.concat(rest).replace(\"_\", \" \");\n};\n\nfunction useSlackConnectionStatus(\n  knock: Knock,\n  knockSlackChannelId: string,\n  tenantId: string,\n): UseSlackConnectionStatusOutput {\n  const { t } = useTranslations();\n  const [connectionStatus, setConnectionStatus] =\n    useState<ConnectionStatus>(\"connecting\");\n  const [errorLabel, setErrorLabel] = useState<string | null>(null);\n  const [actionLabel, setActionLabel] = useState<string | null>(null);\n\n  useEffect(() => {\n    const checkAuthStatus = async () => {\n      if (connectionStatus !== \"connecting\") return;\n\n      try {\n        const authRes = await knock.slack.authCheck({\n          tenant: tenantId,\n          knockChannelId: knockSlackChannelId,\n        });\n\n        if (authRes.connection?.ok) {\n          return setConnectionStatus(\"connected\");\n        }\n\n        if (!authRes.connection?.ok) {\n          return setConnectionStatus(\"disconnected\");\n        }\n\n        // This is a normal response for a tenant that doesn't have an access\n        // token set on it, meaning it's not connected to Slack, so we\n        // give it a \"disconnected\" status instead of an error status.\n        if (\n          authRes.code === \"ERR_BAD_REQUEST\" &&\n          authRes.response?.data?.message === t(\"slackAccessTokenNotSet\")\n        ) {\n          return setConnectionStatus(\"disconnected\");\n        }\n\n        // This is for an error coming directly from Slack.\n        if (!authRes.connection?.ok && authRes.connection?.error) {\n          const errorLabel = formatSlackErrorMessage(authRes.connection?.error);\n          setErrorLabel(errorLabel);\n          setConnectionStatus(\"error\");\n          return;\n        }\n\n        // This is for any Knock errors that would require a reconnect.\n\n        setConnectionStatus(\"error\");\n      } catch (_error) {\n        setConnectionStatus(\"error\");\n      }\n    };\n\n    checkAuthStatus();\n  }, [connectionStatus, tenantId, knockSlackChannelId, knock.slack, t]);\n\n  return {\n    connectionStatus,\n    setConnectionStatus,\n    errorLabel,\n    setErrorLabel,\n    actionLabel,\n    setActionLabel,\n  };\n}\n\nexport default useSlackConnectionStatus;\n"], "names": ["formatSlackErrorMessage", "errorMessage", "firstLetter", "substring", "toUpperCase", "rest", "concat", "replace", "useSlackConnectionStatus", "knock", "knockSlackChannelId", "tenantId", "t", "useTranslations", "connectionStatus", "setConnectionStatus", "useState", "error<PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "actionLabel", "setActionLabel", "useEffect", "authRes", "slack", "auth<PERSON><PERSON><PERSON>", "tenant", "knockChannelId", "connection", "ok", "code", "response", "data", "message", "error"], "mappings": ";;;;;;;;;AA2BA,MAAMA,IAA0BA,CAACC,MAAyB;IACxD,MAAMC,IAAcD,EAAaE,SAAAA,CAAU,GAAG,CAAC,EAAEC,WAAAA,CAAY,GACvDC,IAAOJ,EAAaE,SAAAA,CAAU,CAAC;IACrC,OAAOD,KAAAA,OAAAA,KAAAA,IAAAA,EAAaI,MAAAA,CAAOD,GAAME,OAAAA,CAAQ,KAAK;AAChD;AAEA,SAASC,EACPC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACgC;IAC1B,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,GAAMC,4UAAAA,CAAgB,IACxB,CAACC,GAAkBC,CAAmB,CAAA,uUAC1CC,EAA2B,YAAY,GACnC,CAACC,GAAYC,CAAa,CAAA,uUAAIF,EAAwB,IAAI,GAC1D,CAACG,GAAaC,CAAc,CAAA,uUAAIJ,EAAwB,IAAI;IAElEK,4UAAAA,EAAU,MAAM;QA4CE,CA3CQ,YAAY;;YAClC,IAAIP,MAAqB,cAErB,IAAA;gBACF,MAAMQ,IAAU,MAAMb,EAAMc,KAAAA,CAAMC,SAAAA,CAAU;oBAC1CC,QAAQd;oBACRe,gBAAgBhB;gBAAAA,CACjB;gBAEGY,IAAAA,CAAAA,IAAAA,EAAQK,UAAAA,KAARL,QAAAA,EAAoBM,EAAAA,EACtB,OAAOb,EAAoB,WAAW;gBAWtCO,IARE,CAAA,CAAA,CAACA,IAAAA,EAAQK,UAAAA,KAARL,QAAAA,EAAoBM,EAAAA,KAQvBN,EAAQO,IAAAA,KAAS,qBAAA,CAAA,CACjBP,IAAAA,CAAAA,IAAAA,EAAQQ,QAAAA,KAARR,OAAAA,KAAAA,IAAAA,EAAkBS,IAAAA,KAAlBT,OAAAA,KAAAA,IAAAA,EAAwBU,OAAAA,MAAYpB,EAAE,wBAAwB,GAE9D,OAAOG,EAAoB,cAAc;gBAI3C,IAAI,CAAA,CAAA,CAACO,IAAAA,EAAQK,UAAAA,KAARL,QAAAA,EAAoBM,EAAAA,KAAAA,CAAMN,IAAAA,EAAQK,UAAAA,KAARL,QAAAA,EAAoBW,KAAAA,EAAO;oBACxD,MAAMhB,IAAajB,EAAAA,CAAwBsB,IAAAA,EAAQK,UAAAA,KAARL,OAAAA,KAAAA,IAAAA,EAAoBW,KAAK;oBACpEf,EAAcD,CAAU,GACxBF,EAAoB,OAAO;oBAC3B;gBAAA;gBAKFA,EAAoB,OAAO;YAAA,EAAA,OACZ;gBACfA,EAAoB,OAAO;YAAA;QAE/B,CAAA,EAEgB;IAAA,GACf;QAACD;QAAkBH;QAAUD;QAAqBD,EAAMc,KAAAA;QAAOX,CAAC;KAAC,GAE7D;QACLE,kBAAAA;QACAC,qBAAAA;QACAE,YAAAA;QACAC,eAAAA;QACAC,aAAAA;QACAC,gBAAAA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "file": "KnockSlackProvider.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/slack/context/KnockSlackProvider.tsx"], "sourcesContent": ["import { useSlackConnectionStatus } from \"..\";\nimport * as React from \"react\";\nimport { PropsWithChildren } from \"react\";\n\nimport { slackProviderKey } from \"../../core\";\nimport { useKnockClient } from \"../../core\";\nimport { ConnectionStatus } from \"../hooks/useSlackConnectionStatus\";\n\nexport interface KnockSlackProviderState {\n  knockSlackChannelId: string;\n  tenantId: string;\n  /**\n   * @deprecated Use `tenantId` instead. This field will be removed in a future release.\n   */\n  tenant: string;\n  connectionStatus: ConnectionStatus;\n  setConnectionStatus: (connectionStatus: ConnectionStatus) => void;\n  errorLabel: string | null;\n  setErrorLabel: (label: string) => void;\n  actionLabel: string | null;\n  setActionLabel: (label: string | null) => void;\n}\n\nconst SlackProviderStateContext =\n  React.createContext<KnockSlackProviderState | null>(null);\n\nexport type KnockSlackProviderProps =\n  | {\n      knockSlackChannelId: string;\n      /**\n       * @deprecated Use `tenantId` instead. This field will be removed in a future release.\n       */\n      tenant: string;\n    }\n  | {\n      knockSlackChannelId: string;\n      tenantId: string;\n    };\n\nexport const KnockSlackProvider: React.FC<\n  PropsWithChildren<KnockSlackProviderProps>\n> = (props) => {\n  const { knockSlackChannelId, children } = props;\n  const tenantId = \"tenantId\" in props ? props.tenantId : props.tenant;\n\n  const knock = useKnockClient();\n\n  const {\n    connectionStatus,\n    setConnectionStatus,\n    errorLabel,\n    setErrorLabel,\n    actionLabel,\n    setActionLabel,\n  } = useSlackConnectionStatus(knock, knockSlackChannelId, tenantId);\n\n  return (\n    <SlackProviderStateContext.Provider\n      key={slackProviderKey({\n        knockSlackChannelId,\n        tenantId,\n        connectionStatus,\n        errorLabel,\n      })}\n      value={{\n        connectionStatus,\n        setConnectionStatus,\n        errorLabel,\n        setErrorLabel,\n        actionLabel,\n        setActionLabel,\n        knockSlackChannelId,\n        // Assign the same value to both tenant and tenantId for backwards compatibility\n        tenant: tenantId,\n        tenantId,\n      }}\n    >\n      {children}\n    </SlackProviderStateContext.Provider>\n  );\n};\n\nexport const useKnockSlackClient = (): KnockSlackProviderState => {\n  const context = React.useContext(\n    SlackProviderStateContext,\n  ) as KnockSlackProviderState;\n  if (context === undefined) {\n    throw new Error(\n      \"useKnockSlackClient must be used within a KnockSlackProvider\",\n    );\n  }\n  return context as KnockSlackProviderState;\n};\n"], "names": ["SlackProviderStateContext", "React", "createContext", "KnockSlackProvider", "props", "knockSlackChannelId", "children", "tenantId", "tenant", "knock", "useKnockClient", "connectionStatus", "setConnectionStatus", "error<PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "actionLabel", "setActionLabel", "useSlackConnectionStatus", "slackProviderKey", "useKnockSlackClient", "context", "useContext", "undefined", "Error"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAuBA,MAAMA,6TACJC,EAAMC,cAAAA,EAA8C,IAAI,GAe7CC,IAERC,CAAUA,MAAA;IACP,MAAA,EAAEC,qBAAAA,CAAAA,EAAqBC,UAAAA,CAAAA,EAAAA,GAAaF,GACpCG,IAAW,cAAcH,IAAQA,EAAMG,QAAAA,GAAWH,EAAMI,MAAAA,EAExDC,+UAAQC,CAAe,IAEvB,EACJC,kBAAAA,CAAAA,EACAC,qBAAAA,CAAAA,EACAC,YAAAA,CAAAA,EACAC,eAAAA,CAAAA,EACAC,aAAAA,CAAAA,EACAC,gBAAAA,CAAAA,EACEC,IAAAA,6UAAAA,EAAyBR,GAAOJ,GAAqBE,CAAQ;IAEjE,OACG,aAAA,4TAAAN,EAAA,cAAA,EAAAD,EAA0B,QAAA,EAA1B;QACC,+TAAKkB,EAAiB;YACpBb,qBAAAA;YACAE,UAAAA;YACAI,kBAAAA;YACAE,YAAAA;QACD,CAAA;QACD,OAAO;YACLF,kBAAAA;YACAC,qBAAAA;YACAC,YAAAA;YACAC,eAAAA;YACAC,aAAAA;YACAC,gBAAAA;YACAX,qBAAAA;YAAAA,gFAAAA;YAEAG,QAAQD;YACRA,UAAAA;QAAAA;IAAAA,GAGDD,CACH;AAEJ,GAEaa,IAAsBA,MAA+B;IAC1DC,MAAAA,6TAAUnB,EAAMoB,WAAAA,EACpBrB,CACF;IACA,IAAIoB,MAAYE,KAAAA,GACR,MAAA,IAAIC,MACR,8DACF;IAEKH,OAAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "file": "useSlackChannels.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/slack/hooks/useSlackChannels.ts"], "sourcesContent": ["import { SlackChannelQueryOptions, useKnockSlackClient } from \"..\";\nimport { GetSlackChannelsResponse, SlackChannel } from \"@knocklabs/client\";\nimport { useEffect, useMemo } from \"react\";\nimport useSWRInfinite from \"swr/infinite\";\n\nimport { useKnockClient } from \"../../core\";\n\nconst MAX_COUNT = 1000;\nconst LIMIT_PER_PAGE = 200;\nconst CHANNEL_TYPES = \"private_channel,public_channel\";\n\nconst QUERY_KEY = \"SLACK_CHANNELS\";\n\ntype UseSlackChannelsOptions = {\n  queryOptions?: SlackChannelQueryOptions;\n};\n\ntype UseSlackChannelOutput = {\n  data: SlackChannel[];\n  isLoading: boolean;\n  refetch: () => void;\n};\n\ntype QueryKey = [key: string, cursor: string] | null;\n\nfunction getQueryKey(\n  pageIndex: number,\n  previousPageData: GetSlackChannelsResponse,\n): Query<PERSON><PERSON> {\n  // First page so just pass empty\n  if (pageIndex === 0) {\n    return [QUERY_KEY, \"\"];\n  }\n\n  // If there's no more data then return an empty next cursor\n  if (previousPageData && [\"\", null].includes(previousPageData.next_cursor)) {\n    return null;\n  }\n\n  // Next cursor exists so pass it\n  return [QUERY_KEY, previousPageData.next_cursor ?? \"\"];\n}\n\nfunction useSlackChannels({\n  queryOptions,\n}: UseSlackChannelsOptions): UseSlackChannelOutput {\n  const knock = useKnockClient();\n  const { knockSlackChannelId, tenantId, connectionStatus } =\n    useKnockSlackClient();\n\n  const fetchChannels = (queryKey: QueryKey) => {\n    return knock.slack.getChannels({\n      tenant: tenantId,\n      knockChannelId: knockSlackChannelId,\n      queryOptions: {\n        ...queryOptions,\n        cursor: queryKey?.[1],\n        limit: queryOptions?.limitPerPage || LIMIT_PER_PAGE,\n        types: queryOptions?.types || CHANNEL_TYPES,\n      },\n    });\n  };\n\n  const { data, error, isLoading, isValidating, setSize, mutate } =\n    useSWRInfinite<GetSlackChannelsResponse>(getQueryKey, fetchChannels, {\n      initialSize: 0,\n      revalidateFirstPage: false,\n    });\n\n  const lastPage = data?.at(-1);\n  const hasNextPage = lastPage === undefined || !!lastPage.next_cursor;\n\n  const slackChannels: SlackChannel[] = useMemo(\n    () =>\n      (data ?? [])\n        .flatMap((page) => page?.slack_channels)\n        .filter((channel) => !!channel),\n    [data],\n  );\n\n  const maxCount = queryOptions?.maxCount || MAX_COUNT;\n\n  useEffect(() => {\n    if (\n      connectionStatus === \"connected\" &&\n      !error &&\n      hasNextPage &&\n      !isLoading &&\n      !isValidating &&\n      slackChannels.length < maxCount\n    ) {\n      // Fetch a page at a time until we have nothing else left to fetch\n      // or we've already hit the max amount of channels to fetch\n      setSize((size) => size + 1);\n    }\n  }, [\n    slackChannels.length,\n    setSize,\n    hasNextPage,\n    isLoading,\n    isValidating,\n    maxCount,\n    error,\n    connectionStatus,\n  ]);\n\n  return {\n    data: slackChannels,\n    isLoading: isLoading || isValidating,\n    refetch: () => mutate(),\n  };\n}\n\nexport default useSlackChannels;\n"], "names": ["MAX_COUNT", "LIMIT_PER_PAGE", "CHANNEL_TYPES", "QUERY_KEY", "get<PERSON><PERSON>y<PERSON>ey", "pageIndex", "previousPageData", "includes", "next_cursor", "useSlackChannels", "queryOptions", "knock", "useKnockClient", "knockSlackChannelId", "tenantId", "connectionStatus", "useKnockSlackClient", "fetchChannels", "query<PERSON><PERSON>", "slack", "getChannels", "tenant", "knockChannelId", "cursor", "limit", "limitPerPage", "types", "data", "error", "isLoading", "isValidating", "setSize", "mutate", "useSWRInfinite", "initialSize", "revalidateFirstPage", "lastPage", "at", "hasNextPage", "undefined", "slackChannels", "useMemo", "flatMap", "page", "slack_channels", "filter", "channel", "maxCount", "useEffect", "length", "size", "refetch"], "mappings": ";;;;;;;;;;;;;;;;;;;AAOA,MAAMA,IAAY,KACZC,IAAiB,KACjBC,IAAgB,kCAEhBC,IAAY;AAclB,SAASC,EACPC,CAAAA,EACAC,CAAAA,EACU;IAEV,OAAID,MAAc,IACT;QAACF;QAAW,EAAE;KAAA,GAInBG,KAAoB;QAAC;QAAI,IAAI;KAAA,CAAEC,QAAAA,CAASD,EAAiBE,WAAW,IAC/D,OAIF;QAACL;QAAWG,EAAiBE,WAAAA,IAAe,EAAE;KAAA;AACvD;AAEA,SAASC,EAAiB,EACxBC,cAAAA,CAAAA,EACuB,EAA0B;IACjD,MAAMC,+UAAQC,CAAe,IACvB,EAAEC,qBAAAA,CAAAA,EAAqBC,UAAAA,CAAAA,EAAUC,kBAAAA,CAAAA,EAAAA,yVACrCC,CAAoB,IAEhBC,IAAgBA,CAACC,IACdP,EAAMQ,KAAAA,CAAMC,WAAAA,CAAY;YAC7BC,QAAQP;YACRQ,gBAAgBT;YAChBH,cAAc;gBACZ,GAAGA,CAAAA;gBACHa,QAAQL,KAAAA,OAAAA,KAAAA,IAAAA,CAAAA,CAAW,EAAA;gBACnBM,OAAAA,CAAOd,KAAAA,OAAAA,KAAAA,IAAAA,EAAce,YAAAA,KAAgBxB;gBACrCyB,OAAAA,CAAOhB,KAAAA,OAAAA,KAAAA,IAAAA,EAAcgB,KAAAA,KAASxB;YAAAA;QAChC,CACD,GAGG,EAAEyB,MAAAA,CAAAA,EAAMC,OAAAA,CAAAA,EAAOC,WAAAA,CAAAA,EAAWC,cAAAA,CAAAA,EAAcC,SAAAA,CAAAA,EAASC,QAAAA,CAAAA,EAAAA,yOACrDC,EAAyC7B,GAAaa,GAAe;QACnEiB,aAAa;QACbC,qBAAqB,CAAA;IAAA,CACtB,GAEGC,IAAWT,KAAAA,OAAAA,KAAAA,IAAAA,EAAMU,EAAAA,CAAG,CAAA,IACpBC,IAAcF,MAAaG,KAAAA,KAAa,CAAC,CAACH,EAAS5B,WAAAA,EAEnDgC,uUAAgCC,EACpC,IAAA,CACGd,KAAQ,EAAA,EACNe,OAAAA,CAASC,CAASA,IAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAMC,cAAc,EACtCC,MAAAA,CAAQC,CAAAA,IAAY,CAAC,CAACA,CAAO,GAClC;QAACnB,CAAI;KACP,GAEMoB,IAAAA,CAAWrC,KAAAA,OAAAA,KAAAA,IAAAA,EAAcqC,QAAAA,KAAY/C;IAE3CgD,4UAAAA,EAAU,MAAM;QAEZjC,MAAqB,eACrB,CAACa,KACDU,KACA,CAACT,KACD,CAACC,KACDU,EAAcS,MAAAA,GAASF,KAIdG,EAAAA,CAAAA,IAASA,IAAO,CAAC;IAE9B,GAAG;QACDV,EAAcS,MAAAA;QACdlB;QACAO;QACAT;QACAC;QACAiB;QACAnB;QACAb,CAAgB;KACjB,GAEM;QACLY,MAAMa;QACNX,WAAWA,KAAaC;QACxBqB,SAASA,IAAMnB,EAAO;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1150, "column": 0}, "map": {"version": 3, "file": "useConnectedSlackChannels.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/slack/hooks/useConnectedSlackChannels.ts"], "sourcesContent": ["import { useKnockSlackClient } from \"..\";\nimport { SlackChannelConnection } from \"@knocklabs/client\";\nimport { GenericData } from \"@knocklabs/types\";\nimport { useState } from \"react\";\nimport useSWR from \"swr\";\n\nimport { RecipientObject } from \"../../..\";\nimport { useKnockClient } from \"../../core\";\nimport { useTranslations } from \"../../i18n\";\n\nconst QUERY_KEY = \"SLACK_CONNECTED_CHANNELS\";\n\ntype UseConnectedSlackChannelsProps = {\n  slackChannelsRecipientObject: RecipientObject;\n};\n\ntype UseConnectedSlackChannelsOutput = {\n  data: SlackChannelConnection[] | null;\n  updateConnectedChannels: (\n    connectedChannels: SlackChannelConnection[],\n  ) => Promise<void>;\n  loading: boolean;\n  error: string | null;\n  updating: boolean;\n};\n\nfunction useConnectedSlackChannels({\n  slackChannelsRecipientObject: { objectId, collection },\n}: UseConnectedSlackChannelsProps): UseConnectedSlackChannelsOutput {\n  const { t } = useTranslations();\n  const knock = useKnockClient();\n  const { connectionStatus, knockSlackChannelId } = useKnockSlackClient();\n\n  const [error, setError] = useState<string | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  const {\n    data: connectedChannels,\n    mutate,\n    isValidating,\n    isLoading,\n  } = useSWR<SlackChannelConnection[]>(\n    // Only fetch when Slack is connected\n    connectionStatus === \"connected\"\n      ? [QUERY_KEY, knockSlackChannelId, collection, objectId]\n      : null,\n    async () => {\n      return knock.objects\n        .getChannelData({\n          collection,\n          objectId,\n          channelId: knockSlackChannelId,\n        })\n        .then((res) => res.data?.connections ?? [])\n        .catch(() => []);\n    },\n    {\n      onSuccess: () => {\n        setError(null);\n      },\n    },\n  );\n\n  const updateConnectedChannels = async (\n    channelsToSendToKnock: SlackChannelConnection[],\n  ) => {\n    setIsUpdating(true);\n    try {\n      await mutate(\n        () =>\n          knock.objects\n            .setChannelData({\n              objectId,\n              collection,\n              channelId: knockSlackChannelId,\n              data: { connections: channelsToSendToKnock },\n            })\n            .then((res) => (res as GenericData).data?.connections ?? []),\n        {\n          populateCache: true,\n          revalidate: false,\n          optimisticData: channelsToSendToKnock,\n        },\n      );\n    } catch (_error) {\n      setError(t(\"slackChannelSetError\") || \"\");\n    }\n    setIsUpdating(false);\n  };\n\n  return {\n    data: connectedChannels ?? null,\n    updateConnectedChannels,\n    updating: isUpdating,\n    loading: isLoading || isValidating,\n    error,\n  };\n}\n\nexport default useConnectedSlackChannels;\n"], "names": ["QUERY_KEY", "useConnectedSlackChannels", "slackChannelsRecipientObject", "objectId", "collection", "t", "useTranslations", "knock", "useKnockClient", "connectionStatus", "knockSlackChannelId", "useKnockSlackClient", "error", "setError", "useState", "isUpdating", "setIsUpdating", "data", "connectedChannels", "mutate", "isValidating", "isLoading", "useSWR", "objects", "getChannelData", "channelId", "then", "res", "connections", "catch", "onSuccess", "updateConnectedChannels", "channelsToSendToKnock", "setChannelData", "populateCache", "revalidate", "optimisticData", "updating", "loading"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAUA,MAAMA,IAAY;AAgBlB,SAASC,EAA0B,EACjCC,8BAA8B,EAAEC,UAAAA,CAAAA,EAAUC,YAAAA,CAAAA,EAAAA,EACZ,EAAoC;IAC5D,MAAA,EAAEC,GAAAA,CAAAA,EAAAA,+UAAMC,CAAgB,IACxBC,QAAQC,uUAAAA,CAAe,IACvB,EAAEC,kBAAAA,CAAAA,EAAkBC,qBAAAA,CAAAA,EAAAA,OAAwBC,kVAAAA,CAAoB,IAEhE,CAACC,GAAOC,CAAQ,CAAA,uUAAIC,EAAwB,IAAI,GAChD,CAACC,GAAYC,CAAa,CAAA,GAAIF,oUAAAA,EAAS,CAAA,CAAK,GAE5C,EACJG,MAAMC,CAAAA,EACNC,QAAAA,CAAAA,EACAC,cAAAA,CAAAA,EACAC,WAAAA,CAAAA,EAAAA,sPACEC,EAAAA,qCAAAA;IAEFb,MAAqB,cACjB;QAACT;QAAWU;QAAqBN;QAAYD,CAAQ;KAAA,GACrD,MACJ,UACSI,EAAMgB,OAAAA,CACVC,cAAAA,CAAe;YACdpB,YAAAA;YACAD,UAAAA;YACAsB,WAAWf;QACZ,CAAA,EACAgB,IAAAA,CAAMC,CAAAA,MAAAA;;YAAQA,OAAAA,CAAAA,CAAAA,IAAAA,EAAIV,IAAAA,KAAJU,OAAAA,KAAAA,IAAAA,EAAUC,WAAAA,KAAe,EAAA;SAAE,EACzCC,KAAAA,CAAM,IAAM,EAAE,GAEnB;QACEC,WAAWA,MAAM;YACfjB,EAAS,IAAI;QAAA;IACf;IA+BG,OAAA;QACLI,MAAMC,KAAqB;QAC3Ba,yBA7B8B,OAC9BC,MACG;YACHhB,EAAc,CAAA,CAAI;YACd,IAAA;gBACF,MAAMG,EACJ,IACEZ,EAAMgB,OAAAA,CACHU,cAAAA,CAAe;wBACd9B,UAAAA;wBACAC,YAAAA;wBACAqB,WAAWf;wBACXO,MAAM;4BAAEW,aAAaI;wBAAAA;oBAAsB,CAC5C,EACAN,IAAAA,CAAMC,CAAAA,MAAAA;;wBAASA,OAAAA,CAAAA,CAAAA,IAAAA,EAAoBV,IAAAA,KAApBU,OAAAA,KAAAA,IAAAA,EAA0BC,WAAAA,KAAe,CAAA,CAAA;oBAAA,CAAE,GAC/D;oBACEM,eAAe,CAAA;oBACfC,YAAY,CAAA;oBACZC,gBAAgBJ;gBAAAA,CAEpB;YAAA,EAAA,OACe;gBACN3B,EAAAA,EAAE,sBAAsB,KAAK,EAAE;YAAA;YAE1CW,EAAc,CAAA,CAAK;QACrB;QAKEqB,UAAUtB;QACVuB,SAASjB,KAAaD;QACtBR,OAAAA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "file": "useSlackAuth.mjs", "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40knocklabs%2Breact-core%400.6.1_1cd36181420d457e15c6cbc284fa3aa3/node_modules/%40knocklabs/react-core/src/modules/slack/hooks/useSlackAuth.ts"], "sourcesContent": ["import { useKnockSlackClient } from \"..\";\nimport { TENANT_OBJECT_COLLECTION } from \"@knocklabs/client\";\nimport { useCallback } from \"react\";\n\nimport { useKnockClient } from \"../../core\";\n\nconst SLACK_AUTHORIZE_URL = \"https://slack.com/oauth/v2/authorize\";\nconst DEFAULT_SLACK_SCOPES = [\n  \"chat:write\",\n  \"chat:write.public\",\n  \"channels:read\",\n  \"groups:read\",\n];\n\ntype UseSlackAuthOutput = {\n  buildSlackAuthUrl: () => string;\n  disconnectFromSlack: () => void;\n};\n\ntype UseSlackAuthOptions = {\n  // When provided, the default scopes will be overridden with the provided scopes\n  scopes?: string[];\n  // Additional scopes to add to the default scopes\n  additionalScopes?: string[];\n};\n\n// Here we normalize the options to be a single object with scopes and additionalScopes\n// The \"options\" parameter can be an array of scopes, an object with scopes and additionalScopes, or undefined\n// We handle the array case because it was the previous way to pass options so we're being backward compatible\nfunction normalizeOptions(options?: UseSlackAuthOptions | string[]): {\n  scopes: string[];\n  additionalScopes: string[];\n} {\n  if (!options) {\n    return { scopes: DEFAULT_SLACK_SCOPES, additionalScopes: [] };\n  }\n\n  if (Array.isArray(options)) {\n    return { scopes: DEFAULT_SLACK_SCOPES, additionalScopes: options };\n  }\n\n  return {\n    scopes: options.scopes ?? DEFAULT_SLACK_SCOPES,\n    additionalScopes: options.additionalScopes ?? [],\n  };\n}\n\nfunction useSlackAuth(\n  slackClientId: string,\n  redirectUrl?: string,\n  options?: UseSlackAuthOptions | string[],\n): UseSlackAuthOutput {\n  const knock = useKnockClient();\n  const { setConnectionStatus, knockSlackChannelId, tenantId, setActionLabel } =\n    useKnockSlackClient();\n\n  const { scopes, additionalScopes } = normalizeOptions(options);\n  const combinedScopes = Array.from(new Set(scopes.concat(additionalScopes)));\n\n  const disconnectFromSlack = useCallback(async () => {\n    setActionLabel(null);\n    setConnectionStatus(\"disconnecting\");\n    try {\n      const revoke = await knock.slack.revokeAccessToken({\n        tenant: tenantId,\n        knockChannelId: knockSlackChannelId,\n      });\n\n      if (revoke === \"ok\") {\n        setConnectionStatus(\"disconnected\");\n      } else {\n        setConnectionStatus(\"error\");\n      }\n    } catch (_error) {\n      setConnectionStatus(\"error\");\n    }\n  }, [\n    setConnectionStatus,\n    knock.slack,\n    tenantId,\n    knockSlackChannelId,\n    setActionLabel,\n  ]);\n\n  const buildSlackAuthUrl = useCallback(() => {\n    const rawParams = {\n      state: JSON.stringify({\n        redirect_url: redirectUrl,\n        access_token_object: {\n          object_id: tenantId,\n          collection: TENANT_OBJECT_COLLECTION,\n        },\n        channel_id: knockSlackChannelId,\n        public_key: knock.apiKey,\n        user_token: knock.userToken,\n      }),\n      client_id: slackClientId,\n      scope: combinedScopes.join(\",\"),\n    };\n    return `${SLACK_AUTHORIZE_URL}?${new URLSearchParams(rawParams)}`;\n  }, [\n    redirectUrl,\n    tenantId,\n    knockSlackChannelId,\n    knock.apiKey,\n    knock.userToken,\n    slackClientId,\n    combinedScopes,\n  ]);\n\n  return {\n    buildSlackAuthUrl,\n    disconnectFromSlack,\n  };\n}\n\nexport default useSlackAuth;\n"], "names": ["SLACK_AUTHORIZE_URL", "DEFAULT_SLACK_SCOPES", "normalizeOptions", "options", "Array", "isArray", "scopes", "additionalScopes", "useSlackAuth", "slackClientId", "redirectUrl", "knock", "useKnockClient", "setConnectionStatus", "knockSlackChannelId", "tenantId", "setActionLabel", "useKnockSlackClient", "combinedScopes", "from", "Set", "concat", "disconnectFromSlack", "useCallback", "revoke", "slack", "revokeAccessToken", "tenant", "knockChannelId", "buildSlackAuthUrl", "rawParams", "state", "JSON", "stringify", "redirect_url", "access_token_object", "object_id", "collection", "TENANT_OBJECT_COLLECTION", "channel_id", "public_key", "<PERSON><PERSON><PERSON><PERSON>", "user_token", "userToken", "client_id", "scope", "join", "URLSearchParams"], "mappings": ";;;;;;;;;;;;;;;;;;;AAMA,MAAMA,IAAsB,wCACtBC,IAAuB;IAC3B;IACA;IACA;IACA,aAAa;CAAA;AAkBf,SAASC,EAAiBC,CAAAA,EAGxB;IACA,OAAKA,IAIDC,MAAMC,OAAAA,CAAQF,CAAO,IAChB;QAAEG,QAAQL;QAAsBM,kBAAkBJ;IAAQ,IAG5D;QACLG,QAAQH,EAAQG,MAAAA,IAAUL;QAC1BM,kBAAkBJ,EAAQI,gBAAAA,IAAoB,CAAA,CAAA;IAChD,IAVS;QAAED,QAAQL;QAAsBM,kBAAkB,CAAA,CAAA;IAAG;AAWhE;AAEA,SAASC,EACPC,CAAAA,EACAC,CAAAA,EACAP,CAAAA,EACoB;IACpB,MAAMQ,+UAAQC,CAAe,IACvB,EAAEC,qBAAAA,CAAAA,EAAqBC,qBAAAA,CAAAA,EAAqBC,UAAAA,CAAAA,EAAUC,gBAAAA,CAAAA,EAAAA,yVAC1DC,CAAoB,IAEhB,EAAEX,QAAAA,CAAAA,EAAQC,kBAAAA,CAAAA,EAAAA,GAAqBL,EAAiBC,CAAO,GACvDe,IAAiBd,MAAMe,IAAAA,CAAK,IAAIC,IAAId,EAAOe,MAAAA,CAAOd,CAAgB,CAAC,CAAC,GAEpEe,2UAAsBC,EAAY,YAAY;QAClDP,EAAe,IAAI,GACnBH,EAAoB,eAAe;QAC/B,IAAA;YACF,MAAMW,IAAS,MAAMb,EAAMc,KAAAA,CAAMC,iBAAAA,CAAkB;gBACjDC,QAAQZ;gBACRa,gBAAgBd;YAAAA,CACjB;YAGCD,EADEW,MAAW,OACO,iBAEA,OAFc;QAAA,EAAA,OAIrB;YACfX,EAAoB,OAAO;QAAA;IAC7B,GACC;QACDA;QACAF,EAAMc,KAAAA;QACNV;QACAD;QACAE,CAAc;KACf;IA4BM,OAAA;QACLa,0VA3BwBN,EAAY,MAAM;YAC1C,MAAMO,IAAY;gBAChBC,OAAOC,KAAKC,SAAAA,CAAU;oBACpBC,cAAcxB;oBACdyB,qBAAqB;wBACnBC,WAAWrB;wBACXsB,YAAYC,gVAAAA;oBACd;oBACAC,YAAYzB;oBACZ0B,YAAY7B,EAAM8B,MAAAA;oBAClBC,YAAY/B,EAAMgC,SAAAA;gBAAAA,CACnB;gBACDC,WAAWnC;gBACXoC,OAAO3B,EAAe4B,IAAAA,CAAK,GAAG;YAChC;YACA,OAAO,GAAG9C,CAAmB,CAAA,CAAA,EAAI,IAAI+C,gBAAgBjB,CAAS,CAAC,EAAA;QACjE,GAAG;YACDpB;YACAK;YACAD;YACAH,EAAM8B,MAAAA;YACN9B,EAAMgC,SAAAA;YACNlC;YACAS,CAAc;SACf;QAICI,qBAAAA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": [], "names": [], "mappings": "", "debugId": null}}]}