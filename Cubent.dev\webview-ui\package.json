{"name": "@qapt-coder/vscode-webview", "private": true, "type": "module", "scripts": {"lint": "eslint src --ext=ts,tsx --max-warnings=0", "check-types": "tsc", "pretest": "turbo run bundle --cwd ..", "test": "jest -w=40%", "format": "prettier --write src", "dev": "vite", "build": "tsc -b && vite build", "build:nightly": "tsc -b && vite build --mode nightly", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "clean": "rimraf build tsconfig.tsbuildinfo .turbo"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-portal": "^1.1.5", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@qapt-coder/types": "workspace:^", "@tailwindcss/vite": "^4.0.0", "@tanstack/react-query": "^5.68.0", "@vscode/codicons": "^0.0.36", "@vscode/webview-ui-toolkit": "^1.4.0", "axios": "^1.7.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "debounce": "^2.1.1", "fast-deep-equal": "^3.1.3", "fzf": "^0.5.2", "i18next": "^24.2.2", "i18next-http-backend": "^3.0.2", "knuth-shuffle-seeded": "^1.0.6", "lru-cache": "^11.1.0", "lucide-react": "^0.511.0", "mermaid": "^11.4.1", "posthog-js": "^1.227.2", "pretty-bytes": "^6.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.4.1", "react-markdown": "^9.0.3", "react-remark": "^2.1.0", "react-textarea-autosize": "^8.5.3", "react-use": "^17.5.1", "react-virtuoso": "^4.7.13", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.1", "remove-markdown": "^0.6.0", "shell-quote": "^1.8.2", "shiki": "^3.2.1", "source-map": "^0.7.4", "styled-components": "^6.1.13", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "unist-util-visit": "^5.0.0", "use-sound": "^5.0.0", "vscode-material-icons": "^0.1.1", "vscrui": "^0.2.2", "zod": "^3.24.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@qapt-coder/config-eslint": "workspace:^", "@qapt-coder/config-typescript": "workspace:^", "@storybook/addon-essentials": "^8.5.6", "@storybook/blocks": "^8.5.6", "@storybook/react": "^8.5.6", "@storybook/react-vite": "^8.5.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^27.5.2", "@types/node": "^18.0.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/shell-quote": "^1.7.5", "@types/testing-library__jest-dom": "^5.14.5", "@types/vscode-webview": "^1.57.5", "@vitejs/plugin-react": "^4.3.4", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-simple-dot-reporter": "^1.0.5", "storybook": "^8.5.6", "storybook-dark-mode": "^4.0.2", "ts-jest": "^29.2.5", "typescript": "5.8.3", "vite": "6.3.5"}}