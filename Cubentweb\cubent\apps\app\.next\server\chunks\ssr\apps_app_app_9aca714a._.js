module.exports = {

"[project]/apps/app/app/apple-icon.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/apple-icon.be30e690.png");}}),
"[project]/apps/app/app/apple-icon.png.mjs { IMAGE => \"[project]/apps/app/app/apple-icon.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$app$2f$app$2f$apple$2d$icon$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/apps/app/app/apple-icon.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$app$2f$app$2f$apple$2d$icon$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 192,
    height: 192
};
}}),

};

//# sourceMappingURL=apps_app_app_9aca714a._.js.map