{"greeting": "<PERSON><PERSON>o mừng đến với <PERSON>", "task": {"title": "Nhiệm vụ", "seeMore": "<PERSON><PERSON>", "seeLess": "<PERSON><PERSON>", "tokens": "Tokens:", "cache": "Bộ nhớ đệm:", "apiCost": "Chi phí API:", "contextWindow": "<PERSON><PERSON><PERSON> dài b<PERSON>i cảnh:", "closeAndStart": "<PERSON><PERSON><PERSON> nhiệm vụ và bắt đầu nhiệm vụ mới", "export": "<PERSON><PERSON><PERSON> lịch sử nhiệm vụ", "delete": "<PERSON><PERSON><PERSON> nhi<PERSON> vụ (Shift + Click để bỏ qua xác nhận)", "condenseContext": "<PERSON><PERSON> đọng ngữ cảnh thông minh"}, "unpin": "Bỏ ghim khỏi đầu", "pin": "<PERSON><PERSON> lên đ<PERSON>u", "tokenProgress": {"availableSpace": "<PERSON><PERSON>ông gian khả dụng: {{amount}} tokens", "tokensUsed": "Tokens đã sử dụng: {{used}} trong {{total}}", "reservedForResponse": "<PERSON><PERSON><PERSON> riêng cho phản hồi mô hình: {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON> lại", "tooltip": "<PERSON><PERSON><PERSON> lại thao tác"}, "startNewTask": {"title": "<PERSON><PERSON><PERSON> đ<PERSON> n<PERSON> vụ mới", "tooltip": "<PERSON>ắt đầu một nhiệm vụ mới"}, "proceedAnyways": {"title": "Vẫn tiếp tục", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tục trong khi lệnh đang chạy"}, "save": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> c<PERSON>c thay đổi tệp"}, "reject": {"title": "<PERSON><PERSON> chối", "tooltip": "Từ chối hành động này"}, "completeSubtaskAndReturn": "<PERSON><PERSON><PERSON> thành nhi<PERSON> vụ phụ và quay lại", "approve": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON>t hành động này"}, "runCommand": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON>h<PERSON><PERSON> thi lệnh này"}, "proceedWhileRunning": {"title": "<PERSON><PERSON><PERSON><PERSON> tục trong khi chạy", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tụ<PERSON> bất chấp cảnh báo"}, "killCommand": {"title": "<PERSON><PERSON><PERSON> l<PERSON>", "tooltip": "<PERSON><PERSON><PERSON> l<PERSON>nh hiện tại"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> vụ", "tooltip": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> vụ hiện tại"}, "terminate": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> n<PERSON> v<PERSON> hiện tại"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> thao tác hiện tại"}, "scrollToBottom": "<PERSON><PERSON><PERSON>n xuống cuối cuộc trò chuyện", "about": "<PERSON><PERSON><PERSON>, tái cấu trúc và gỡ lỗi mã bằng sự hỗ trợ của AI. Ki<PERSON>m tra <DocsLink>tài liệu</DocsLink> của chúng tôi để tìm hiểu thêm.", "onboarding": "<strong><PERSON><PERSON> sách nhiệm vụ của bạn trong không gian làm việc này trống.</strong> Bắt đầu bằng cách nhập nhiệm vụ bên dưới. Bạn không chắc chắn nên bắt đầu như thế nào? Đọc thêm về những gì Roo có thể làm cho bạn trong <DocsLink>tài liệu</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "Nhiệm vụ Boomerang", "description": "<PERSON><PERSON> nhỏ các nhiệm vụ thành các phần nhỏ hơn, <PERSON><PERSON> quản lý hơn."}, "stickyModels": {"title": "<PERSON><PERSON> độ dính", "description": "Mỗi chế độ ghi nhớ mô hình đã sử dụng cuối cùng của bạn"}, "tools": {"title": "<PERSON><PERSON><PERSON> cụ", "description": "<PERSON> phép AI gi<PERSON>i quyết vấn đề bằng cách duyệt web, ch<PERSON><PERSON> l<PERSON>, v.v."}, "customizableModes": {"title": "Chế độ tùy chỉnh", "description": "<PERSON><PERSON>c nhân vật chuyên biệt với hành vi riêng và mô hình được chỉ định"}}, "selectMode": "<PERSON><PERSON><PERSON> chế độ tương tác", "selectApiConfig": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh <PERSON>", "enhancePrompt": "<PERSON><PERSON><PERSON> cao yêu cầu với ngữ cảnh bổ sung", "addImages": "<PERSON><PERSON><PERSON><PERSON> hình <PERSON>nh vào tin nh<PERSON>n", "sendMessage": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "typeMessage": "<PERSON><PERSON><PERSON><PERSON> tin nhắn...", "typeTask": "<PERSON><PERSON><PERSON><PERSON> vụ của bạn tại đây...", "addContext": "@ để thêm ngữ cảnh, / để chuyển chế độ", "dragFiles": "giữ shift để kéo tệp", "dragFilesImages": "giữ shift để kéo tệp/hình <PERSON>nh", "enhancePromptDescription": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cao yêu cầu' gi<PERSON><PERSON> cải thiện yêu cầu của bạn bằng cách cung cấp ngữ cảnh bổ sung, làm rõ hoặc diễn đạt lại. H<PERSON><PERSON> thử nhập yêu cầu tại đây và nhấp vào nút một lần nữa để xem cách thức hoạt động.", "errorReadingFile": "Lỗi khi đọc tệp:", "noValidImages": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh hợp lệ nào đư<PERSON><PERSON> xử lý", "separator": "<PERSON><PERSON><PERSON> phân cách", "edit": "Chỉnh sửa...", "forNextMode": "cho chế độ tiếp theo", "error": "Lỗi", "diffError": {"title": "Chỉnh sửa không thành công"}, "troubleMessage": "<PERSON><PERSON> đang gặp sự cố...", "apiRequest": {"title": "<PERSON><PERSON><PERSON> c<PERSON>", "failed": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON> thất bại", "streaming": "<PERSON><PERSON><PERSON> cầu API...", "cancelled": "<PERSON><PERSON><PERSON> c<PERSON>u API đã hủy", "streamingFailed": "Streaming API thất bại"}, "checkpoint": {"initial": "<PERSON><PERSON><PERSON><PERSON> kiểm tra ban đầu", "regular": "<PERSON><PERSON><PERSON><PERSON> kiểm tra", "initializingWarning": "<PERSON>ang khởi tạo điểm kiểm tra... Nếu quá trình này mất quá nhiều thời gian, bạn có thể vô hiệu hóa điểm kiểm tra trong <settingsLink>cài đặt</settingsLink> và khởi động lại tác vụ của bạn.", "menu": {"viewDiff": "<PERSON><PERSON>", "restore": "<PERSON><PERSON><PERSON><PERSON> phục điểm kiểm tra", "restoreFiles": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>p", "restoreFilesDescription": "<PERSON><PERSON><PERSON><PERSON> phục các tệp dự án của bạn về bản chụp được thực hiện tại thời điểm này.", "restoreFilesAndTask": "Khô<PERSON> phục tệ<PERSON> & nhiệm vụ", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "<PERSON><PERSON><PERSON> động này không thể hoàn tác.", "restoreFilesAndTaskDescription": "<PERSON><PERSON><PERSON><PERSON> phục các tệp dự án của bạn về bản chụp được thực hiện tại thời điểm này và xóa tất cả tin nhắn sau điểm này."}, "current": "<PERSON><PERSON><PERSON> t<PERSON>i"}, "instructions": {"wantsToFetch": "<PERSON><PERSON> muốn lấy hướng dẫn chi tiết để hỗ trợ nhiệm vụ hiện tại"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON> mu<PERSON><PERSON> đ<PERSON><PERSON> t<PERSON> n<PERSON>:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON> muốn đọc tệp này bên ngoài không gian làm việc:", "didRead": "<PERSON><PERSON> đã đ<PERSON><PERSON> tệ<PERSON> n<PERSON>:", "wantsToEdit": "<PERSON><PERSON> muốn chỉnh sửa tệp này:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON> muốn chỉnh sửa tệp này bên ngoài không gian làm việc:", "wantsToCreate": "<PERSON><PERSON> muốn tạo một tệp mới:", "wantsToSearchReplace": "<PERSON><PERSON> muốn thực hiện tìm kiếm và thay thế trong tệp này:", "didSearchReplace": "<PERSON><PERSON> đã thực hiện tìm kiếm và thay thế trong tệp này:", "wantsToInsert": "<PERSON><PERSON> muốn chèn nội dung vào tệp này:", "wantsToInsertWithLineNumber": "<PERSON><PERSON> muốn chèn nội dung vào dòng {{lineNumber}} của tệp này:", "wantsToInsertAtEnd": "<PERSON><PERSON> muốn thêm nội dung vào cuối tệp này:", "wantsToReadAndXMore": "<PERSON><PERSON> muốn đ<PERSON>c tệp này và {{count}} tệp khác:", "wantsToReadMultiple": "<PERSON><PERSON> mu<PERSON>n đ<PERSON><PERSON> tệp:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON> muốn xem các tệp cấp cao nhất trong thư mục này:", "didViewTopLevel": "<PERSON><PERSON> đã xem các tệp cấp cao nhất trong thư mục này:", "wantsToViewRecursive": "<PERSON><PERSON> muốn xem đệ quy tất cả các tệp trong thư mục này:", "didViewRecursive": "<PERSON><PERSON> đã xem đệ quy tất cả các tệp trong thư mục này:", "wantsToViewDefinitions": "<PERSON><PERSON> muốn xem tên định nghĩa mã nguồn đư<PERSON><PERSON> sử dụng trong thư mục này:", "didViewDefinitions": "<PERSON>oo đã xem tên định nghĩa mã nguồn đư<PERSON><PERSON> sử dụng trong thư mục này:", "wantsToSearch": "<PERSON><PERSON> muốn tìm kiếm trong thư mục này cho <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON> đã tìm kiếm trong thư mục này cho <code>{{regex}}</code>:"}, "commandOutput": "<PERSON><PERSON><PERSON> qu<PERSON> l<PERSON>nh", "response": "<PERSON><PERSON><PERSON>", "arguments": "<PERSON>ham s<PERSON>", "mcp": {"wantsToUseTool": "<PERSON><PERSON> muốn sử dụng một công cụ trên máy chủ MCP {{serverName}}:", "wantsToAccessResource": "<PERSON><PERSON> muốn truy cập một tài nguyên trên máy chủ MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON> mu<PERSON><PERSON> chuy<PERSON> sang chế độ <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON><PERSON> mu<PERSON><PERSON> chuy<PERSON> sang chế độ <code>{{mode}}</code> vì: {{reason}}", "didSwitch": "<PERSON><PERSON> đã chuyển sang chế độ <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON> đã chuyển sang chế độ <code>{{mode}}</code> vì: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON> muốn tạo một nhiệm vụ phụ mới trong chế độ <code>{{mode}}</code>:", "wantsToFinish": "<PERSON><PERSON> muốn hoàn thành nhiệm vụ phụ này", "newTaskContent": "Hướng dẫn nhiệm vụ phụ", "completionContent": "Nhiệm vụ phụ đã hoàn thành", "resultContent": "<PERSON><PERSON><PERSON> qu<PERSON> n<PERSON> vụ phụ", "defaultResult": "<PERSON>ui lòng tiếp tục với nhiệm vụ tiếp theo.", "completionInstructions": "Nhiệm vụ phụ đã hoàn thành! Bạn có thể xem lại kết quả và đề xuất các sửa đổi hoặc bước tiếp theo. Nếu mọi thứ có vẻ tốt, hãy xác nhận để trả kết quả về nhiệm vụ chính."}, "questions": {"hasQuestion": "<PERSON>oo có một câu hỏi:"}, "taskCompleted": "Nhiệm v<PERSON> hoàn thành", "powershell": {"issues": "<PERSON><PERSON> vẻ như bạn đang gặp vấn đề với Windows PowerShell, vui lòng xem"}, "autoApprove": {"title": "Tự động phê duyệt:", "none": "K<PERSON>ô<PERSON>", "description": "Tự động phê duyệt cho phép Roo Code thực hiện hành động mà không cần xin phép. Chỉ bật cho các hành động bạn hoàn toàn tin tưởng. Cấu hình chi tiết hơn có sẵn trong <settingsLink>Cài đặt</settingsLink>."}, "reasoning": {"thinking": "<PERSON><PERSON> suy nghĩ", "seconds": "{{count}} gi<PERSON>y"}, "contextCondense": {"title": "<PERSON><PERSON> cảnh đã tóm tắt", "condensing": "<PERSON><PERSON> cô đọng ngữ cảnh...", "errorHeader": "<PERSON><PERSON><PERSON><PERSON> thể cô đọng ngữ cảnh", "tokens": "token"}, "followUpSuggest": {"copyToInput": "<PERSON>o chép vào ô nhập liệu (hoặc Shift + nhấp chuột)"}, "announcement": {"title": "🎉 Roo Code {{version}} <PERSON><PERSON> phát hành", "description": "Roo Code {{version}} mang đến các tính năng mạnh mẽ và cải tiến mới dựa trên phản hồi của bạn.", "whatsNew": "<PERSON>ó gì mới", "feature1": "<bold><PERSON><PERSON> đọng ngữ cảnh thông minh được bật mặc định</bold>: C<PERSON> đọng ngữ cảnh hiện được bật mặc định với các cài đặt có thể cấu hình cho khi nào tự động cô đọng", "feature2": "<bold><PERSON><PERSON><PERSON> cô đọng thủ công</bold>: <PERSON><PERSON><PERSON> mới trong tiêu đề nhiệm vụ cho phép bạn kích hoạt cô đọng ngữ cảnh thủ công bất cứ lúc nào", "feature3": "<bold><PERSON>ài đặt cô đọng nâng cao</bold>: <PERSON><PERSON> chỉnh khi nào và cách thức tự động cô đọng thông qua <contextSettingsLink>Cài đặt ngữ cảnh</contextSettingsLink>", "hideButton": "Ẩn thông báo", "detailsDiscussLinks": "<PERSON><PERSON><PERSON><PERSON> thêm chi tiết và thảo luận tại <discordLink>Discord</discordLink> và <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "<PERSON><PERSON> muốn sử dụng trình du<PERSON>:", "consoleLogs": "<PERSON><PERSON><PERSON><PERSON> ký bảng điều khiển", "noNewLogs": "(<PERSON><PERSON><PERSON><PERSON> có nhật ký mới)", "screenshot": "Ảnh chụp màn hình trình du<PERSON>t", "cursor": "con trỏ", "navigation": {"step": "Bước {{current}} / {{total}}", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>"}, "sessionStarted": "<PERSON><PERSON><PERSON> trình duyệt đã bắt đầu", "actions": {"title": "<PERSON><PERSON><PERSON> động trình du<PERSON>: ", "launch": "Khởi chạy trình du<PERSON>t tại {{url}}", "click": "<PERSON><PERSON><PERSON>p ({{coordinate}})", "type": "G<PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON>ng", "scrollUp": "<PERSON><PERSON><PERSON><PERSON> lên", "close": "<PERSON><PERSON><PERSON> tr<PERSON>"}}, "codeblock": {"tooltips": {"expand": "Mở rộng khối mã", "collapse": "<PERSON><PERSON> gọn kh<PERSON>i mã", "enable_wrap": "<PERSON><PERSON>t tự động xuống dòng", "disable_wrap": "Tắt tự động xuống dòng", "copy_code": "Sao chép mã"}}, "systemPromptWarning": "CẢNH BÁO: <PERSON><PERSON> kích hoạt ghi đè lệnh nhắc hệ thống tùy chỉnh. Điều này có thể phá vỡ nghiêm trọng chức năng và gây ra hành vi không thể dự đoán.", "profileViolationWarning": "<PERSON><PERSON> sơ hiện tại vi phạm cài đặt của tổ chức của bạn", "shellIntegration": {"title": "<PERSON><PERSON><PERSON> b<PERSON>o thực thi lệnh", "description": "Lệnh của bạn đang được thực thi mà không có tích hợp shell terminal VSCode. Đ<PERSON> <PERSON>n cảnh báo nà<PERSON>, bạn có thể vô hiệu hóa tích hợp shell trong phần <strong>Terminal</strong> của <settingsLink>cài đặt Roo Code</settingsLink> hoặc khắc phục sự cố tích hợp terminal VSCode bằng liên kết bên dưới.", "troubleshooting": "<PERSON><PERSON>ấ<PERSON> vào đây để xem tài liệu tích hợp shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Đ<PERSON> Đ<PERSON>t G<PERSON>ới Hạn <PERSON> Tự Động <PERSON> Duy<PERSON>", "description": "Roo đã đạt đến giới hạn tự động phê duyệt là {{count}} yêu cầu API. Bạn có muốn đặt lại bộ đếm và tiếp tục nhiệm vụ không?", "button": "Đặt lại và T<PERSON>ế<PERSON> tục"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON> muốn tìm kiếm trong cơ sở mã cho <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON> muốn tìm kiếm trong cơ sở mã cho <code>{{query}}</code> trong <code>{{path}}</code>:", "didSearch": "<PERSON><PERSON> tìm thấy {{count}} kết quả cho <code>{{query}}</code>:"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON> nhận tất cả"}, "deny": {"title": "<PERSON>ừ chối tất cả"}}}