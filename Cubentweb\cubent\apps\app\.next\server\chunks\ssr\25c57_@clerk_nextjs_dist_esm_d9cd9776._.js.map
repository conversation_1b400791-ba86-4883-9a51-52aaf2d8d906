{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/runtime/node/safe-node-apis.js"], "sourcesContent": ["/**\n * This file is used for conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst { existsSync, writeFileSync, readFileSync, appendFileSync, mkdirSync, rmSync } = require('node:fs');\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst path = require('node:path');\nconst fs = {\n  existsSync,\n  writeFileSync,\n  readFileSync,\n  appendFileSync,\n  mkdirSync,\n  rmSync,\n};\n\nconst cwd = () => process.cwd();\n\nmodule.exports = { fs, path, cwd };\n"], "names": [], "mappings": ";;;;;AAAA,IAAA,yBAAA,CAAA,GAAA,+QAAA,CAAA,aAAA,EAAA;IAAA,sCAAA,OAAA,EAAA,MAAA;QAIA,MAAM,EAAE,UAAA,EAAY,aAAA,EAAe,YAAA,EAAc,cAAA,EAAgB,SAAA,EAAW,MAAA,CAAO,CAAA,GAAI,QAAQ,SAAS;QAExG,MAAM,OAAO,QAAQ,WAAW;QAChC,MAAM,KAAK;YACT;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,MAAM,MAAM,IAAM,QAAQ,GAAA,CAAI;QAE9B,OAAO,OAAA,GAAU;YAAE;YAAI;YAAM;QAAI;IAAA;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/fs/utils.ts"], "sourcesContent": ["/**\n * Attention: Only import this module when the node runtime is used.\n * We are using conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\n// @ts-ignore\nimport nodeRuntime from '#safe-node-apis';\n\nconst throwMissingFsModule = (module: string) => {\n  throw new Error(`Clerk: ${module} is missing. This is an internal error. Please contact Clerk's support.`);\n};\n\nconst nodeFsOrThrow = () => {\n  if (!nodeRuntime.fs) {\n    throwMissingFsModule('fs');\n  }\n  return nodeRuntime.fs;\n};\n\nconst nodePathOrThrow = () => {\n  if (!nodeRuntime.path) {\n    throwMissingFsModule('path');\n  }\n  return nodeRuntime.path;\n};\n\nconst nodeCwdOrThrow = () => {\n  if (!nodeRuntime.cwd) {\n    throwMissingFsModule('cwd');\n  }\n  return nodeRuntime.cwd;\n};\n\nexport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow };\n"], "names": [], "mappings": ";;;;;AAKA,OAAO,iBAAiB;;;AAExB,MAAM,uBAAuB,CAAC,WAAmB;IAC/C,MAAM,IAAI,MAAM,CAAA,OAAA,EAAU,MAAM,CAAA,uEAAA,CAAyE;AAC3G;AAEA,MAAM,gBAAgB,MAAM;IAC1B,IAAI,uSAAC,UAAA,CAAY,EAAA,EAAI;QACnB,qBAAqB,IAAI;IAC3B;IACA,6SAAO,UAAA,CAAY,EAAA;AACrB;AAEA,MAAM,kBAAkB,MAAM;IAC5B,IAAI,uSAAC,UAAA,CAAY,IAAA,EAAM;QACrB,qBAAqB,MAAM;IAC7B;IACA,6SAAO,UAAA,CAAY,IAAA;AACrB;AAEA,MAAM,iBAAiB,MAAM;IAC3B,IAAI,uSAAC,UAAA,CAAY,GAAA,EAAK;QACpB,qBAAqB,KAAK;IAC5B;IACA,6SAAO,UAAA,CAAY,GAAA;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/fs/middleware-location.ts"], "sourcesContent": ["import { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow } from './utils';\n\nfunction hasSrcAppDir() {\n  const { existsSync } = nodeFsOrThrow();\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n\n  const projectWithAppSrc = path.join(cwd(), 'src', 'app');\n\n  return !!existsSync(projectWithAppSrc);\n}\n\nfunction suggestMiddlewareLocation() {\n  const fileExtensions = ['ts', 'js'] as const;\n  const suggestionMessage = (\n    extension: (typeof fileExtensions)[number],\n    to: 'src/' | '',\n    from: 'src/app/' | 'app/' | '',\n  ) =>\n    `Clerk: clerkMiddleware() was not run, your middleware file might be misplaced. Move your middleware file to ./${to}middleware.${extension}. Currently located at ./${from}middleware.${extension}`;\n\n  const { existsSync } = nodeFsOrThrow();\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n\n  const projectWithAppSrcPath = path.join(cwd(), 'src', 'app');\n  const projectWithAppPath = path.join(cwd(), 'app');\n\n  const checkMiddlewareLocation = (\n    basePath: string,\n    to: 'src/' | '',\n    from: 'src/app/' | 'app/' | '',\n  ): string | undefined => {\n    for (const fileExtension of fileExtensions) {\n      if (existsSync(path.join(basePath, `middleware.${fileExtension}`))) {\n        return suggestionMessage(fileExtension, to, from);\n      }\n    }\n    return undefined;\n  };\n\n  if (existsSync(projectWithAppSrcPath)) {\n    return (\n      checkMiddlewareLocation(projectWithAppSrcPath, 'src/', 'src/app/') || checkMiddlewareLocation(cwd(), 'src/', '')\n    );\n  }\n\n  if (existsSync(projectWithAppPath)) {\n    return checkMiddlewareLocation(projectWithAppPath, '', 'app/');\n  }\n\n  return undefined;\n}\n\nexport { suggestMiddlewareLocation, hasSrcAppDir };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gBAAgB,eAAe,uBAAuB;;;AAE/D,SAAS,eAAe;IACtB,MAAM,EAAE,UAAA,CAAW,CAAA,2RAAI,gBAAA,CAAc;IACrC,MAAM,+RAAO,kBAAA,CAAgB;IAC7B,MAAM,UAAM,qSAAA,CAAe;IAE3B,MAAM,oBAAoB,KAAK,IAAA,CAAK,IAAI,GAAG,OAAO,KAAK;IAEvD,OAAO,CAAC,CAAC,WAAW,iBAAiB;AACvC;AAEA,SAAS,4BAA4B;IACnC,MAAM,iBAAiB;QAAC;QAAM,IAAI;KAAA;IAClC,MAAM,oBAAoB,CACxB,WACA,IACA,OAEA,CAAA,8GAAA,EAAiH,EAAE,CAAA,WAAA,EAAc,SAAS,CAAA,yBAAA,EAA4B,IAAI,CAAA,WAAA,EAAc,SAAS,EAAA;IAEnM,MAAM,EAAE,UAAA,CAAW,CAAA,2RAAI,gBAAA,CAAc;IACrC,MAAM,+RAAO,kBAAA,CAAgB;IAC7B,MAAM,OAAM,wSAAA,CAAe;IAE3B,MAAM,wBAAwB,KAAK,IAAA,CAAK,IAAI,GAAG,OAAO,KAAK;IAC3D,MAAM,qBAAqB,KAAK,IAAA,CAAK,IAAI,GAAG,KAAK;IAEjD,MAAM,0BAA0B,CAC9B,UACA,IACA,SACuB;QACvB,KAAA,MAAW,iBAAiB,eAAgB;YAC1C,IAAI,WAAW,KAAK,IAAA,CAAK,UAAU,CAAA,WAAA,EAAc,aAAa,EAAE,CAAC,GAAG;gBAClE,OAAO,kBAAkB,eAAe,IAAI,IAAI;YAClD;QACF;QACA,OAAO,KAAA;IACT;IAEA,IAAI,WAAW,qBAAqB,GAAG;QACrC,OACE,wBAAwB,uBAAuB,QAAQ,UAAU,KAAK,wBAAwB,IAAI,GAAG,QAAQ,EAAE;IAEnH;IAEA,IAAI,WAAW,kBAAkB,GAAG;QAClC,OAAO,wBAAwB,oBAAoB,IAAI,MAAM;IAC/D;IAEA,OAAO,KAAA;AACT", "ignoreList": [0], "debugId": null}}]}