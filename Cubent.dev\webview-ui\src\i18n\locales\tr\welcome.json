{"greeting": "<PERSON><PERSON><PERSON><PERSON>, ben <PERSON>!", "introduction": "<strong><PERSON><PERSON>, önde gelen otonom kodlama aracıdır.</strong> <PERSON>ha önce hiç görmediğin şekilde mimari tasarım ya<PERSON>, kod yazmaya, hata ayıklamaya ve üretkenliğini artırmaya hazırlan. Devam etmek için Roo Code'un bir API anahtarına ihtiyacı var.", "notice": "Başlamak için bu eklentinin bir API sağlayıcısına ihtiyacı var.", "start": "<PERSON>i ba<PERSON>alım!", "chooseProvider": "Başlamak için bir API sağlayıcısı seç:", "routers": {"requesty": {"description": "Optimize edilmiş LLM yönlendiricin", "incentive": "$1 ücretsiz kredi"}, "openrouter": {"description": "LLM'ler i<PERSON> bi<PERSON> bir arayüz"}}, "startRouter": "Yönlendirici Üzerinden Hızlı Kurulum", "startCustom": "Kendi API Anahtarını Kullan", "telemetry": {"title": "Roo Code'u Geliştirmeye Yardım Et", "anonymousTelemetry": "Hataları düzeltmemize ve eklentiyi geliştirmemize yardımcı olmak için anonim hata ve kullanım verileri gönder. <PERSON>ç<PERSON>aman kod, metin veya kişisel bilgi gönderilmez.", "changeSettings": "<PERSON><PERSON><PERSON> her zaman <settingsLink>ayar<PERSON></settingsLink>ın altından değiştirebilirsin", "settings": "<PERSON><PERSON><PERSON>", "allow": "<PERSON><PERSON> V<PERSON>", "deny": "<PERSON><PERSON>"}, "or": "veya", "importSettings": "Ayarları İçe Aktar"}