{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6VAAC,8QAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6VAAC,8QAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6VAAC,6QAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6VAAC,6QAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6VAAC,6QAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6VAAC,6QAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/profile/usage/components/usage-chart.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON> } from 'recharts';\n\ninterface UsageMetric {\n  date: Date;\n  cubentUnitsUsed: number;\n  requestsMade: number;\n}\n\ninterface UsageChartProps {\n  data: UsageMetric[];\n}\n\nexport function UsageChart({ data }: UsageChartProps) {\n  const chartData = useMemo(() => {\n    // Fill in missing days with zero values for the last 30 days\n    const today = new Date();\n    const thirtyDaysAgo = new Date(today);\n    thirtyDaysAgo.setDate(today.getDate() - 29);\n\n    const filledData = [];\n    for (let i = 0; i < 30; i++) {\n      const currentDate = new Date(thirtyDaysAgo);\n      currentDate.setDate(thirtyDaysAgo.getDate() + i);\n\n      const existingData = data.find(metric => {\n        const metricDate = new Date(metric.date);\n        return metricDate.toDateString() === currentDate.toDateString();\n      });\n\n      filledData.push({\n        date: currentDate.toLocaleDateString('en-US', {\n          month: 'short',\n          day: 'numeric'\n        }),\n        fullDate: currentDate.toLocaleDateString('en-US', {\n          weekday: 'short',\n          month: 'short',\n          day: 'numeric'\n        }),\n        cubentUnits: existingData?.cubentUnitsUsed || 0,\n        requests: existingData?.requestsMade || 0,\n      });\n    }\n\n    return filledData;\n  }, [data]);\n\n  // Custom tooltip component\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <div className=\"bg-gray-900 dark:bg-gray-800 text-white p-3 rounded-lg shadow-lg border border-gray-700\">\n          <p className=\"font-medium text-center mb-2\">{data.fullDate}</p>\n          <div className=\"space-y-1\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-3 h-3 bg-blue-500 rounded\"></div>\n              <span className=\"text-sm\">{data.cubentUnits.toFixed(2)} Cubent Units</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-3 h-3 bg-green-500 rounded\"></div>\n              <span className=\"text-sm\">{data.requests} Messages</span>\n            </div>\n          </div>\n        </div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"h-80 w-full\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <BarChart\n            data={chartData}\n            margin={{\n              top: 20,\n              right: 30,\n              left: 20,\n              bottom: 60,\n            }}\n          >\n            <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n            <XAxis\n              dataKey=\"date\"\n              tick={{ fontSize: 12 }}\n              angle={-45}\n              textAnchor=\"end\"\n              height={60}\n              interval={Math.floor(chartData.length / 8)} // Show every ~4th label to avoid crowding\n            />\n            <YAxis\n              tick={{ fontSize: 12 }}\n              label={{ value: 'Usage', angle: -90, position: 'insideLeft' }}\n            />\n            <Tooltip content={<CustomTooltip />} />\n            <Legend />\n            <Bar\n              dataKey=\"cubentUnits\"\n              name=\"Cubent Units\"\n              fill=\"#3b82f6\"\n              radius={[2, 2, 0, 0]}\n            />\n            <Bar\n              dataKey=\"requests\"\n              name=\"Messages\"\n              fill=\"#10b981\"\n              radius={[2, 2, 0, 0]}\n            />\n          </BarChart>\n        </ResponsiveContainer>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAeO,SAAS,WAAW,EAAE,IAAI,EAAmB;IAClD,MAAM,YAAY,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QACxB,6DAA6D;QAC7D,MAAM,QAAQ,IAAI;QAClB,MAAM,gBAAgB,IAAI,KAAK;QAC/B,cAAc,OAAO,CAAC,MAAM,OAAO,KAAK;QAExC,MAAM,aAAa,EAAE;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,MAAM,cAAc,IAAI,KAAK;YAC7B,YAAY,OAAO,CAAC,cAAc,OAAO,KAAK;YAE9C,MAAM,eAAe,KAAK,IAAI,CAAC,CAAA;gBAC7B,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;gBACvC,OAAO,WAAW,YAAY,OAAO,YAAY,YAAY;YAC/D;YAEA,WAAW,IAAI,CAAC;gBACd,MAAM,YAAY,kBAAkB,CAAC,SAAS;oBAC5C,OAAO;oBACP,KAAK;gBACP;gBACA,UAAU,YAAY,kBAAkB,CAAC,SAAS;oBAChD,SAAS;oBACT,OAAO;oBACP,KAAK;gBACP;gBACA,aAAa,cAAc,mBAAmB;gBAC9C,UAAU,cAAc,gBAAgB;YAC1C;QACF;QAEA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAE,WAAU;kCAAgC,KAAK,QAAQ;;;;;;kCAC1D,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;;;;;kDACf,6VAAC;wCAAK,WAAU;;4CAAW,KAAK,WAAW,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAEzD,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;;;;;kDACf,6VAAC;wCAAK,WAAU;;4CAAW,KAAK,QAAQ;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;QAKnD;QACA,OAAO;IACT;IAEA,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC,sSAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAO,QAAO;0BACvC,cAAA,6VAAC,uRAAA,CAAA,WAAQ;oBACP,MAAM;oBACN,QAAQ;wBACN,KAAK;wBACL,OAAO;wBACP,MAAM;wBACN,QAAQ;oBACV;;sCAEA,6VAAC,gSAAA,CAAA,gBAAa;4BAAC,iBAAgB;4BAAM,WAAU;;;;;;sCAC/C,6VAAC,wRAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,MAAM;gCAAE,UAAU;4BAAG;4BACrB,OAAO,CAAC;4BACR,YAAW;4BACX,QAAQ;4BACR,UAAU,KAAK,KAAK,CAAC,UAAU,MAAM,GAAG;;;;;;sCAE1C,6VAAC,wRAAA,CAAA,QAAK;4BACJ,MAAM;gCAAE,UAAU;4BAAG;4BACrB,OAAO;gCAAE,OAAO;gCAAS,OAAO,CAAC;gCAAI,UAAU;4BAAa;;;;;;sCAE9D,6VAAC,0RAAA,CAAA,UAAO;4BAAC,uBAAS,6VAAC;;;;;;;;;;sCACnB,6VAAC,yRAAA,CAAA,SAAM;;;;;sCACP,6VAAC,sRAAA,CAAA,MAAG;4BACF,SAAQ;4BACR,MAAK;4BACL,MAAK;4BACL,QAAQ;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE;;;;;;sCAEtB,6VAAC,sRAAA,CAAA,MAAG;4BACF,SAAQ;4BACR,MAAK;4BACL,MAAK;4BACL,QAAQ;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/profile/usage/components/usage-analytics.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@repo/design-system/components/ui/card';\nimport { Badge } from '@repo/design-system/components/ui/badge';\nimport { Progress } from '@repo/design-system/components/ui/progress';\nimport { Avatar, AvatarFallback, AvatarImage } from '@repo/design-system/components/ui/avatar';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/design-system/components/ui/tabs';\nimport {\n  ArrowLeft,\n  Zap,\n  MessageSquare,\n  TrendingUp,\n  RefreshCw,\n  Crown,\n  Sparkles,\n  Download\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { UsageChart } from './usage-chart';\n\ninterface UsageData {\n  totalCubentUnits: number;\n  totalMessages: number;\n  userLimit: number;\n  subscriptionTier: string;\n  chartData: Array<{\n    date: Date;\n    cubentUnitsUsed: number;\n    requestsMade: number;\n  }>;\n  user: {\n    name: string;\n    email: string;\n    picture?: string;\n  };\n}\n\ninterface UsageAnalyticsProps {\n  initialData: UsageData;\n}\n\nexport function UsageAnalytics({ initialData }: UsageAnalyticsProps) {\n  const [data, setData] = useState(initialData);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n\n  const refreshData = async () => {\n    setIsRefreshing(true);\n    try {\n      const response = await fetch('/api/extension/usage/stats', {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        if (result.success) {\n          setData(prev => ({\n            ...prev,\n            totalCubentUnits: result.totalCubentUnits,\n            totalMessages: result.totalMessages,\n            userLimit: result.userLimit,\n            subscriptionTier: result.subscriptionTier,\n          }));\n          setLastUpdated(new Date());\n        }\n      }\n    } catch (error) {\n      console.error('Failed to refresh usage data:', error);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  // Auto-refresh every 30 seconds\n  useEffect(() => {\n    const interval = setInterval(refreshData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const usagePercentage = (data.totalCubentUnits / data.userLimit) * 100;\n  const isNearLimit = usagePercentage > 80;\n  const isOverLimit = usagePercentage > 100;\n\n  const getTierInfo = (tier: string) => {\n    switch (tier) {\n      case 'pro':\n        return { name: 'Pro', icon: Crown, color: 'text-yellow-600' };\n      case 'premium':\n        return { name: 'Premium', icon: Sparkles, color: 'text-purple-600' };\n      default:\n        return { name: 'Free Trial', icon: Zap, color: 'text-blue-600' };\n    }\n  };\n\n  const tierInfo = getTierInfo(data.subscriptionTier);\n  const TierIcon = tierInfo.icon;\n\n  return (\n    <>\n      {/* Shadcn-Admin Style Header */}\n      <div className=\"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12\">\n        <div className=\"flex items-center gap-2 px-4\">\n          <Button variant=\"ghost\" size=\"sm\" asChild>\n            <Link href=\"/profile\">\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Profile\n            </Link>\n          </Button>\n        </div>\n        <div className=\"ml-auto flex items-center gap-2 px-4\">\n          <div className=\"text-right\">\n            <p className=\"text-xs text-muted-foreground\">Last updated</p>\n            <p className=\"text-sm font-medium\">\n              {lastUpdated.toLocaleTimeString()}\n            </p>\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={refreshData}\n            disabled={isRefreshing}\n          >\n            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />\n            Refresh\n          </Button>\n          <Button size=\"sm\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Download\n          </Button>\n        </div>\n      </div>\n\n      {/* Shadcn-Admin Style Main Content */}\n      <main className=\"flex flex-1 flex-col gap-4 p-4 pt-0\">\n        <div className=\"flex items-center justify-between space-y-2\">\n          <div>\n            <h2 className=\"text-2xl font-bold tracking-tight\">Usage Analytics</h2>\n            <p className=\"text-muted-foreground\">\n              Here's what happening with your usage today\n            </p>\n          </div>\n        </div>\n\n        {/* Shadcn-Admin Style Stats Cards */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n          {/* Cubent Units Card */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Total Cubent Units\n              </CardTitle>\n              <Zap className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.totalCubentUnits.toFixed(2)}</div>\n              <div className=\"flex items-center space-x-2 text-xs text-muted-foreground\">\n                <span className={`font-medium ${isOverLimit ? 'text-red-500' : isNearLimit ? 'text-yellow-500' : 'text-green-500'}`}>\n                  {usagePercentage.toFixed(0)}%\n                </span>\n                <span>of {data.userLimit} limit</span>\n              </div>\n              <Progress\n                value={Math.min(usagePercentage, 100)}\n                className=\"mt-2 h-1\"\n              />\n            </CardContent>\n          </Card>\n\n          {/* Messages Card */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Messages</CardTitle>\n              <MessageSquare className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.totalMessages.toLocaleString()}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +{Math.round((data.totalMessages / 30) * 7)} this week\n              </p>\n            </CardContent>\n          </Card>\n\n          {/* Efficiency Card */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Efficiency</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {data.totalMessages > 0 ? (data.totalCubentUnits / data.totalMessages).toFixed(2) : '0.00'}\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                units per message\n              </p>\n            </CardContent>\n          </Card>\n\n          {/* Subscription Card */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Subscription</CardTitle>\n              <TierIcon className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.userLimit}</div>\n              <div className=\"flex items-center space-x-2 text-xs text-muted-foreground\">\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  <TierIcon className={`h-3 w-3 mr-1 ${tierInfo.color}`} />\n                  {tierInfo.name}\n                </Badge>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Shadcn-Admin Style Chart with Tabs */}\n        <Tabs defaultValue=\"overview\" className=\"space-y-4\">\n          <TabsList>\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n            <TabsTrigger value=\"analytics\">Analytics</TabsTrigger>\n            <TabsTrigger value=\"reports\">Reports</TabsTrigger>\n          </TabsList>\n          <TabsContent value=\"overview\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Usage Overview</CardTitle>\n                <CardDescription>\n                  Daily consumption for the last 30 days\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"pl-2\">\n                <UsageChart data={data.chartData} />\n              </CardContent>\n            </Card>\n          </TabsContent>\n          <TabsContent value=\"analytics\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Detailed Analytics</CardTitle>\n                <CardDescription>\n                  Advanced usage metrics and trends\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  Advanced analytics coming soon...\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n          <TabsContent value=\"reports\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Usage Reports</CardTitle>\n                <CardDescription>\n                  Export and download usage reports\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  Report generation coming soon...\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n\n        {/* Shadcn-Admin Style Upgrade Prompt */}\n        {(isNearLimit || isOverLimit) && data.subscriptionTier === 'free_trial' && (\n          <Card className=\"border-yellow-200 bg-yellow-50 dark:border-yellow-800/50 dark:bg-yellow-900/10\">\n            <CardContent className=\"flex items-center justify-between p-6\">\n              <div className=\"flex items-center gap-4\">\n                <div className=\"flex h-12 w-12 items-center justify-center rounded-xl bg-yellow-100 dark:bg-yellow-900/30\">\n                  <Crown className=\"h-6 w-6 text-yellow-600 dark:text-yellow-400\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold\">\n                    {isOverLimit ? 'Usage Limit Exceeded' : 'Approaching Usage Limit'}\n                  </h3>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Upgrade to Pro for unlimited Cubent Units and advanced features.\n                  </p>\n                </div>\n              </div>\n              <Button>\n                <Sparkles className=\"h-4 w-4 mr-2\" />\n                Upgrade Now\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n      </main>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AApBA;;;;;;;;;;;AA2CO,SAAS,eAAe,EAAE,WAAW,EAAuB;IACjE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEnD,MAAM,cAAc;QAClB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,IAAI,OAAO,OAAO,EAAE;oBAClB,QAAQ,CAAA,OAAQ,CAAC;4BACf,GAAG,IAAI;4BACP,kBAAkB,OAAO,gBAAgB;4BACzC,eAAe,OAAO,aAAa;4BACnC,WAAW,OAAO,SAAS;4BAC3B,kBAAkB,OAAO,gBAAgB;wBAC3C,CAAC;oBACD,eAAe,IAAI;gBACrB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,gCAAgC;IAChC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY,aAAa;QAC1C,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,kBAAkB,AAAC,KAAK,gBAAgB,GAAG,KAAK,SAAS,GAAI;IACnE,MAAM,cAAc,kBAAkB;IACtC,MAAM,cAAc,kBAAkB;IAEtC,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAO,MAAM,wRAAA,CAAA,QAAK;oBAAE,OAAO;gBAAkB;YAC9D,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,MAAM,8RAAA,CAAA,WAAQ;oBAAE,OAAO;gBAAkB;YACrE;gBACE,OAAO;oBAAE,MAAM;oBAAc,MAAM,oRAAA,CAAA,MAAG;oBAAE,OAAO;gBAAgB;QACnE;IACF;IAEA,MAAM,WAAW,YAAY,KAAK,gBAAgB;IAClD,MAAM,WAAW,SAAS,IAAI;IAE9B,qBACE;;0BAEE,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC,2JAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,OAAO;sCACvC,cAAA,6VAAC,2QAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,6VAAC,oSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;kCAK5C,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAC7C,6VAAC;wCAAE,WAAU;kDACV,YAAY,kBAAkB;;;;;;;;;;;;0CAGnC,6VAAC,2JAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,6VAAC,oSAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,aAAa,EAAE,eAAe,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;0CAGhF,6VAAC,2JAAA,CAAA,SAAM;gCAAC,MAAK;;kDACX,6VAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6VAAC;gBAAK,WAAU;;kCACd,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;;8CACC,6VAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6VAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAOzC,6VAAC;wBAAI,WAAU;;0CAEb,6VAAC,yJAAA,CAAA,OAAI;;kDACH,6VAAC,yJAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6VAAC,yJAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAG3C,6VAAC,oRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;kDAEjB,6VAAC,yJAAA,CAAA,cAAW;;0DACV,6VAAC;gDAAI,WAAU;0DAAsB,KAAK,gBAAgB,CAAC,OAAO,CAAC;;;;;;0DACnE,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAW,CAAC,YAAY,EAAE,cAAc,iBAAiB,cAAc,oBAAoB,kBAAkB;;4DAChH,gBAAgB,OAAO,CAAC;4DAAG;;;;;;;kEAE9B,6VAAC;;4DAAK;4DAAI,KAAK,SAAS;4DAAC;;;;;;;;;;;;;0DAE3B,6VAAC,6JAAA,CAAA,WAAQ;gDACP,OAAO,KAAK,GAAG,CAAC,iBAAiB;gDACjC,WAAU;;;;;;;;;;;;;;;;;;0CAMhB,6VAAC,yJAAA,CAAA,OAAI;;kDACH,6VAAC,yJAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6VAAC,yJAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6VAAC,4SAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;kDAE3B,6VAAC,yJAAA,CAAA,cAAW;;0DACV,6VAAC;gDAAI,WAAU;0DAAsB,KAAK,aAAa,CAAC,cAAc;;;;;;0DACtE,6VAAC;gDAAE,WAAU;;oDAAgC;oDACzC,KAAK,KAAK,CAAC,AAAC,KAAK,aAAa,GAAG,KAAM;oDAAG;;;;;;;;;;;;;;;;;;;0CAMlD,6VAAC,yJAAA,CAAA,OAAI;;kDACH,6VAAC,yJAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6VAAC,yJAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6VAAC,sSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6VAAC,yJAAA,CAAA,cAAW;;0DACV,6VAAC;gDAAI,WAAU;0DACZ,KAAK,aAAa,GAAG,IAAI,CAAC,KAAK,gBAAgB,GAAG,KAAK,aAAa,EAAE,OAAO,CAAC,KAAK;;;;;;0DAEtF,6VAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAOjD,6VAAC,yJAAA,CAAA,OAAI;;kDACH,6VAAC,yJAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6VAAC,yJAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6VAAC;gDAAS,WAAU;;;;;;;;;;;;kDAEtB,6VAAC,yJAAA,CAAA,cAAW;;0DACV,6VAAC;gDAAI,WAAU;0DAAsB,KAAK,SAAS;;;;;;0DACnD,6VAAC;gDAAI,WAAU;0DACb,cAAA,6VAAC,0JAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;;sEACnC,6VAAC;4DAAS,WAAW,CAAC,aAAa,EAAE,SAAS,KAAK,EAAE;;;;;;wDACpD,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQxB,6VAAC,yJAAA,CAAA,OAAI;wBAAC,cAAa;wBAAW,WAAU;;0CACtC,6VAAC,yJAAA,CAAA,WAAQ;;kDACP,6VAAC,yJAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,6VAAC,yJAAA,CAAA,cAAW;wCAAC,OAAM;kDAAY;;;;;;kDAC/B,6VAAC,yJAAA,CAAA,cAAW;wCAAC,OAAM;kDAAU;;;;;;;;;;;;0CAE/B,6VAAC,yJAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,6VAAC,yJAAA,CAAA,OAAI;;sDACH,6VAAC,yJAAA,CAAA,aAAU;;8DACT,6VAAC,yJAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6VAAC,yJAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6VAAC,yJAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6VAAC,4LAAA,CAAA,aAAU;gDAAC,MAAM,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;0CAItC,6VAAC,yJAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;0CACvC,cAAA,6VAAC,yJAAA,CAAA,OAAI;;sDACH,6VAAC,yJAAA,CAAA,aAAU;;8DACT,6VAAC,yJAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6VAAC,yJAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6VAAC,yJAAA,CAAA,cAAW;sDACV,cAAA,6VAAC;gDAAI,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;0CAM9D,6VAAC,yJAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;0CACrC,cAAA,6VAAC,yJAAA,CAAA,OAAI;;sDACH,6VAAC,yJAAA,CAAA,aAAU;;8DACT,6VAAC,yJAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6VAAC,yJAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,6VAAC,yJAAA,CAAA,cAAW;sDACV,cAAA,6VAAC;gDAAI,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAS/D,CAAC,eAAe,WAAW,KAAK,KAAK,gBAAgB,KAAK,8BACzD,6VAAC,yJAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6VAAC,yJAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6VAAC;;8DACC,6VAAC;oDAAG,WAAU;8DACX,cAAc,yBAAyB;;;;;;8DAE1C,6VAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAKjD,6VAAC,2JAAA,CAAA,SAAM;;sDACL,6VAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}]}