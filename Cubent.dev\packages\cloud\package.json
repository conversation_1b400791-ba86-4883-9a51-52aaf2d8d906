{"name": "@qapt-coder/cloud", "description": "qapt coder Cloud VSCode integration.", "version": "0.0.0", "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "vitest run", "clean": "rimraf dist .turbo"}, "dependencies": {"@qapt-coder/telemetry": "workspace:^", "@qapt-coder/types": "workspace:^", "axios": "^1.7.4", "zod": "^3.24.2"}, "devDependencies": {"@qapt-coder/config-eslint": "workspace:^", "@qapt-coder/config-typescript": "workspace:^", "@types/node": "^22.15.20", "@types/vscode": "^1.84.0", "vitest": "^3.1.3"}}