(()=>{var e={};e.id=6847,e.ids=[6847],e.modules={1359:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(12901),i=r(37838);async function n(){r(1447);let{userId:e}=await (0,i.j)();return e?(await (0,s.$)()).users.getUser(e):null}},1378:(e,t,r)=>{Promise.resolve().then(r.bind(r,86332))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},8963:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=8963,e.exports=t},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12901:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var s=r(8741),i=r(23056),n=r(76315),o=r(97495);let a=new(r(16698)).AsyncLocalStorage;var u=r(60606);let c=async()=>{var e,t;let r;try{let e=await (0,i.TG)(),t=(0,o._b)(e,s.AA.Headers.ClerkRequestData);r=(0,u.Kk)(t)}catch(e){if(e&&(0,i.Sz)(e))throw e}let c=null!=(t=null==(e=a.getStore())?void 0:e.get("requestData"))?t:r;return(null==c?void 0:c.secretKey)||(null==c?void 0:c.publishableKey)?(0,n.n)(c):(0,n.n)({})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},26790:(e,t,r)=>{"use strict";r.d(t,{z:()=>O});var s,i,n,o,a,u,c,l,d,p,h,m,x,g,f,S,q,y,k,w=r(45940);r(92867);var v=r(37081);r(27322),r(6264);var b=r(49530),R=r(57136),E=r(94051),j=class{constructor(){(0,E.VK)(this,n),(0,E.VK)(this,s,"clerk_telemetry_throttler"),(0,E.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,E.S7)(this,n,u))return!1;let t=Date.now(),r=(0,E.jq)(this,n,o).call(this,e),c=(0,E.S7)(this,n,a)?.[r];if(!c){let e={...(0,E.S7)(this,n,a),[r]:t};localStorage.setItem((0,E.S7)(this,s),JSON.stringify(e))}if(c&&t-c>(0,E.S7)(this,i)){let e=(0,E.S7)(this,n,a);delete e[r],localStorage.setItem((0,E.S7)(this,s),JSON.stringify(e))}return!!c}};s=new WeakMap,i=new WeakMap,n=new WeakSet,o=function(e){let{sk:t,pk:r,payload:s,...i}=e,n={...s,...i};return JSON.stringify(Object.keys({...s,...i}).sort().map(e=>n[e]))},a=function(){let e=localStorage.getItem((0,E.S7)(this,s));return e?JSON.parse(e):{}},u=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,E.S7)(this,s)),!1}};var K={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},V=class{constructor(e){(0,E.VK)(this,m),(0,E.VK)(this,c),(0,E.VK)(this,l),(0,E.VK)(this,d,{}),(0,E.VK)(this,p,[]),(0,E.VK)(this,h),(0,E.OV)(this,c,{maxBufferSize:e.maxBufferSize??K.maxBufferSize,samplingRate:e.samplingRate??K.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:K.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,E.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,E.S7)(this,d).clerkVersion="",(0,E.S7)(this,d).sdk=e.sdk,(0,E.S7)(this,d).sdkVersion=e.sdkVersion,(0,E.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,R.q5)(e.publishableKey);t&&((0,E.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,E.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,E.OV)(this,l,new j)}get isEnabled(){return!("development"!==(0,E.S7)(this,d).instanceType||(0,E.S7)(this,c).disabled||"undefined"!=typeof process&&(0,b.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,E.S7)(this,c).debug||"undefined"!=typeof process&&(0,b.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,E.jq)(this,m,k).call(this,e.event,e.payload);(0,E.jq)(this,m,q).call(this,t.event,t),(0,E.jq)(this,m,x).call(this,t,e.eventSamplingRate)&&((0,E.S7)(this,p).push(t),(0,E.jq)(this,m,f).call(this))}};c=new WeakMap,l=new WeakMap,d=new WeakMap,p=new WeakMap,h=new WeakMap,m=new WeakSet,x=function(e,t){return this.isEnabled&&!this.isDebug&&(0,E.jq)(this,m,g).call(this,e,t)},g=function(e,t){let r=Math.random();return!!(r<=(0,E.S7)(this,c).samplingRate&&(void 0===t||r<=t))&&!(0,E.S7)(this,l).isEventThrottled(e)},f=function(){if("undefined"==typeof window)return void(0,E.jq)(this,m,S).call(this);if((0,E.S7)(this,p).length>=(0,E.S7)(this,c).maxBufferSize){(0,E.S7)(this,h)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,E.S7)(this,h)),(0,E.jq)(this,m,S).call(this);return}(0,E.S7)(this,h)||("requestIdleCallback"in window?(0,E.OV)(this,h,requestIdleCallback(()=>{(0,E.jq)(this,m,S).call(this)})):(0,E.OV)(this,h,setTimeout(()=>{(0,E.jq)(this,m,S).call(this)},0)))},S=function(){fetch(new URL("/v1/event",(0,E.S7)(this,c).endpoint),{method:"POST",body:JSON.stringify({events:(0,E.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,E.OV)(this,p,[])}).catch(()=>void 0)},q=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},y=function(){let e={name:(0,E.S7)(this,d).sdk,version:(0,E.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},k=function(e,t){let r=(0,E.jq)(this,m,y).call(this);return{event:e,cv:(0,E.S7)(this,d).clerkVersion??"",it:(0,E.S7)(this,d).instanceType??"",sdk:r.name,sdkv:r.version,...(0,E.S7)(this,d).publishableKey?{pk:(0,E.S7)(this,d).publishableKey}:{},...(0,E.S7)(this,d).secretKey?{sk:(0,E.S7)(this,d).secretKey}:{},payload:t}};function O(e){let t={...e},r=(0,w.y3)(t),s=(0,w.Bs)({options:t,apiClient:r}),i=new V({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...s,telemetry:i}}(0,v.C)(w.nr)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67098:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,21034,23))},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74433:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});var s=r(4520),i=r(76741);let n=e=>{let t="An error occurred";t=e instanceof Error||e&&"object"==typeof e&&"message"in e?e.message:String(e);try{(0,s.captureException)(e),i.R.error(`Parsing error: ${t}`)}catch(e){console.error("Error parsing error:",e)}return t}},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76315:(e,t,r)=>{"use strict";r.d(t,{n:()=>o});var s=r(26790),i=r(54726);let n={secretKey:i.rB,publishableKey:i.At,apiUrl:i.H$,apiVersion:i.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i.Rg,domain:i.V2,isSatellite:i.fS,sdkMetadata:i.tm,telemetry:{disabled:i.nN,debug:i.Mh}},o=e=>(0,s.z)({...n,...e})},76741:(e,t,r)=>{"use strict";r.d(t,{R:()=>s});let s=r(42870).log},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77470:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>g,serverHooks:()=>q,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>S});var s={};r.r(s),r.d(s,{POST:()=>x});var i=r(26142),n=r(94327),o=r(34862),a=r(37838),u=r(1359),c=r(18815),l=r(74433),d=r(76741),p=r(26239),h=r(25);let m=h.z.object({deviceId:h.z.string().min(1,"Device ID is required"),state:h.z.string().min(1,"State parameter is required"),acceptTerms:h.z.boolean().optional()}),x=async e=>{try{let{userId:t}=await (0,a.j)(),s=await (0,u.N)();if(!t||!s)return p.NextResponse.json({error:"Unauthorized",message:"User not authenticated"},{status:401});let i=await e.json(),{deviceId:n,state:o,acceptTerms:l}=m.parse(i),h=await c.database.user.upsert({where:{clerkId:t},update:{email:s.emailAddresses[0]?.emailAddress||"",name:`${s.firstName||""} ${s.lastName||""}`.trim()||null,picture:s.imageUrl},create:{clerkId:t,email:s.emailAddresses[0]?.emailAddress||"",name:`${s.firstName||""} ${s.lastName||""}`.trim()||null,picture:s.imageUrl}});if(l&&!h.termsAccepted&&(h=await c.database.user.update({where:{id:h.id},data:{termsAccepted:!0,termsAcceptedAt:new Date}})),!(h.termsAccepted||l))return p.NextResponse.json({error:"Terms not accepted",message:"User must accept terms to continue"},{status:400});let{randomBytes:x}=await Promise.resolve().then(r.t.bind(r,55511,23)),g=`cubent_ext_${x(32).toString("hex")}`,f=new Date(Date.now()+6e5);await c.database.pendingLogin.deleteMany({where:{OR:[{deviceId:n},{expiresAt:{lt:new Date}}]}}),await c.database.pendingLogin.create({data:{deviceId:n,state:o,token:g,userId:t,expiresAt:f}}),d.R.info("Extension login successful",{userId:h.id,deviceId:n.slice(0,8)+"...",email:h.email});let S=`vscode://cubent.cubent/auth/callback?token=${encodeURIComponent(g)}&state=${encodeURIComponent(o)}`;return p.NextResponse.json({success:!0,token:g,redirectUrl:S,message:"Login successful"})}catch(t){let e=(0,l.u)(t);if(d.R.error("Extension login failed",{error:e}),t instanceof h.z.ZodError)return p.NextResponse.json({error:"Validation error",message:t.errors[0]?.message||"Invalid input"},{status:400});return p.NextResponse.json({error:"Internal server error",message:"Login failed"},{status:500})}},g=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/login/route",pathname:"/api/extension/login",filename:"route",bundlePath:"app/api/extension/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\login\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:S,serverHooks:q}=g;function y(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:S})}},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,903,7838,5480,3319,864],()=>r(77470));module.exports=s})();