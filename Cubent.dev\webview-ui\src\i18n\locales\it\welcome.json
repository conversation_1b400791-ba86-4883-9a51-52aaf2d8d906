{"greeting": "<PERSON><PERSON><PERSON>, sono Roo!", "introduction": "<strong>Roo Code è il principale agente di codifica autonomo.</strong> Preparati ad architettare, codificare, debuggare e aumentare la tua produttività come mai prima d'ora. Per continuare, Roo Code richiede una chiave API.", "notice": "Per iniziare, questa estensione necessita di un fornitore di API.", "start": "Andiamo!", "chooseProvider": "Scegli un fornitore di API per iniziare:", "routers": {"requesty": {"description": "Il tuo router LLM o<PERSON>to", "incentive": "$1 di credito gratuito"}, "openrouter": {"description": "Un'interfaccia unificata per LLMs"}}, "startRouter": "Configurazione rapida tramite router", "startCustom": "Usa la tua chiave API", "telemetry": {"title": "<PERSON><PERSON> a migliorare Roo Code", "anonymousTelemetry": "Invia dati di utilizzo ed errori anonimi per aiutarci a correggere bug e migliorare l'estensione. Non viene mai inviato codice, testo o informazioni personali.", "changeSettings": "Puoi sempre cambiare questo in fondo alle <settingsLink>impostazioni</settingsLink>", "settings": "impostazioni", "allow": "<PERSON><PERSON><PERSON>", "deny": "Nega"}, "or": "o", "importSettings": "Importa impostazioni"}