import React from "react"
import styled from "styled-components"

const BrandingContainer = styled.div`
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 13px;
	font-weight: 500;
	color: var(--vscode-foreground);
	width: fit-content;
`

const LogoContainer = styled.div`
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: #404040;
	padding: 3px;
`

const LogoSvg = styled.svg`
	width: 24px;
	height: 24px;
	color: white;
`

const BrandText = styled.span`
	font-weight: 600;
	letter-spacing: 0.02em;
`

interface QaptCoderBrandingProps {
	style?: React.CSSProperties
}

export const QaptCoderBranding: React.FC<QaptCoderBrandingProps> = ({ style }) => {
	return (
		<BrandingContainer style={style}>
			<LogoContainer>
				<LogoSvg viewBox="0 0 810 810" fill="none" xmlns="http://www.w3.org/2000/svg">
					<defs>
						<clipPath id="qapt-logo-clip">
							<path d="M 113.9375 115 L 695.9375 115 L 695.9375 695 L 113.9375 695 Z M 113.9375 115 " />
						</clipPath>
					</defs>
					<g clipPath="url(#qapt-logo-clip)">
						<path
							fill="currentColor"
							fillRule="nonzero"
							d="M 616.070312 641.121094 L 113.9375 351.453125 L 113.9375 259.980469 L 210.632812 204.164062 L 695.828125 484.03125 L 695.828125 595.082031 Z M 113.9375 407.566406 L 113.9375 549.898438 L 365.058594 694.867188 L 487.871094 623.96875 Z M 616.167969 382.730469 L 616.167969 259.980469 L 365.058594 115.007812 L 259.179688 176.128906 Z M 616.167969 382.730469"
						/>
					</g>
				</LogoSvg>
			</LogoContainer>
			<BrandText>Qapt Coder</BrandText>
		</BrandingContainer>
	)
}
