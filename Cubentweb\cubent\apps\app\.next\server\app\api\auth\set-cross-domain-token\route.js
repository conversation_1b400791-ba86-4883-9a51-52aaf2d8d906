const CHUNK_PUBLIC_PATH = "server/app/api/auth/set-cross-domain-token/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_ddbbb7a7._.js");
runtime.loadChunk("server/chunks/661a5_next_30d4f373._.js");
runtime.loadChunk("server/chunks/eec21_@clerk_shared_dist_82249b3e._.js");
runtime.loadChunk("server/chunks/c67f4_@clerk_backend_dist_70017730._.js");
runtime.loadChunk("server/chunks/25c57_@clerk_nextjs_dist_esm_dff10e14._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_11ae000b._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__770c7990._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/app/.next-internal/server/app/api/auth/set-cross-domain-token/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/app/app/api/auth/set-cross-domain-token/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/app/app/api/auth/set-cross-domain-token/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
