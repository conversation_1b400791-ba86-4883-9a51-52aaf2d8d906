(()=>{var e={};e.id=9663,e.ids=[9663],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21514:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>q});var r={};s.r(r),s.d(r,{GET:()=>p,POST:()=>d});var o=s(26142),i=s(94327),n=s(34862),u=s(37838),a=s(18815),c=s(26239);async function p(){try{let{userId:e}=await (0,u.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await a.database.user.findUnique({where:{clerkId:e}});if(!t)return c.NextResponse.json({error:"User not found"},{status:404});let s=new Date,r=new Date(s.getFullYear(),s.getMonth(),1),o=await a.database.usageMetrics.aggregate({where:{userId:t.id,date:{gte:r}},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),i=t.subscriptionTier||"FREE",n={FREE:{name:"Free",monthlyTokenLimit:1e4,monthlyRequestLimit:100,monthlyCostLimit:5,features:["Basic AI assistance","Limited models"]},PRO:{name:"Pro",monthlyTokenLimit:1e5,monthlyRequestLimit:1e3,monthlyCostLimit:50,features:["Advanced AI assistance","All models","Priority support"]},ENTERPRISE:{name:"Enterprise",monthlyTokenLimit:-1,monthlyRequestLimit:-1,monthlyCostLimit:-1,features:["Unlimited usage","Custom models","Dedicated support"]}}[i],p={subscription:{tier:i,status:t.subscriptionStatus||"ACTIVE",name:n.name,features:n.features,limits:{monthlyTokenLimit:n.monthlyTokenLimit,monthlyRequestLimit:n.monthlyRequestLimit,monthlyCostLimit:n.monthlyCostLimit},billingPeriod:{start:r,end:new Date(s.getFullYear(),s.getMonth()+1,0)}},usage:{current:{tokensUsed:o._sum.tokensUsed||0,requestsMade:o._sum.requestsMade||0,costAccrued:o._sum.costAccrued||0},percentages:{tokens:n.monthlyTokenLimit>0?Math.round((o._sum.tokensUsed||0)/n.monthlyTokenLimit*100):0,requests:n.monthlyRequestLimit>0?Math.round((o._sum.requestsMade||0)/n.monthlyRequestLimit*100):0,cost:n.monthlyCostLimit>0?Math.round((o._sum.costAccrued||0)/n.monthlyCostLimit*100):0}},canUpgrade:"ENTERPRISE"!==i,upgradeUrl:"FREE"===i?"/pricing":"/billing"};return c.NextResponse.json(p)}catch(e){return console.error("Subscription fetch error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let{userId:t}=await (0,u.j)();if(!t)return c.NextResponse.json({error:"Unauthorized"},{status:401});let{tokensRequested:s=0,estimatedCost:r=0}=await e.json(),o=await a.database.user.findUnique({where:{clerkId:t}});if(!o)return c.NextResponse.json({error:"User not found"},{status:404});let i=o.subscriptionTier||"FREE",n=new Date,p=new Date(n.getFullYear(),n.getMonth(),1),d=await a.database.usageMetrics.aggregate({where:{userId:o.id,date:{gte:p}},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),m={FREE:{tokens:1e4,requests:100,cost:5},PRO:{tokens:1e5,requests:1e3,cost:50},ENTERPRISE:{tokens:-1,requests:-1,cost:-1}}[i],l={tokens:d._sum.tokensUsed||0,requests:d._sum.requestsMade||0,cost:d._sum.costAccrued||0},q={tokens:-1===m.tokens||l.tokens+s<=m.tokens,requests:-1===m.requests||l.requests+1<=m.requests,cost:-1===m.cost||l.cost+r<=m.cost},x=q.tokens&&q.requests&&q.cost,h="";return q.tokens?q.requests?q.cost||(h="Monthly cost limit exceeded"):h="Monthly request limit exceeded":h="Monthly token limit exceeded",c.NextResponse.json({allowed:x,reason:x?null:h,limits:m,currentUsage:l,remaining:{tokens:-1===m.tokens?-1:Math.max(0,m.tokens-l.tokens),requests:-1===m.requests?-1:Math.max(0,m.requests-l.requests),cost:-1===m.cost?-1:Math.max(0,m.cost-l.cost)},upgradeRequired:!x&&"ENTERPRISE"!==i})}catch(e){return console.error("Subscription check error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/subscription/route",pathname:"/api/extension/subscription",filename:"route",bundlePath:"app/api/extension/subscription/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\subscription\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:q,serverHooks:x}=m;function h(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:q})}},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>s(21514));module.exports=r})();