(()=>{var e={};e.id=6465,e.ids=[6465],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45002:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>w,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>y,PATCH:()=>m,POST:()=>x});var i=r(26142),a=r(94327),n=r(34862),o=r(37838),u=r(18815),p=r(26239),d=r(25),c=r(55511);let l=d.z.object({name:d.z.string().min(1).max(100),description:d.z.string().max(500).optional(),expiresAt:d.z.string().datetime().optional(),permissions:d.z.array(d.z.string()).default(["read","write"])});async function x(e){try{let{userId:t}=await (0,o.j)();if(!t)return p.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=l.safeParse(r);if(!s.success)return p.NextResponse.json({error:"Invalid API key data",details:s.error.errors},{status:400});let{name:i,description:a,expiresAt:n,permissions:d}=s.data,x=await u.database.user.findUnique({where:{clerkId:t}});if(!x)return p.NextResponse.json({error:"User not found"},{status:404});if(await u.database.apiKey.count({where:{userId:x.id,isActive:!0}})>=10)return p.NextResponse.json({error:"Maximum number of API keys reached (10)"},{status:400});let y=`cubent_${(0,c.randomBytes)(32).toString("hex")}`,m=await A(y),w=await u.database.apiKey.create({data:{userId:x.id,name:i,description:a,keyHash:m,permissions:d,expiresAt:n?new Date(n):null,isActive:!0,lastUsedAt:null,usageCount:0}});return p.NextResponse.json({success:!0,apiKey:{id:w.id,name:w.name,description:w.description,key:y,permissions:w.permissions,expiresAt:w.expiresAt,createdAt:w.createdAt},warning:"This API key will only be shown once. Please save it securely."})}catch(e){return console.error("API key creation error:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(){try{let{userId:e}=await (0,o.j)();if(!e)return p.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return p.NextResponse.json({error:"User not found"},{status:404});let r=await u.database.apiKey.findMany({where:{userId:t.id},orderBy:{createdAt:"desc"},select:{id:!0,name:!0,description:!0,permissions:!0,isActive:!0,expiresAt:!0,lastUsedAt:!0,usageCount:!0,createdAt:!0}}),s=r.filter(e=>e.isActive),i=r.filter(e=>e.expiresAt&&e.expiresAt<new Date);return p.NextResponse.json({apiKeys:r.map(e=>({...e,keyPreview:"cubent_****...****",isExpired:!!e.expiresAt&&e.expiresAt<new Date})),summary:{totalKeys:r.length,activeKeys:s.length,expiredKeys:i.length,remainingSlots:Math.max(0,10-s.length)}})}catch(e){return console.error("API keys fetch error:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e){try{let{userId:t}=await (0,o.j)();if(!t)return p.NextResponse.json({error:"Unauthorized"},{status:401});let{keyId:r,action:s,name:i,description:a,isActive:n}=await e.json();if(!r||!s)return p.NextResponse.json({error:"Key ID and action required"},{status:400});let d=await u.database.user.findUnique({where:{clerkId:t}});if(!d)return p.NextResponse.json({error:"User not found"},{status:404});switch(s){case"update":let l={};i&&(l.name=i),void 0!==a&&(l.description=a),"boolean"==typeof n&&(l.isActive=n);let x=await u.database.apiKey.update({where:{id:r,userId:d.id},data:l,select:{id:!0,name:!0,description:!0,permissions:!0,isActive:!0,expiresAt:!0,lastUsedAt:!0,usageCount:!0,createdAt:!0}});return p.NextResponse.json({success:!0,apiKey:x,message:"API key updated successfully"});case"delete":return await u.database.apiKey.delete({where:{id:r,userId:d.id}}),p.NextResponse.json({success:!0,message:"API key deleted successfully"});case"regenerate":await u.database.apiKey.update({where:{id:r,userId:d.id},data:{isActive:!1}});let y=await u.database.apiKey.findUnique({where:{id:r},select:{name:!0,description:!0,permissions:!0,expiresAt:!0}});if(!y)return p.NextResponse.json({error:"API key not found"},{status:404});let m=`cubent_${(0,c.randomBytes)(32).toString("hex")}`,w=await A(m),f=await u.database.apiKey.create({data:{userId:d.id,name:y.name,description:y.description,keyHash:w,permissions:y.permissions,expiresAt:y.expiresAt,isActive:!0,lastUsedAt:null,usageCount:0}});return p.NextResponse.json({success:!0,apiKey:{id:f.id,name:f.name,description:f.description,key:m,permissions:f.permissions,expiresAt:f.expiresAt,createdAt:f.createdAt},warning:"This new API key will only be shown once. Please save it securely.",message:"API key regenerated successfully"});default:return p.NextResponse.json({error:"Invalid action. Use: update, delete, or regenerate"},{status:400})}}catch(e){return console.error("API key management error:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}async function A(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("")}let w=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/extension/api-keys/route",pathname:"/api/extension/api-keys",filename:"route",bundlePath:"app/api/extension/api-keys/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\api-keys\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:h,serverHooks:g}=w;function j(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:h})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>r(45002));module.exports=s})();