(()=>{var e={};e.id=6599,e.ids=[6599],e.modules={1359:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});var r=s(12901),i=s(37838);async function n(){s(1447);let{userId:e}=await (0,i.j)();return e?(await (0,r.$)()).users.getUser(e):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12901:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(8741),i=s(23056),n=s(76315),a=s(97495);let o=new(s(16698)).AsyncLocalStorage;var u=s(60606);let l=async()=>{var e,t;let s;try{let e=await (0,i.TG)(),t=(0,a._b)(e,r.AA.Headers.ClerkRequestData);s=(0,u.Kk)(t)}catch(e){if(e&&(0,i.Sz)(e))throw e}let l=null!=(t=null==(e=o.getStore())?void 0:e.get("requestData"))?t:s;return(null==l?void 0:l.secretKey)||(null==l?void 0:l.publishableKey)?(0,n.n)(l):(0,n.n)({})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},26790:(e,t,s)=>{"use strict";s.d(t,{z:()=>V});var r,i,n,a,o,u,l,c,d,p,h,f,y,m,S,x,w,b,g,k=s(45940);s(92867);var v=s(37081);s(27322),s(6264);var q=s(49530),A=s(57136),j=s(94051),E=class{constructor(){(0,j.VK)(this,n),(0,j.VK)(this,r,"clerk_telemetry_throttler"),(0,j.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,j.S7)(this,n,u))return!1;let t=Date.now(),s=(0,j.jq)(this,n,a).call(this,e),l=(0,j.S7)(this,n,o)?.[s];if(!l){let e={...(0,j.S7)(this,n,o),[s]:t};localStorage.setItem((0,j.S7)(this,r),JSON.stringify(e))}if(l&&t-l>(0,j.S7)(this,i)){let e=(0,j.S7)(this,n,o);delete e[s],localStorage.setItem((0,j.S7)(this,r),JSON.stringify(e))}return!!l}};r=new WeakMap,i=new WeakMap,n=new WeakSet,a=function(e){let{sk:t,pk:s,payload:r,...i}=e,n={...r,...i};return JSON.stringify(Object.keys({...r,...i}).sort().map(e=>n[e]))},o=function(){let e=localStorage.getItem((0,j.S7)(this,r));return e?JSON.parse(e):{}},u=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,j.S7)(this,r)),!1}};var R={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},K=class{constructor(e){(0,j.VK)(this,f),(0,j.VK)(this,l),(0,j.VK)(this,c),(0,j.VK)(this,d,{}),(0,j.VK)(this,p,[]),(0,j.VK)(this,h),(0,j.OV)(this,l,{maxBufferSize:e.maxBufferSize??R.maxBufferSize,samplingRate:e.samplingRate??R.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:R.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,j.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,j.S7)(this,d).clerkVersion="",(0,j.S7)(this,d).sdk=e.sdk,(0,j.S7)(this,d).sdkVersion=e.sdkVersion,(0,j.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,A.q5)(e.publishableKey);t&&((0,j.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,j.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,j.OV)(this,c,new E)}get isEnabled(){return!("development"!==(0,j.S7)(this,d).instanceType||(0,j.S7)(this,l).disabled||"undefined"!=typeof process&&(0,q.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,j.S7)(this,l).debug||"undefined"!=typeof process&&(0,q.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,j.jq)(this,f,g).call(this,e.event,e.payload);(0,j.jq)(this,f,w).call(this,t.event,t),(0,j.jq)(this,f,y).call(this,t,e.eventSamplingRate)&&((0,j.S7)(this,p).push(t),(0,j.jq)(this,f,S).call(this))}};l=new WeakMap,c=new WeakMap,d=new WeakMap,p=new WeakMap,h=new WeakMap,f=new WeakSet,y=function(e,t){return this.isEnabled&&!this.isDebug&&(0,j.jq)(this,f,m).call(this,e,t)},m=function(e,t){let s=Math.random();return!!(s<=(0,j.S7)(this,l).samplingRate&&(void 0===t||s<=t))&&!(0,j.S7)(this,c).isEventThrottled(e)},S=function(){if("undefined"==typeof window)return void(0,j.jq)(this,f,x).call(this);if((0,j.S7)(this,p).length>=(0,j.S7)(this,l).maxBufferSize){(0,j.S7)(this,h)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,j.S7)(this,h)),(0,j.jq)(this,f,x).call(this);return}(0,j.S7)(this,h)||("requestIdleCallback"in window?(0,j.OV)(this,h,requestIdleCallback(()=>{(0,j.jq)(this,f,x).call(this)})):(0,j.OV)(this,h,setTimeout(()=>{(0,j.jq)(this,f,x).call(this)},0)))},x=function(){fetch(new URL("/v1/event",(0,j.S7)(this,l).endpoint),{method:"POST",body:JSON.stringify({events:(0,j.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,j.OV)(this,p,[])}).catch(()=>void 0)},w=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},b=function(){let e={name:(0,j.S7)(this,d).sdk,version:(0,j.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},g=function(e,t){let s=(0,j.jq)(this,f,b).call(this);return{event:e,cv:(0,j.S7)(this,d).clerkVersion??"",it:(0,j.S7)(this,d).instanceType??"",sdk:s.name,sdkv:s.version,...(0,j.S7)(this,d).publishableKey?{pk:(0,j.S7)(this,d).publishableKey}:{},...(0,j.S7)(this,d).secretKey?{sk:(0,j.S7)(this,d).secretKey}:{},payload:t}};function V(e){let t={...e},s=(0,k.y3)(t),r=(0,k.Bs)({options:t,apiClient:s}),i=new K({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...r,telemetry:i}}(0,v.C)(k.nr)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39112:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>S,routeModule:()=>h,serverHooks:()=>m,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var r={};s.r(r),s.d(r,{GET:()=>d,PATCH:()=>p});var i=s(26142),n=s(94327),a=s(34862),o=s(37838),u=s(1359),l=s(18815),c=s(26239);async function d(){try{let{userId:e}=await (0,o.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await (0,u.N)();if(!t)return c.NextResponse.json({error:"User not found"},{status:404});let s=await l.database.userProfile.findUnique({where:{userId:e}}),r=await l.database.extensionSession.findMany({where:{userId:e,isActive:!0},orderBy:{lastActiveAt:"desc"}});await l.database.usageAnalytics.findMany({where:{userId:e},orderBy:{createdAt:"desc"},take:1}),s||(s=await l.database.userProfile.create({data:{userId:e,email:t.emailAddresses[0]?.emailAddress||"",name:t.fullName||"",subscriptionTier:"FREE",subscriptionStatus:"ACTIVE",termsAccepted:!1}}));let i=await l.database.usageAnalytics.aggregate({where:{userId:e},_sum:{tokensUsed:!0,requestsMade:!0,costAccrued:!0}}),n={user:{id:e,name:t.fullName,email:t.emailAddresses[0]?.emailAddress,imageUrl:t.imageUrl},profile:{subscriptionTier:s.subscriptionTier,subscriptionStatus:s.subscriptionStatus,termsAccepted:s.termsAccepted,extensionEnabled:s.extensionEnabled,settings:s.settings},usage:{tokensUsed:i._sum.tokensUsed||0,requestsMade:i._sum.requestsMade||0,costAccrued:i._sum.costAccrued||0},extensionSessions:r.length,lastActiveSession:r[0]?.lastActiveAt||null};return c.NextResponse.json(n)}catch(e){return console.error("Extension profile error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){try{let{userId:t}=await (0,o.j)();if(!t)return c.NextResponse.json({error:"Unauthorized"},{status:401});let{settings:s,extensionEnabled:r}=await e.json(),i=await l.database.userProfile.update({where:{userId:t},data:{...s&&{settings:s},..."boolean"==typeof r&&{extensionEnabled:r},updatedAt:new Date}});return c.NextResponse.json({success:!0,profile:i})}catch(e){return console.error("Extension profile update error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/profile/route",pathname:"/api/extension/profile",filename:"route",bundlePath:"app/api/extension/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\profile\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:m}=h;function S(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76315:(e,t,s)=>{"use strict";s.d(t,{n:()=>a});var r=s(26790),i=s(54726);let n={secretKey:i.rB,publishableKey:i.At,apiUrl:i.H$,apiVersion:i.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i.Rg,domain:i.V2,isSatellite:i.fS,sdkMetadata:i.tm,telemetry:{disabled:i.nN,debug:i.Mh}},a=e=>(0,r.z)({...n,...e})},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>s(39112));module.exports=r})();