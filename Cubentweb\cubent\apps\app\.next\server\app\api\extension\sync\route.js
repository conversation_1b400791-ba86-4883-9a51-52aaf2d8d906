(()=>{var e={};e.id=1017,e.ids=[1017],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34830:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>m,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>S});var r={};s.r(r),s.d(r,{GET:()=>d,POST:()=>x});var n=s(26142),i=s(94327),o=s(34862),a=s(37838),u=s(18815),c=s(26239),p=s(25);let l=p.z.object({action:p.z.enum(["push","pull","merge"]),settings:p.z.record(p.z.any()).optional(),lastSyncTimestamp:p.z.string().datetime().optional(),conflictResolution:p.z.enum(["client","server","merge"]).default("merge")});async function x(e){try{let{userId:t}=await (0,a.j)();if(!t)return c.NextResponse.json({error:"Unauthorized"},{status:401});let s=await e.json(),r=l.safeParse(s);if(!r.success)return c.NextResponse.json({error:"Invalid sync request",details:r.error.errors},{status:400});let{action:n,settings:i,lastSyncTimestamp:o,conflictResolution:p}=r.data,x=await u.database.user.findUnique({where:{clerkId:t},select:{id:!0,extensionSettings:!0,preferences:!0,lastSettingsSync:!0,updatedAt:!0}});if(!x)return c.NextResponse.json({error:"User not found"},{status:404});let d=new Date,g={};switch(n){case"pull":g={action:"pull",settings:{extensionSettings:x.extensionSettings||{},preferences:x.preferences||{}},timestamp:x.lastSettingsSync||x.updatedAt,serverTimestamp:d};break;case"push":if(!i)return c.NextResponse.json({error:"Settings required for push action"},{status:400});await u.database.user.update({where:{id:x.id},data:{extensionSettings:i.extensionSettings||x.extensionSettings,preferences:i.preferences||x.preferences,lastSettingsSync:d}}),g={action:"push",success:!0,timestamp:d,message:"Settings pushed to server successfully"};break;case"merge":if(!i)return c.NextResponse.json({error:"Settings required for merge action"},{status:400});let f=x.lastSettingsSync||x.updatedAt,S=new Date(o||0),y={},m=!1,h=f>S;h&&"client"===p?y=i:h&&"server"===p?y={extensionSettings:x.extensionSettings||{},preferences:x.preferences||{}}:(y=function(e,t,s){let r={extensionSettings:{...e.extensionSettings},preferences:{...e.preferences}};return t.extensionSettings&&Object.keys(t.extensionSettings).forEach(e=>{let n=t.extensionSettings[e],i=r.extensionSettings[e];void 0===i?r.extensionSettings[e]=n:n===i||s?Array.isArray(n)&&Array.isArray(i)?r.extensionSettings[e]=[...new Set([...i,...n])]:"object"==typeof n&&"object"==typeof i&&null!==n&&null!==i&&(r.extensionSettings[e]={...i,...n}):r.extensionSettings[e]=n}),t.preferences&&Object.keys(t.preferences).forEach(e=>{let n=t.preferences[e],i=r.preferences[e];void 0===i?r.preferences[e]=n:n===i||s?Array.isArray(n)&&Array.isArray(i)?r.preferences[e]=[...new Set([...i,...n])]:"object"==typeof n&&"object"==typeof i&&null!==n&&null!==i&&(r.preferences[e]={...i,...n}):r.preferences[e]=n}),r}({extensionSettings:x.extensionSettings||{},preferences:x.preferences||{}},i,h),m=h),await u.database.user.update({where:{id:x.id},data:{extensionSettings:y.extensionSettings,preferences:y.preferences,lastSettingsSync:d}}),g={action:"merge",settings:y,hasConflicts:m,conflictResolution:p,timestamp:d,message:m?`Settings merged with conflicts resolved using ${p} strategy`:"Settings merged successfully"}}return c.NextResponse.json(g)}catch(e){return console.error("Settings sync error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(){try{let{userId:e}=await (0,a.j)();if(!e)return c.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e},select:{lastSettingsSync:!0,updatedAt:!0,extensionSettings:!0,preferences:!0}});if(!t)return c.NextResponse.json({error:"User not found"},{status:404});let s=new Date,r=t.lastSettingsSync||t.updatedAt,n=s.getTime()-r.getTime(),i=function(e){let t=JSON.stringify(e,Object.keys(e).sort()),s=0;for(let e=0;e<t.length;e++)s=(s<<5)-s+t.charCodeAt(e),s&=s;return s.toString(36)}({extensionSettings:t.extensionSettings||{},preferences:t.preferences||{}});return c.NextResponse.json({syncStatus:{lastSync:r.toISOString(),timeSinceSync:Math.round(n/1e3),isStale:n>3e5},settings:{extensionSettings:t.extensionSettings||{},preferences:t.preferences||{},hash:i},serverTimestamp:s.toISOString(),syncOptions:{supportedActions:["push","pull","merge"],conflictResolutions:["client","server","merge"],autoSyncInterval:300}})}catch(e){return console.error("Sync status error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/sync/route",pathname:"/api/extension/sync",filename:"route",bundlePath:"app/api/extension/sync/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\sync\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:f,workUnitAsyncStorage:S,serverHooks:y}=g;function m(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:S})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>s(34830));module.exports=r})();