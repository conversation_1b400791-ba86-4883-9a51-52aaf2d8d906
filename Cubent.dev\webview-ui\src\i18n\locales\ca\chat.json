{"greeting": "Benvingut a Roo Code", "task": {"title": "Tasca", "seeMore": "<PERSON><PERSON><PERSON> més", "seeLess": "<PERSON><PERSON><PERSON> menys", "tokens": "Tokens:", "cache": "Caché:", "apiCost": "Cost d'API:", "contextWindow": "Finestra de context:", "closeAndStart": "<PERSON><PERSON> tasca i iniciar-ne una de nova", "export": "Exportar historial de tasques", "delete": "Eliminar tasca (Shift + Clic per ometre confirmació)", "condenseContext": "Condensar context de forma intel·ligent"}, "unpin": "Desfixar", "pin": "Fixar", "tokenProgress": {"availableSpace": "Espai disponible: {{amount}} tokens", "tokensUsed": "Tokens utilitzats: {{used}} de {{total}}", "reservedForResponse": "Reservat per a resposta del model: {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON> a intentar", "tooltip": "Torna a provar l'operació"}, "startNewTask": {"title": "Començar una nova tasca", "tooltip": "Comença una nova tasca"}, "proceedAnyways": {"title": "Con<PERSON><PERSON><PERSON> de totes maneres", "tooltip": "Continua mentre s'executa l'ordre"}, "save": {"title": "Desar", "tooltip": "Desa els canvis del fitxer"}, "reject": {"title": "Rebutjar", "tooltip": "Rebutja aquesta acció"}, "completeSubtaskAndReturn": "Completar la subtasca i tornar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprova aquesta acció"}, "runCommand": {"title": "Executar ordre", "tooltip": "Executa aquesta ordre"}, "proceedWhileRunning": {"title": "Continuar mentre s'executa", "tooltip": "Continua malgrat els advertiments"}, "killCommand": {"title": "Atura l'ordre", "tooltip": "Atura l'ordre actual"}, "resumeTask": {"title": "Reprendre la tasca", "tooltip": "<PERSON><PERSON> la tasca actual"}, "terminate": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Finalitz<PERSON> la tasca actual"}, "cancel": {"title": "Cancel·lar", "tooltip": "Cancel·la l'operació actual"}, "scrollToBottom": "Desplaça't al final del xat", "about": "Genera, refactoritza i depura codi amb l'ajuda de la IA. Consulta la nostra <DocsLink>documentació</DocsLink> per obtenir més informació.", "onboarding": "<strong> La vostra llista de tasques en aquest espai de treball està buida. </strong> Comença escrivint una tasca a continuació. \nNo esteu segur per on començar? \nMés informació sobre què pot fer Roo als documents.", "rooTips": {"boomerangTasks": {"title": "Tasques Boomerang", "description": "Divideix les tasques en parts més petites i manejables."}, "stickyModels": {"title": "Modes persistents", "description": "Cada mode recorda el vostre darrer model utilitzat"}, "tools": {"title": "<PERSON><PERSON>", "description": "Permet que la IA resolgui problemes navegant per la web, executant ordres i molt més."}, "customizableModes": {"title": "Modes personalitzables", "description": "Personalitats especialitzades amb comportaments propis i models assignats"}}, "selectMode": "Selecciona el mode d'interacció", "selectApiConfig": "Seleccioneu la configuració de l'API", "enhancePrompt": "Millora la sol·licitud amb context addicional", "addImages": "Afegeix imatges al missatge", "sendMessage": "Envia el missatge", "typeMessage": "Escriu un missatge...", "typeTask": "Escriu la teva tasca aquí...", "addContext": "@ per afegir context, / per canviar de mode", "dragFiles": "manté premut shift per arrossegar fitxers", "dragFilesImages": "manté premut shift per arrossegar fitxers/imatges", "enhancePromptDescription": "El botó 'Millora la sol·licitud' ajuda a millorar la teva sol·licitud proporcionant context addicional, aclariments o reformulacions. Prova d'escriure una sol·licitud aquí i fes clic al botó de nou per veure com funciona.", "errorReadingFile": "Error en llegir el fitxer:", "noValidImages": "No s'ha processat cap imatge vàlida", "separator": "Separador", "edit": "Edita...", "forNextMode": "per al següent mode", "error": "Error", "diffError": {"title": "Ed<PERSON><PERSON> fall<PERSON>"}, "troubleMessage": "Roo està tenint problemes...", "apiRequest": {"title": "Sol·licitud API", "failed": "Sol·licitud API ha fallat", "streaming": "Sol·licitud API...", "cancelled": "Sol·licitud API cancel·lada", "streamingFailed": "Transmissió API ha fallat"}, "checkpoint": {"initial": "Punt de control inicial", "regular": "Punt de control", "initializingWarning": "Encara s'està inicialitzant el punt de control... Si això triga massa, pots desactivar els punts de control a la <settingsLink>configuració</settingsLink> i reiniciar la teva tasca.", "menu": {"viewDiff": "Veure diferències", "restore": "Restaurar punt de control", "restoreFiles": "<PERSON><PERSON><PERSON> arxius", "restoreFilesDescription": "Restaura els arxius del teu projecte a una instantània presa en aquest punt.", "restoreFilesAndTask": "Restaurar arxius i tasca", "confirm": "Confirmar", "cancel": "Cancel·lar", "cannotUndo": "Aquesta acció no es pot desfer.", "restoreFilesAndTaskDescription": "Restaura els arxius del teu projecte a una instantània presa en aquest punt i elimina tots els missatges posteriors a aquest punt."}, "current": "Actual"}, "instructions": {"wantsToFetch": "Roo vol obtenir instruccions detallades per ajudar amb la tasca actual."}, "fileOperations": {"wantsToRead": "Roo vol llegir aquest fitxer:", "wantsToReadOutsideWorkspace": "Roo vol llegir aquest fitxer fora de l'espai de treball:", "didRead": "Roo ha llegit aquest fitxer:", "wantsToEdit": "Roo vol editar aquest fitxer:", "wantsToEditOutsideWorkspace": "Roo vol editar aquest fitxer fora de l'espai de treball:", "wantsToCreate": "Roo vol crear un nou fitxer:", "wantsToSearchReplace": "Roo vol realitzar cerca i substitució en aquest fitxer:", "didSearchReplace": "Roo ha realitzat cerca i substitució en aquest fitxer:", "wantsToInsert": "Roo vol inserir contingut en aquest fitxer:", "wantsToInsertWithLineNumber": "Roo vol inserir contingut a la línia {{lineNumber}} d'aquest fitxer:", "wantsToInsertAtEnd": "Roo vol afegir contingut al final d'aquest fitxer:", "wantsToReadAndXMore": "En Roo vol llegir aquest fitxer i {{count}} més:", "wantsToReadMultiple": "Roo vol llegir diversos fitxers:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo vol veure els fitxers de nivell superior en aquest directori:", "didViewTopLevel": "Roo ha vist els fitxers de nivell superior en aquest directori:", "wantsToViewRecursive": "Roo vol veure recursivament tots els fitxers en aquest directori:", "didViewRecursive": "Roo ha vist recursivament tots els fitxers en aquest directori:", "wantsToViewDefinitions": "Roo vol veure noms de definicions de codi font utilitzats en aquest directori:", "didViewDefinitions": "Roo ha vist noms de definicions de codi font utilitzats en aquest directori:", "wantsToSearch": "Roo vol cercar en aquest directori <code>{{regex}}</code>:", "didSearch": "Roo ha cercat en aquest directori <code>{{regex}}</code>:"}, "commandOutput": "Sortida de l'ordre", "response": "Resposta", "arguments": "Arguments", "mcp": {"wantsToUseTool": "Roo vol utilitzar una eina al servidor MCP {{serverName}}:", "wantsToAccessResource": "Roo vol accedir a un recurs al servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Roo vol canviar a mode <code>{{mode}}</code>", "wantsToSwitchWithReason": "Roo vol canviar a mode <code>{{mode}}</code> perquè: {{reason}}", "didSwitch": "<PERSON>oo ha canviat a mode <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON>oo ha canviat a mode <code>{{mode}}</code> perquè: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo vol crear una nova subtasca en mode <code>{{mode}}</code>:", "wantsToFinish": "Roo vol finalitzar aquesta subtasca", "newTaskContent": "Instruccions de la subtasca", "completionContent": "Subtasca completada", "resultContent": "Resultats de la subtasca", "defaultResult": "Si us plau, continua amb la següent tasca.", "completionInstructions": "Subtasca completada! Pots revisar els resultats i suggerir correccions o següents passos. Si tot sembla correcte, confirma per tornar el resultat a la tasca principal."}, "questions": {"hasQuestion": "Roo té una pregunta:"}, "taskCompleted": "<PERSON><PERSON> completada", "powershell": {"issues": "Sembla que estàs tenint problemes amb Windows PowerShell, si us plau consulta aquesta documentació per a més informació."}, "autoApprove": {"title": "Aprovació automàtica:", "none": "Cap", "description": "L'aprovació automàtica permet a Roo Code realitzar accions sense demanar permís. Activa-la només per a accions en les que confies plenament. Configuració més detallada disponible a la <settingsLink>Configuració</settingsLink>."}, "reasoning": {"thinking": "Pensant", "seconds": "{{count}}s"}, "contextCondense": {"title": "Context condensat", "condensing": "Condensant context...", "errorHeader": "Error en condensar el context", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "Copiar a l'entrada (o Shift + clic)"}, "announcement": {"title": "🎉 Roo Code {{version}} publicat", "description": "Roo Code {{version}} porta noves funcionalitats potents i millores basades en els teus comentaris.", "whatsNew": "Novetats", "feature1": "<bold>Condensació intel·ligent de context activada per defecte</bold>: La condensació de context ara està activada per defecte amb configuracions configurables per quan es produeix la condensació automàtica", "feature2": "<bold>Botó de condensació manual</bold>: Nou botó a la capçalera de tasques que et permet activar manualment la condensació de context en qualsevol moment", "feature3": "<bold>Configuració avançada de condensació</bold>: Ajusta quan i com es produeix la condensació automàtica a través de <contextSettingsLink>Configuració de context</contextSettingsLink>", "hideButton": "<PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Obtingues més detalls i participa a <discordLink>Discord</discordLink> i <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "Roo vol utilitzar el navegador:", "consoleLogs": "Registres de consola", "noNewLogs": "(Cap registre nou)", "screenshot": "Captura de pantalla del navegador", "cursor": "cursor", "navigation": {"step": "Pas {{current}} de {{total}}", "previous": "Anterior", "next": "<PERSON><PERSON><PERSON><PERSON>"}, "sessionStarted": "<PERSON><PERSON>ó de navegador iniciada", "actions": {"title": "Acció de navegació: ", "launch": "Iniciar nave<PERSON><PERSON> a {{url}}", "click": "Clic ({{coordinate}})", "type": "Escriure \"{{text}}\"", "scrollDown": "<PERSON>p<PERSON><PERSON><PERSON>", "scrollUp": "<PERSON><PERSON><PERSON><PERSON><PERSON> amunt", "close": "<PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Expandir bloc de codi", "collapse": "Contraure bloc de codi", "enable_wrap": "Activar ajustament de línia", "disable_wrap": "Desactivar ajustament de línia", "copy_code": "Copiar codi"}}, "systemPromptWarning": "ADVERTÈNCIA: S'ha activat una substitució personalitzada d'instruccions del sistema. Això pot trencar greument la funcionalitat i causar un comportament impredictible.", "profileViolationWarning": "El perfil actual infringeix la configuració de la teva organització", "shellIntegration": {"title": "Advertència d'execució d'ordres", "description": "La teva ordre s'està executant sense la integració de shell del terminal VSCode. Per suprimir aquest advertiment, pots desactivar la integració de shell a la secció <strong>Terminal</strong> de la <settingsLink>configuració de Roo Code</settingsLink> o solucionar problemes d'integració del terminal VSCode utilitzant l'enllaç a continuació.", "troubleshooting": "Fes clic aquí per a la documentació d'integració de shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "S'ha arribat al límit de sol·licituds aprovades automàticament", "description": "Roo ha arribat al límit aprovat automàticament de {{count}} sol·licitud(s) d'API. Vols reiniciar el comptador i continuar amb la tasca?", "button": "Reiniciar i continuar"}}, "codebaseSearch": {"wantsToSearch": "Roo vol cercar a la base de codi <code>{{query}}</code>:", "wantsToSearchWithPath": "Roo vol cercar a la base de codi <code>{{query}}</code> a <code>{{path}}</code>:", "didSearch": "S'han trobat {{count}} resultat(s) per a <code>{{query}}</code>:"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON> tot"}, "deny": {"title": "<PERSON><PERSON><PERSON> tot"}}}