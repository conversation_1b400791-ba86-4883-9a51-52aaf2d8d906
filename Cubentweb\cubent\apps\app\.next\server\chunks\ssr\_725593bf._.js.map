{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6VAAC,+QAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6VAAC,+QAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6VAAC,+QAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/avatar-stack.tsx"], "sourcesContent": ["'use client';\n\nimport { useOthers, useSelf } from '@repo/collaboration/hooks';\nimport {\n  Avatar,\n  AvatarFallback,\n  AvatarImage,\n} from '@repo/design-system/components/ui/avatar';\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipTrigger,\n} from '@repo/design-system/components/ui/tooltip';\n\ntype PresenceAvatarProps = {\n  info?: Liveblocks['UserMeta']['info'];\n};\n\nconst PresenceAvatar = ({ info }: PresenceAvatarProps) => (\n  <Tooltip delayDuration={0}>\n    <TooltipTrigger>\n      <Avatar className=\"h-7 w-7 bg-secondary ring-1 ring-background\">\n        <AvatarImage src={info?.avatar} alt={info?.name} />\n        <AvatarFallback className=\"text-xs\">\n          {info?.name?.slice(0, 2)}\n        </AvatarFallback>\n      </Avatar>\n    </TooltipTrigger>\n    <TooltipContent collisionPadding={4}>\n      <p>{info?.name ?? 'Unknown'}</p>\n    </TooltipContent>\n  </Tooltip>\n);\n\nexport const AvatarStack = () => {\n  const others = useOthers();\n  const self = useSelf();\n  const hasMoreUsers = others.length > 3;\n\n  return (\n    <div className=\"-space-x-1 flex items-center px-4\">\n      {others.slice(0, 3).map(({ connectionId, info }) => (\n        <PresenceAvatar key={connectionId} info={info} />\n      ))}\n\n      {hasMoreUsers && (\n        <PresenceAvatar\n          info={{\n            name: `+${others.length - 3}`,\n            color: 'var(--color-muted-foreground)',\n          }}\n        />\n      )}\n\n      {self && <PresenceAvatar info={self.info} />}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAKA;AARA;;;;;AAkBA,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAuB,iBACnD,6VAAC,4JAAA,CAAA,UAAO;QAAC,eAAe;;0BACtB,6VAAC,4JAAA,CAAA,iBAAc;0BACb,cAAA,6VAAC,2JAAA,CAAA,SAAM;oBAAC,WAAU;;sCAChB,6VAAC,2JAAA,CAAA,cAAW;4BAAC,KAAK,MAAM;4BAAQ,KAAK,MAAM;;;;;;sCAC3C,6VAAC,2JAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB,MAAM,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;0BAI5B,6VAAC,4JAAA,CAAA,iBAAc;gBAAC,kBAAkB;0BAChC,cAAA,6VAAC;8BAAG,MAAM,QAAQ;;;;;;;;;;;;;;;;;AAKjB,MAAM,cAAc;IACzB,MAAM,SAAS,CAAA,GAAA,iTAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,CAAA,GAAA,6SAAA,CAAA,UAAO,AAAD;IACnB,MAAM,eAAe,OAAO,MAAM,GAAG;IAErC,qBACE,6VAAC;QAAI,WAAU;;YACZ,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,iBAC7C,6VAAC;oBAAkC,MAAM;mBAApB;;;;;YAGtB,8BACC,6VAAC;gBACC,MAAM;oBACJ,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,GAAG,GAAG;oBAC7B,OAAO;gBACT;;;;;;YAIH,sBAAQ,6VAAC;gBAAe,MAAM,KAAK,IAAI;;;;;;;;;;;;AAG9C", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/cursors.tsx"], "sourcesContent": ["'use client';\n\nimport { useMyPresence, useOthers } from '@repo/collaboration/hooks';\nimport { useEffect } from 'react';\n\nconst Cursor = ({\n  name,\n  color,\n  x,\n  y,\n}: {\n  name: string | undefined;\n  color: string;\n  x: number;\n  y: number;\n}) => (\n  <div\n    className=\"pointer-events-none absolute top-0 left-0 z-[999] select-none transition-transform duration-100\"\n    style={{\n      transform: `translateX(${x}px) translateY(${y}px)`,\n    }}\n  >\n    <svg\n      className=\"absolute top-0 left-0\"\n      width=\"24\"\n      height=\"36\"\n      viewBox=\"0 0 24 36\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <title>Cursor</title>\n      <path\n        d=\"M5.65376 12.3673H5.46026L5.31717 12.4976L0.500002 16.8829L0.500002 1.19841L11.7841 12.3673H5.65376Z\"\n        fill={color}\n      />\n    </svg>\n    <div\n      className=\"absolute top-4 left-1.5 whitespace-nowrap rounded-full px-2 py-0.5 text-white text-xs\"\n      style={{\n        backgroundColor: color,\n      }}\n    >\n      {name}\n    </div>\n  </div>\n);\n\nexport const Cursors = () => {\n  /**\n   * useMyPresence returns the presence of the current user and a function to update it.\n   * updateMyPresence is different than the setState function returned by the useState hook from React.\n   * You don't need to pass the full presence object to update it.\n   * See https://liveblocks.io/docs/api-reference/liveblocks-react#useMyPresence for more information\n   */\n  const [_cursor, updateMyPresence] = useMyPresence();\n\n  /**\n   * Return all the other users in the room and their presence (a cursor position in this case)\n   */\n  const others = useOthers();\n\n  useEffect(() => {\n    const onPointerMove = (event: PointerEvent) => {\n      // Update the user cursor position on every pointer move\n      updateMyPresence({\n        cursor: {\n          x: Math.round(event.clientX),\n          y: Math.round(event.clientY),\n        },\n      });\n    };\n\n    const onPointerLeave = () => {\n      // When the pointer goes out, set cursor to null\n      updateMyPresence({\n        cursor: null,\n      });\n    };\n\n    document.body.addEventListener('pointermove', onPointerMove);\n    document.body.addEventListener('pointerleave', onPointerLeave);\n\n    return () => {\n      document.body.removeEventListener('pointermove', onPointerMove);\n      document.body.removeEventListener('pointerleave', onPointerLeave);\n    };\n  }, [updateMyPresence]);\n\n  return others.map(({ connectionId, presence, info }) => {\n    if (!presence.cursor) {\n      return null;\n    }\n\n    return (\n      <Cursor\n        key={`cursor-${connectionId}`}\n        // connectionId is an integer that is incremented at every new connections\n        // Assigning a color with a modulo makes sure that a specific user has the same colors on every clients\n        color={info.color}\n        x={presence.cursor.x}\n        y={presence.cursor.y}\n        name={info?.name}\n      />\n    );\n  });\n};\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAHA;;;;AAKA,MAAM,SAAS,CAAC,EACd,IAAI,EACJ,KAAK,EACL,CAAC,EACD,CAAC,EAMF,iBACC,6VAAC;QACC,WAAU;QACV,OAAO;YACL,WAAW,CAAC,WAAW,EAAE,EAAE,eAAe,EAAE,EAAE,GAAG,CAAC;QACpD;;0BAEA,6VAAC;gBACC,WAAU;gBACV,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6VAAC;kCAAM;;;;;;kCACP,6VAAC;wBACC,GAAE;wBACF,MAAM;;;;;;;;;;;;0BAGV,6VAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;gBACnB;0BAEC;;;;;;;;;;;;AAKA,MAAM,UAAU;IACrB;;;;;GAKC,GACD,MAAM,CAAC,SAAS,iBAAiB,GAAG,CAAA,GAAA,iTAAA,CAAA,gBAAa,AAAD;IAEhD;;GAEC,GACD,MAAM,SAAS,CAAA,GAAA,iTAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,wDAAwD;YACxD,iBAAiB;gBACf,QAAQ;oBACN,GAAG,KAAK,KAAK,CAAC,MAAM,OAAO;oBAC3B,GAAG,KAAK,KAAK,CAAC,MAAM,OAAO;gBAC7B;YACF;QACF;QAEA,MAAM,iBAAiB;YACrB,gDAAgD;YAChD,iBAAiB;gBACf,QAAQ;YACV;QACF;QAEA,SAAS,IAAI,CAAC,gBAAgB,CAAC,eAAe;QAC9C,SAAS,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;QAE/C,OAAO;YACL,SAAS,IAAI,CAAC,mBAAmB,CAAC,eAAe;YACjD,SAAS,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;QACpD;IACF,GAAG;QAAC;KAAiB;IAErB,OAAO,OAAO,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE;QACjD,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,OAAO;QACT;QAEA,qBACE,6VAAC;YAEC,0EAA0E;YAC1E,uGAAuG;YACvG,OAAO,KAAK,KAAK;YACjB,GAAG,SAAS,MAAM,CAAC,CAAC;YACpB,GAAG,SAAS,MAAM,CAAC,CAAC;YACpB,MAAM,MAAM;WANP,CAAC,OAAO,EAAE,cAAc;;;;;IASnC;AACF", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/actions/users/get.ts"], "sourcesContent": ["'use server';\n\nimport {\n  type OrganizationMembership,\n  auth,\n  clerkClient,\n} from '@repo/auth/server';\n\nconst getName = (user: OrganizationMembership): string | undefined => {\n  let name = user.publicUserData?.firstName;\n\n  if (name && user.publicUserData?.lastName) {\n    name = `${name} ${user.publicUserData.lastName}`;\n  } else if (!name) {\n    name = user.publicUserData?.identifier;\n  }\n\n  return name;\n};\n\nconst colors = [\n  'var(--color-red-500)',\n  'var(--color-orange-500)',\n  'var(--color-amber-500)',\n  'var(--color-yellow-500)',\n  'var(--color-lime-500)',\n  'var(--color-green-500)',\n  'var(--color-emerald-500)',\n  'var(--color-teal-500)',\n  'var(--color-cyan-500)',\n  'var(--color-sky-500)',\n  'var(--color-blue-500)',\n  'var(--color-indigo-500)',\n  'var(--color-violet-500)',\n  'var(--color-purple-500)',\n  'var(--color-fuchsia-500)',\n  'var(--color-pink-500)',\n  'var(--color-rose-500)',\n];\n\nexport const getUsers = async (\n  userIds: string[]\n): Promise<\n  | {\n      data: Liveblocks['UserMeta']['info'][];\n    }\n  | {\n      error: unknown;\n    }\n> => {\n  try {\n    const { orgId } = await auth();\n\n    if (!orgId) {\n      throw new Error('Not logged in');\n    }\n\n    const clerk = await clerkClient();\n\n    const members = await clerk.organizations.getOrganizationMembershipList({\n      organizationId: orgId,\n      limit: 100,\n    });\n\n    const data: Liveblocks['UserMeta']['info'][] = members.data\n      .filter(\n        (user) =>\n          user.publicUserData?.userId &&\n          userIds.includes(user.publicUserData.userId)\n      )\n      .map((user) => ({\n        name: getName(user) ?? 'Unknown user',\n        picture: user.publicUserData?.imageUrl ?? '',\n        color: colors[Math.floor(Math.random() * colors.length)],\n      }));\n\n    return { data };\n  } catch (error) {\n    return { error };\n  }\n};\n"], "names": [], "mappings": ";;;;;;IAwCa,WAAA,WAAA,GAAA,CAAA,GAAA,qUAAA,CAAA,wBAAA,EAAA,8CAAA,qUAAA,CAAA,aAAA,EAAA,KAAA,GAAA,qUAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/actions/users/search.ts"], "sourcesContent": ["'use server';\n\nimport {\n  type OrganizationMembership,\n  auth,\n  clerkClient,\n} from '@repo/auth/server';\nimport Fuse from 'fuse.js';\n\nconst getName = (user: OrganizationMembership): string | undefined => {\n  let name = user.publicUserData?.firstName;\n\n  if (name && user.publicUserData?.lastName) {\n    name = `${name} ${user.publicUserData.lastName}`;\n  } else if (!name) {\n    name = user.publicUserData?.identifier;\n  }\n\n  return name;\n};\n\nexport const searchUsers = async (\n  query: string\n): Promise<\n  | {\n      data: string[];\n    }\n  | {\n      error: unknown;\n    }\n> => {\n  try {\n    const { orgId } = await auth();\n\n    if (!orgId) {\n      throw new Error('Not logged in');\n    }\n\n    const clerk = await clerkClient();\n\n    const members = await clerk.organizations.getOrganizationMembershipList({\n      organizationId: orgId,\n      limit: 100,\n    });\n\n    const users = members.data.map((user) => ({\n      id: user.id,\n      name: getName(user) ?? user.publicUserData?.identifier,\n      imageUrl: user.publicUserData?.imageUrl,\n    }));\n\n    const fuse = new Fuse(users, {\n      keys: ['name'],\n      minMatchCharLength: 1,\n      threshold: 0.3,\n    });\n\n    const results = fuse.search(query);\n    const data = results.map((result) => result.item.id);\n\n    return { data };\n  } catch (error) {\n    return { error };\n  }\n};\n"], "names": [], "mappings": ";;;;;;IAqBa,cAAA,WAAA,GAAA,CAAA,GAAA,qUAAA,CAAA,wBAAA,EAAA,8CAAA,qUAAA,CAAA,aAAA,EAAA,KAAA,GAAA,qUAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/collaboration/room.tsx"], "sourcesContent": ["'use client';\n\nimport type { ResolveMentionSuggestionsArgs } from '@liveblocks/client';\nimport type { ResolveUsersArgs } from '@liveblocks/node';\nimport {\n  ClientSideSuspense,\n  LiveblocksProvider,\n  RoomProvider,\n} from '@liveblocks/react/suspense';\nimport type { ComponentProps, ReactNode } from 'react';\n\ntype RoomProps = ComponentProps<typeof LiveblocksProvider> & {\n  id: string;\n  children: ReactNode;\n  authEndpoint: string;\n  fallback: ReactNode;\n  resolveUsers?: (\n    args: ResolveUsersArgs\n  ) => Promise<Liveblocks['UserMeta']['info'][]>;\n  resolveMentionSuggestions?: (\n    args: ResolveMentionSuggestionsArgs\n  ) => Promise<string[]>;\n};\n\nexport const Room = ({\n  id,\n  children,\n  authEndpoint,\n  fallback,\n  ...props\n}: RoomProps) => (\n  <LiveblocksProvider authEndpoint={authEndpoint} {...props}>\n    <RoomProvider id={id} initialPresence={{ cursor: null }}>\n      <ClientSideSuspense fallback={fallback}>{children}</ClientSideSuspense>\n    </RoomProvider>\n  </LiveblocksProvider>\n);\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAJA;;;AAwBO,MAAM,OAAO,CAAC,EACnB,EAAE,EACF,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,GAAG,OACO,iBACV,6VAAC,2PAAA,CAAA,qBAAkB;QAAC,cAAc;QAAe,GAAG,KAAK;kBACvD,cAAA,6VAAC,+SAAA,CAAA,eAAY;YAAC,IAAI;YAAI,iBAAiB;gBAAE,QAAQ;YAAK;sBACpD,cAAA,6VAAC,2PAAA,CAAA,qBAAkB;gBAAC,UAAU;0BAAW", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/components/collaboration-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { getUsers } from '@/app/actions/users/get';\nimport { searchUsers } from '@/app/actions/users/search';\nimport { Room } from '@repo/collaboration/room';\nimport type { ReactNode } from 'react';\n\nexport const CollaborationProvider = ({\n  orgId,\n  children,\n}: {\n  orgId: string;\n  children: ReactNode;\n}) => {\n  const resolveUsers = async ({ userIds }: { userIds: string[] }) => {\n    const response = await getUsers(userIds);\n\n    if ('error' in response) {\n      throw new Error('Problem resolving users');\n    }\n\n    return response.data;\n  };\n\n  const resolveMentionSuggestions = async ({ text }: { text: string }) => {\n    const response = await searchUsers(text);\n\n    if ('error' in response) {\n      throw new Error('Problem resolving mention suggestions');\n    }\n\n    return response.data;\n  };\n\n  return (\n    <Room\n      id={`${orgId}:presence`}\n      authEndpoint=\"/api/collaboration/auth\"\n      fallback={\n        <div className=\"px-3 text-muted-foreground text-xs\">Loading...</div>\n      }\n      resolveUsers={resolveUsers}\n      resolveMentionSuggestions={resolveMentionSuggestions}\n    >\n      {children}\n    </Room>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOO,MAAM,wBAAwB,CAAC,EACpC,KAAK,EACL,QAAQ,EAIT;IACC,MAAM,eAAe,OAAO,EAAE,OAAO,EAAyB;QAC5D,MAAM,WAAW,MAAM,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAEhC,IAAI,WAAW,UAAU;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,4BAA4B,OAAO,EAAE,IAAI,EAAoB;QACjE,MAAM,WAAW,MAAM,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAEnC,IAAI,WAAW,UAAU;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,qBACE,6VAAC,kIAAA,CAAA,OAAI;QACH,IAAI,GAAG,MAAM,SAAS,CAAC;QACvB,cAAa;QACb,wBACE,6VAAC;YAAI,WAAU;sBAAqC;;;;;;QAEtD,cAAc;QACd,2BAA2B;kBAE1B;;;;;;AAGP", "debugId": null}}]}