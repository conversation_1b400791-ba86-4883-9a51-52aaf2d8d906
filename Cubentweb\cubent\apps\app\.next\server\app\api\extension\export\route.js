(()=>{var e={};e.id=3666,e.ids=[3666],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,r)=>{"use strict";e.exports=r(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44679:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>p});var a=r(26142),o=r(94327),n=r(34862),i=r(37838),u=r(18815),d=r(26239);async function c(e){try{let r,{userId:s}=await (0,i.j)();if(!s)return d.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),o=a.get("format")||"json",n=a.get("period")||"30d",c="true"===a.get("includeMetadata"),p=await u.database.user.findUnique({where:{clerkId:s}});if(!p)return d.NextResponse.json({error:"User not found"},{status:404});let l=new Date;switch(n){case"7d":r=new Date(l.getTime()-6048e5);break;case"30d":default:r=new Date(l.getTime()-2592e6);break;case"90d":r=new Date(l.getTime()-7776e6);break;case"1y":r=new Date(l.getTime()-31536e6);break;case"all":r=new Date(0)}let x=await u.database.usageMetrics.findMany({where:{userId:p.id,date:{gte:r}},orderBy:{date:"asc"}}),g=[];c&&(g=await u.database.extensionSession.findMany({where:{userId:p.id,createdAt:{gte:r}},orderBy:{createdAt:"asc"}}));let h={exportInfo:{userId:s,exportDate:l.toISOString(),period:n,dateRange:{start:r.toISOString(),end:l.toISOString()},totalRecords:x.length,format:o},usageMetrics:x.map(e=>({date:e.date.toISOString(),tokensUsed:e.tokensUsed,requestsMade:e.requestsMade,costAccrued:e.costAccrued})),summary:{totalTokensUsed:x.reduce((e,t)=>e+t.tokensUsed,0),totalRequestsMade:x.reduce((e,t)=>e+t.requestsMade,0),totalCostAccrued:x.reduce((e,t)=>e+t.costAccrued,0),averageDailyTokens:x.length>0?Math.round(x.reduce((e,t)=>e+t.tokensUsed,0)/x.length):0,averageDailyRequests:x.length>0?Math.round(x.reduce((e,t)=>e+t.requestsMade,0)/x.length):0},...c&&{extensionSessions:g.map(e=>({sessionId:e.sessionId,extensionVersion:e.extensionVersion,vscodeVersion:e.vscodeVersion,platform:e.platform,isActive:e.isActive,tokensUsed:e.tokensUsed,requestsMade:e.requestsMade,createdAt:e.createdAt.toISOString(),lastActiveAt:e.lastActiveAt?.toISOString(),metadata:e.metadata}))}};if("csv"===o){var t;let e=(t=h.usageMetrics,0===t.length?"Date,Tokens Used,Requests Made,Cost Accrued,Created At\n":"Date,Tokens Used,Requests Made,Cost Accrued,Created At\n"+t.map(e=>`${e.date},${e.tokensUsed},${e.requestsMade},${e.costAccrued},${e.createdAt}`).join("\n"));return new d.NextResponse(e,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="cubent-usage-${n}-${l.toISOString().split("T")[0]}.csv"`}})}if("xlsx"===o)return d.NextResponse.json({...h,note:"Excel format not yet implemented. Use CSV format for spreadsheet compatibility."});return d.NextResponse.json(h,{headers:{"Content-Disposition":`attachment; filename="cubent-usage-${n}-${l.toISOString().split("T")[0]}.json"`}})}catch(e){return console.error("Export error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){try{let{userId:e}=await (0,i.j)();if(!e)return d.NextResponse.json({error:"Unauthorized"},{status:401});let t=await u.database.user.findUnique({where:{clerkId:e}});if(!t)return d.NextResponse.json({error:"User not found"},{status:404});let r=await u.database.usageMetrics.count({where:{userId:t.id}}),s=await u.database.extensionSession.count({where:{userId:t.id}}),a=await u.database.usageMetrics.findFirst({where:{userId:t.id},orderBy:{date:"asc"},select:{date:!0}}),o=await u.database.usageMetrics.findFirst({where:{userId:t.id},orderBy:{date:"desc"},select:{date:!0}});return d.NextResponse.json({availableData:{totalUsageRecords:r,totalSessionRecords:s,dateRange:{oldest:a?.date||null,newest:o?.date||null}},exportOptions:{formats:["json","csv"],periods:["7d","30d","90d","1y","all"],includeMetadata:!0},estimatedSizes:{json:`${Math.round(.2*r)}KB`,csv:`${Math.round(.1*r)}KB`}})}catch(e){return console.error("Export info error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/extension/export/route",pathname:"/api/extension/export",filename:"route",bundlePath:"app/api/extension/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\export\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:h}=l;function q(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>r(44679));module.exports=s})();