{"extension.displayName": "Roo Code (precedentemente Roo Cline)", "extension.description": "Un intero team di sviluppo di agenti IA nel tuo editor.", "command.newTask.title": "Nuovo Task", "command.explainCode.title": "Spiega Codice", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "Aggiungi al Contesto", "command.openInNewTab.title": "Apri in Nuova Scheda", "command.focusInput.title": "Focalizza Campo di Input", "command.setCustomStoragePath.title": "Imposta Percorso di Archiviazione Personalizzato", "command.terminal.addToContext.title": "Aggiungi Contenuto del Terminale al Contesto", "command.terminal.fixCommand.title": "Corregg<PERSON>", "command.terminal.explainCommand.title": "Spiega Questo Comando", "command.acceptInput.title": "Accetta Input/Suggerimento", "views.activitybar.title": "Roo Code", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.sidebar.name": "Roo Code", "command.mcpServers.title": "Server MCP", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "Cronologia", "command.openInEditor.title": "<PERSON><PERSON> nell'Editor", "command.settings.title": "Impostazioni", "command.documentation.title": "Documentazione", "configuration.title": "qapt coder", "commands.allowedCommands.description": "Comandi che possono essere eseguiti automaticamente quando 'Approva sempre le operazioni di esecuzione' è attivato", "settings.vsCodeLmModelSelector.description": "Impostazioni per l'API del modello linguistico VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Il fornitore del modello linguistico (es. copilot)", "settings.vsCodeLmModelSelector.family.description": "La famiglia del modello linguistico (es. gpt-4)", "settings.customStoragePath.description": "Percorso di archiviazione personalizzato. Lasciare vuoto per utilizzare la posizione predefinita. Supporta percorsi assoluti (es. 'D:\\qaptCoderStorage')", "settings.qaptCoderCloudEnabled.description": "Abilita qapt coder Cloud."}