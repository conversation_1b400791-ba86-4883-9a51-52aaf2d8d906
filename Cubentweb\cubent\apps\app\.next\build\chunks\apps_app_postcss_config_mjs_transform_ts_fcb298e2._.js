module.exports = {

"[project]/apps/app/postcss.config.mjs/transform.ts { CONFIG => \"[project]/apps/app/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/node_modules__pnpm_d09083e4._.js",
  "build/chunks/[root-of-the-server]__700bfabb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/apps/app/postcss.config.mjs/transform.ts { CONFIG => \"[project]/apps/app/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}}),

};