{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/instance.ts"], "sourcesContent": ["/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n"], "names": [], "mappings": ";;;;AAGO,SAAS,UAAU,WAAA,EAA8B;IACtD,OACE,YAAY,QAAA,CAAS,eAAe,KACpC,YAAY,QAAA,CAAS,eAAe,KACpC,YAAY,QAAA,CAAS,iBAAiB,KACtC,YAAY,QAAA,CAAS,oBAAoB;AAE7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/constants.ts"], "sourcesContent": ["export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAO,IAAM,+BAA+B;IAAC;IAAY;IAAiB,eAAe;CAAA;AAClF,IAAM,gCAAgC;IAAC;IAAiB;IAAsB,wBAAwB;CAAA;AACtG,IAAM,0BAA0B;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AACO,IAAM,qBAAqB;IAAC;IAAY;IAAgB;IAAiB,wBAAwB;CAAA;AACjG,IAAM,uBAAuB;IAAC,oBAAoB;CAAA;AAClD,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AAMrB,SAAS,aAAa,EAAA,EAAY,SAAyB,KAAA,EAAe;IAC/E,OAAO,CAAA,6BAAA,EAAgC,EAAE,CAAA,CAAA,EAAI,MAAM,EAAA;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/url.ts"], "sourcesContent": ["import { CURRENT_DEV_INSTANCE_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, version?: string) => {\n  if (!version && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!version) {\n    return 'latest';\n  }\n\n  return version.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (frontendApi: string, { clerkJSVersion }: { clerkJSVersion?: string }) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n\n// Returns true for hosts such as:\n// * accounts.foo.bar-13.lcl.dev\n// * accounts.foo.bar-13.lclstage.dev\n// * accounts.foo.bar-13.dev.lclclerk.com\nexport function isLegacyDevAccountPortalOrigin(host: string): boolean {\n  return LEGACY_DEV_INSTANCE_SUFFIXES.some(legacyDevSuffix => {\n    return host.startsWith('accounts.') && host.endsWith(legacyDevSuffix);\n  });\n}\n\n// Returns true for hosts such as:\n// * foo-bar-13.accounts.dev\n// * foo-bar-13.accountsstage.dev\n// * foo-bar-13.accounts.lclclerk.com\n// But false for:\n// * foo-bar-13.clerk.accounts.lclclerk.com\nexport function isCurrentDevAccountPortalOrigin(host: string): boolean {\n  return CURRENT_DEV_INSTANCE_SUFFIXES.some(currentDevSuffix => {\n    return host.endsWith(currentDevSuffix) && !host.endsWith('.clerk' + currentDevSuffix);\n  });\n}\n\n/* Functions below are taken from https://github.com/unjs/ufo/blob/main/src/utils.ts. LICENSE: MIT */\n\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\n\nexport function hasTrailingSlash(input = '', respectQueryAndFragment?: boolean): boolean {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/');\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\n\nexport function withTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/') ? input : input + '/';\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split('?');\n  return s0 + '/' + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function withoutTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || '/';\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split('?');\n  return (s0.slice(0, -1) || '/') + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function hasLeadingSlash(input = ''): boolean {\n  return input.startsWith('/');\n}\n\nexport function withoutLeadingSlash(input = ''): string {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || '/';\n}\n\nexport function withLeadingSlash(input = ''): string {\n  return hasLeadingSlash(input) ? input : '/' + input;\n}\n\nexport function cleanDoubleSlashes(input = ''): string {\n  return input\n    .split('://')\n    .map(string_ => string_.replace(/\\/{2,}/g, '/'))\n    .join('://');\n}\n\nexport function isNonEmptyURL(url: string) {\n  return url && url !== '/';\n}\n\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\n\nexport function joinURL(base: string, ...input: string[]): string {\n  let url = base || '';\n\n  for (const segment of input.filter(url => isNonEmptyURL(url))) {\n    if (url) {\n      // TODO: Handle .. when joining\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, '');\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n\n  return url;\n}\n\n/* Code below is taken from https://github.com/vercel/next.js/blob/fe7ff3f468d7651a92865350bfd0f16ceba27db5/packages/next/src/shared/lib/utils.ts. LICENSE: MIT */\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url);\n"], "names": ["url"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,kBAAkB,cAAc,EAAA,EAAqB;IACnE,IAAI,YAAY,UAAA,CAAW,GAAG,GAAG;QAC/B,cAAc,YAAY,KAAA,CAAM,CAAC;IACnC;IACA,OAAO,IAAI,gBAAgB,WAAW;AACxC;AAEO,SAAS,YAAY,MAAM,EAAA,EAAY;IAC5C,OAAA,CAAQ,OAAO,EAAA,EAAI,OAAA,CAAQ,YAAY,EAAE;AAC3C;AAEO,SAAS,eAAe,GAAA,EAAyB;IACtD,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,IAAI;IACJ,IAAI,IAAI,KAAA,CAAM,iBAAiB,GAAG;QAChC,QAAQ;IACV,OAAA,IAAW,IAAI,KAAA,CAAM,kBAAkB,GAAG;QACxC,OAAO;IACT,OAAO;QACL,QAAQ;IACV;IAEA,MAAM,WAAW,IAAI,OAAA,CAAQ,OAAO,EAAE;IACtC,OAAO,CAAA,MAAA,EAAS,QAAQ,EAAA;AAC1B;AAQO,IAAM,8BAA8B,CAAC,aAAqB,YAAqB;IACpF,IAAI,CAAC,WAAW,0RAAA,EAAU,WAAW,GAAG;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,OAAO,QAAQ,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK;AAClC;AAOO,IAAM,eAAe,CAAC,aAAqB,EAAE,cAAA,CAAe,CAAA,KAAmC;IACpG,MAAM,sBAAsB,YAAY,OAAA,CAAQ,iBAAiB,EAAE;IACnE,MAAM,QAAQ,4BAA4B,aAAa,cAAc;IACrE,OAAO,CAAA,QAAA,EAAW,mBAAmB,CAAA,qBAAA,EAAwB,kBAAkB,KAAK,CAAA,sBAAA,CAAA;AACtF;AAMO,SAAS,+BAA+B,IAAA,EAAuB;IACpE,iRAAO,+BAAA,CAA6B,IAAA,CAAK,CAAA,oBAAmB;QAC1D,OAAO,KAAK,UAAA,CAAW,WAAW,KAAK,KAAK,QAAA,CAAS,eAAe;IACtE,CAAC;AACH;AAQO,SAAS,gCAAgC,IAAA,EAAuB;IACrE,iRAAO,gCAAA,CAA8B,IAAA,CAAK,CAAA,qBAAoB;QAC5D,OAAO,KAAK,QAAA,CAAS,gBAAgB,KAAK,CAAC,KAAK,QAAA,CAAS,WAAW,gBAAgB;IACtF,CAAC;AACH;AAIA,IAAM,oBAAoB;AAEnB,SAAS,iBAAiB,QAAQ,EAAA,EAAI,uBAAA,EAA4C;IACvF,IAAI,CAAC,yBAAyB;QAC5B,OAAO,MAAM,QAAA,CAAS,GAAG;IAC3B;IACA,OAAO,kBAAkB,IAAA,CAAK,KAAK;AACrC;AAEO,SAAS,kBAAkB,QAAQ,EAAA,EAAI,uBAAA,EAA2C;IACvF,IAAI,CAAC,yBAAyB;QAC5B,OAAO,MAAM,QAAA,CAAS,GAAG,IAAI,QAAQ,QAAQ;IAC/C;IACA,IAAI,iBAAiB,OAAO,IAAI,GAAG;QACjC,OAAO,SAAS;IAClB;IACA,IAAI,OAAO;IACX,IAAI,WAAW;IACf,MAAM,gBAAgB,MAAM,OAAA,CAAQ,GAAG;IACvC,IAAI,iBAAiB,GAAG;QACtB,OAAO,MAAM,KAAA,CAAM,GAAG,aAAa;QACnC,WAAW,MAAM,KAAA,CAAM,aAAa;QACpC,IAAI,CAAC,MAAM;YACT,OAAO;QACT;IACF;IACA,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA,GAAI,KAAK,KAAA,CAAM,GAAG;IACjC,OAAO,KAAK,MAAA,CAAO,EAAE,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA,GAAK,EAAA,IAAM;AAC9D;AAEO,SAAS,qBAAqB,QAAQ,EAAA,EAAI,uBAAA,EAA2C;IAC1F,IAAI,CAAC,yBAAyB;QAC5B,OAAA,CAAQ,iBAAiB,KAAK,IAAI,MAAM,KAAA,CAAM,GAAG,CAAA,CAAE,IAAI,KAAA,KAAU;IACnE;IACA,IAAI,CAAC,iBAAiB,OAAO,IAAI,GAAG;QAClC,OAAO,SAAS;IAClB;IACA,IAAI,OAAO;IACX,IAAI,WAAW;IACf,MAAM,gBAAgB,MAAM,OAAA,CAAQ,GAAG;IACvC,IAAI,iBAAiB,GAAG;QACtB,OAAO,MAAM,KAAA,CAAM,GAAG,aAAa;QACnC,WAAW,MAAM,KAAA,CAAM,aAAa;IACtC;IACA,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA,GAAI,KAAK,KAAA,CAAM,GAAG;IACjC,OAAA,CAAQ,GAAG,KAAA,CAAM,GAAG,CAAA,CAAE,KAAK,GAAA,IAAA,CAAQ,EAAE,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA,GAAK,EAAA,IAAM;AAC9E;AAEO,SAAS,gBAAgB,QAAQ,EAAA,EAAa;IACnD,OAAO,MAAM,UAAA,CAAW,GAAG;AAC7B;AAEO,SAAS,oBAAoB,QAAQ,EAAA,EAAY;IACtD,OAAA,CAAQ,gBAAgB,KAAK,IAAI,MAAM,KAAA,CAAM,CAAC,IAAI,KAAA,KAAU;AAC9D;AAEO,SAAS,iBAAiB,QAAQ,EAAA,EAAY;IACnD,OAAO,gBAAgB,KAAK,IAAI,QAAQ,MAAM;AAChD;AAEO,SAAS,mBAAmB,QAAQ,EAAA,EAAY;IACrD,OAAO,MACJ,KAAA,CAAM,KAAK,EACX,GAAA,CAAI,CAAA,UAAW,QAAQ,OAAA,CAAQ,WAAW,GAAG,CAAC,EAC9C,IAAA,CAAK,KAAK;AACf;AAEO,SAAS,cAAc,GAAA,EAAa;IACzC,OAAO,OAAO,QAAQ;AACxB;AAEA,IAAM,wBAAwB;AAEvB,SAAS,QAAQ,IAAA,EAAA,GAAiB,KAAA,EAAyB;IAChE,IAAI,MAAM,QAAQ;IAElB,KAAA,MAAW,WAAW,MAAM,MAAA,CAAO,CAAAA,OAAO,cAAcA,IAAG,CAAC,EAAG;QAC7D,IAAI,KAAK;YAEP,MAAM,WAAW,QAAQ,OAAA,CAAQ,uBAAuB,EAAE;YAC1D,MAAM,kBAAkB,GAAG,IAAI;QACjC,OAAO;YACL,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAMA,IAAM,qBAAqB;AACpB,IAAM,gBAAgB,CAAC,MAAgB,mBAAmB,IAAA,CAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/retry.ts"], "sourcesContent": ["type Milliseconds = number;\n\ntype RetryOptions = Partial<{\n  /**\n   * The initial delay before the first retry.\n   * @default 125\n   */\n  initialDelay: Milliseconds;\n  /**\n   * The maximum delay between retries.\n   * The delay between retries will never exceed this value.\n   * If set to 0, the delay will increase indefinitely.\n   * @default 0\n   */\n  maxDelayBetweenRetries: Milliseconds;\n  /**\n   * The multiplier for the exponential backoff.\n   * @default 2\n   */\n  factor: number;\n  /**\n   * A function to determine if the operation should be retried.\n   * The callback accepts the error that was thrown and the number of iterations.\n   * The iterations variable references the number of retries AFTER attempt\n   * that caused the error and starts at 1 (as in, this is the 1st, 2nd, nth retry).\n   * @default (error, iterations) => iterations < 5\n   */\n  shouldRetry: (error: unknown, iterations: number) => boolean;\n  /**\n   * Controls whether the helper should retry the operation immediately once before applying exponential backoff.\n   * The delay for the immediate retry is 100ms.\n   * @default false\n   */\n  retryImmediately: boolean;\n  /**\n   * If true, the intervals will be multiplied by a factor in the range of [1,2].\n   * @default true\n   */\n  jitter: boolean;\n}>;\n\nconst defaultOptions: Required<RetryOptions> = {\n  initialDelay: 125,\n  maxDelayBetweenRetries: 0,\n  factor: 2,\n  shouldRetry: (_: unknown, iteration: number) => iteration < 5,\n  retryImmediately: false,\n  jitter: true,\n};\n\nconst RETRY_IMMEDIATELY_DELAY = 100;\n\nconst sleep = async (ms: Milliseconds) => new Promise(s => setTimeout(s, ms));\n\nconst applyJitter = (delay: Milliseconds, jitter: boolean) => {\n  return jitter ? delay * (1 + Math.random()) : delay;\n};\n\nconst createExponentialDelayAsyncFn = (\n  opts: Required<Pick<RetryOptions, 'initialDelay' | 'maxDelayBetweenRetries' | 'factor' | 'jitter'>>,\n) => {\n  let timesCalled = 0;\n\n  const calculateDelayInMs = () => {\n    const constant = opts.initialDelay;\n    const base = opts.factor;\n    let delay = constant * Math.pow(base, timesCalled);\n    delay = applyJitter(delay, opts.jitter);\n    return Math.min(opts.maxDelayBetweenRetries || delay, delay);\n  };\n\n  return async (): Promise<void> => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\n\n/**\n * Retries a callback until it succeeds or the shouldRetry function returns false.\n * See {@link RetryOptions} for the available options.\n */\nexport const retry = async <T>(callback: () => T | Promise<T>, options: RetryOptions = {}): Promise<T> => {\n  let iterations = 0;\n  const { shouldRetry, initialDelay, maxDelayBetweenRetries, factor, retryImmediately, jitter } = {\n    ...defaultOptions,\n    ...options,\n  };\n\n  const delay = createExponentialDelayAsyncFn({\n    initialDelay,\n    maxDelayBetweenRetries,\n    factor,\n    jitter,\n  });\n\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterations++;\n      if (!shouldRetry(e, iterations)) {\n        throw e;\n      }\n      if (retryImmediately && iterations === 1) {\n        await sleep(applyJitter(RETRY_IMMEDIATELY_DELAY, jitter));\n      } else {\n        await delay();\n      }\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;AAyCA,IAAM,iBAAyC;IAC7C,cAAc;IACd,wBAAwB;IACxB,QAAQ;IACR,aAAa,CAAC,GAAY,YAAsB,YAAY;IAC5D,kBAAkB;IAClB,QAAQ;AACV;AAEA,IAAM,0BAA0B;AAEhC,IAAM,QAAQ,OAAO,KAAqB,IAAI,QAAQ,CAAA,IAAK,WAAW,GAAG,EAAE,CAAC;AAE5E,IAAM,cAAc,CAAC,OAAqB,WAAoB;IAC5D,OAAO,SAAS,QAAA,CAAS,IAAI,KAAK,MAAA,CAAO,CAAA,IAAK;AAChD;AAEA,IAAM,gCAAgC,CACpC,SACG;IACH,IAAI,cAAc;IAElB,MAAM,qBAAqB,MAAM;QAC/B,MAAM,WAAW,KAAK,YAAA;QACtB,MAAM,OAAO,KAAK,MAAA;QAClB,IAAI,QAAQ,WAAW,KAAK,GAAA,CAAI,MAAM,WAAW;QACjD,QAAQ,YAAY,OAAO,KAAK,MAAM;QACtC,OAAO,KAAK,GAAA,CAAI,KAAK,sBAAA,IAA0B,OAAO,KAAK;IAC7D;IAEA,OAAO,YAA2B;QAChC,MAAM,MAAM,mBAAmB,CAAC;QAChC;IACF;AACF;AAMO,IAAM,QAAQ,OAAU,UAAgC,UAAwB,CAAC,CAAA,KAAkB;IACxG,IAAI,aAAa;IACjB,MAAM,EAAE,WAAA,EAAa,YAAA,EAAc,sBAAA,EAAwB,MAAA,EAAQ,gBAAA,EAAkB,MAAA,CAAO,CAAA,GAAI;QAC9F,GAAG,cAAA;QACH,GAAG,OAAA;IACL;IAEA,MAAM,QAAQ,8BAA8B;QAC1C;QACA;QACA;QACA;IACF,CAAC;IAED,MAAO,KAAM;QACX,IAAI;YACF,OAAO,MAAM,SAAS;QACxB,EAAA,OAAS,GAAG;YACV;YACA,IAAI,CAAC,YAAY,GAAG,UAAU,GAAG;gBAC/B,MAAM;YACR;YACA,IAAI,oBAAoB,eAAe,GAAG;gBACxC,MAAM,MAAM,YAAY,yBAAyB,MAAM,CAAC;YAC1D,OAAO;gBACL,MAAM,MAAM;YACd;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/isomorphicAtob.ts"], "sourcesContent": ["/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n"], "names": [], "mappings": ";;;;AAIO,IAAM,iBAAiB,CAAC,SAAiB;IAC9C,IAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;QAC7D,OAAO,KAAK,IAAI;IAClB,OAAA,IAAW,OAAO,WAAW,eAAe,OAAO,MAAA,EAAQ;QACzD,OAAO,IAAI,OAAO,MAAA,CAAO,MAAM,QAAQ,EAAE,QAAA,CAAS;IACpD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/isomorphicBtoa.ts"], "sourcesContent": ["export const isomorphicBtoa = (data: string) => {\n  if (typeof btoa !== 'undefined' && typeof btoa === 'function') {\n    return btoa(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data).toString('base64');\n  }\n  return data;\n};\n"], "names": [], "mappings": ";;;;AAAO,IAAM,iBAAiB,CAAC,SAAiB;IAC9C,IAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;QAC7D,OAAO,KAAK,IAAI;IAClB,OAAA,IAAW,OAAO,WAAW,eAAe,OAAO,MAAA,EAAQ;QACzD,OAAO,IAAI,OAAO,MAAA,CAAO,IAAI,EAAE,QAAA,CAAS,QAAQ;IAClD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/keys.ts"], "sourcesContent": ["import type { Publishable<PERSON>ey } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\ntype ParsePublishableKeyOptions = {\n  fatal?: boolean;\n  domain?: string;\n  proxyUrl?: string;\n  isSatellite?: boolean;\n};\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  // TODO(@dimkl): validate packages/clerk-js/src/utils/instance.ts\n  frontendApi = frontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n    return hasValidPrefix && hasValidFrontendApiPostfix;\n  } catch {\n    return false;\n  }\n}\n\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAaA,IAAM,8BAA8B;AACpC,IAAM,8BAA8B;AAGpC,IAAM,qCAAqC;AAEpC,SAAS,oBAAoB,WAAA,EAA6B;IAC/D,MAAM,WACJ,mCAAmC,IAAA,CAAK,WAAW,KAClD,YAAY,UAAA,CAAW,QAAQ,+QAAK,+BAAA,CAA6B,IAAA,CAAK,CAAA,IAAK,YAAY,QAAA,CAAS,CAAC,CAAC;IACrG,MAAM,YAAY,WAAW,8BAA8B;IAC3D,OAAO,GAAG,SAAS,iRAAG,iBAAA,EAAe,GAAG,WAAW,CAAA,CAAA,CAAG,CAAC,EAAA;AACzD;AAUO,SAAS,oBACd,GAAA,EACA,UAA0F,CAAC,CAAA,EACpE;IACvB,MAAM,OAAO;IAEb,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;QAClC,IAAI,QAAQ,KAAA,IAAS,CAAC,KAAK;YACzB,MAAM,IAAI,MACR;QAEJ;QACA,IAAI,QAAQ,KAAA,IAAS,CAAC,iBAAiB,GAAG,GAAG;YAC3C,MAAM,IAAI,MAAM,4BAA4B;QAC9C;QACA,OAAO;IACT;IAEA,MAAM,eAAe,IAAI,UAAA,CAAW,2BAA2B,IAAI,eAAe;IAElF,IAAI,4RAAc,iBAAA,EAAe,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;IAGlD,cAAc,YAAY,KAAA,CAAM,GAAG,CAAA,CAAE;IAErC,IAAI,QAAQ,QAAA,EAAU;QACpB,cAAc,QAAQ,QAAA;IACxB,OAAA,IAAW,iBAAiB,iBAAiB,QAAQ,MAAA,IAAU,QAAQ,WAAA,EAAa;QAClF,cAAc,CAAA,MAAA,EAAS,QAAQ,MAAM,EAAA;IACvC;IAEA,OAAO;QACL;QACA;IACF;AACF;AAQO,SAAS,iBAAiB,MAAc,EAAA,EAAI;IACjD,IAAI;QACF,MAAM,iBAAiB,IAAI,UAAA,CAAW,2BAA2B,KAAK,IAAI,UAAA,CAAW,2BAA2B;QAEhH,MAAM,2SAA6B,iBAAA,EAAe,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK,EAAE,EAAE,QAAA,CAAS,GAAG;QAEvF,OAAO,kBAAkB;IAC3B,EAAA,OAAQ;QACN,OAAO;IACT;AACF;AAEO,SAAS,6BAA6B;IAC3C,MAAM,uBAAuB,aAAA,GAAA,IAAI,IAAqB;IAEtD,OAAO;QACL,mBAAmB,CAAC,QAA+B;YACjD,IAAI,CAAC,KAAK;gBACR,OAAO;YACT;YAEA,MAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI,QAAA;YACrD,IAAI,MAAM,qBAAqB,GAAA,CAAI,QAAQ;YAC3C,IAAI,QAAQ,KAAA,GAAW;gBACrB,gRAAM,0BAAA,CAAwB,IAAA,CAAK,CAAA,IAAK,SAAS,QAAA,CAAS,CAAC,CAAC;gBAC5D,qBAAqB,GAAA,CAAI,UAAU,GAAG;YACxC;YACA,OAAO;QACT;IACF;AACF;AAEO,SAAS,gCAAgC,MAAA,EAAyB;IACvE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEO,SAAS,+BAA+B,MAAA,EAAyB;IACtE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEO,SAAS,2BAA2B,MAAA,EAAyB;IAClE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEO,SAAS,0BAA0B,MAAA,EAAyB;IACjE,OAAO,OAAO,UAAA,CAAW,OAAO,KAAK,OAAO,UAAA,CAAW,UAAU;AACnE;AAEA,eAAsB,gBACpB,cAAA,EACA,SAAuB,WAAW,MAAA,CAAO,MAAA,EACxB;IACjB,MAAM,OAAO,IAAI,YAAY,EAAE,MAAA,CAAO,cAAc;IACpD,MAAM,SAAS,MAAM,OAAO,MAAA,CAAO,SAAS,IAAI;IAChD,MAAM,eAAe,OAAO,YAAA,CAAa,GAAG,IAAI,WAAW,MAAM,CAAC;IAElE,qRAAO,iBAAA,EAAe,YAAY,EAAE,OAAA,CAAQ,QAAQ,GAAG,EAAE,OAAA,CAAQ,QAAQ,GAAG,EAAE,SAAA,CAAU,GAAG,CAAC;AAC9F;AAEO,IAAM,wBAAwB,CAAC,YAAoB,iBAAiC;IACzF,OAAO,GAAG,UAAU,CAAA,CAAA,EAAI,YAAY,EAAA;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/runtimeEnvironment.ts"], "sourcesContent": ["export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n"], "names": [], "mappings": ";;;;;;AAAO,IAAM,2BAA2B,MAAe;IACrD,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAIT,OAAO;AACT;AAEO,IAAM,oBAAoB,MAAe;IAC9C,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAGT,OAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;IACpD,IAAI;QACF,OAAO,QAAQ,IAAI,wCAAa;IAElC,EAAA,OAAQ,CAAC;IAGT,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/deprecated.ts"], "sourcesContent": ["import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;AAqBA,IAAM,oBAAoB,aAAA,GAAA,IAAI,IAAY;AACnC,IAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;IACjF,MAAM,4RAAc,oBAAA,CAAkB,OAAK,uSAAA,CAAwB;IACnE,MAAM,YAAY,OAAO;IACzB,IAAI,kBAAkB,GAAA,CAAI,SAAS,KAAK,aAAa;QACnD;IACF;IACA,kBAAkB,GAAA,CAAI,SAAS;IAE/B,QAAQ,IAAA,CACN,CAAA,8BAAA,EAAiC,MAAM,CAAA;AAAA,EAAmE,OAAO,EAAA;AAErH;AAyBO,IAAM,qBAAqB,CAAC,KAAe,UAAkB,SAAiB,WAAW,KAAA,KAAgB;IAC9G,MAAM,SAAS,WAAW,MAAM,IAAI,SAAA;IAEpC,IAAI,QAAQ,MAAA,CAAO,QAAQ,CAAA;IAC3B,OAAO,cAAA,CAAe,QAAQ,UAAU;QACtC,MAAM;YACJ,WAAW,UAAU,SAAS,GAAG,IAAI,IAAI,CAAA,CAAA,EAAI,QAAQ,EAAE;YACvD,OAAO;QACT;QACA,KAAI,CAAA,EAAY;YACd,QAAQ;QACV;IACF,CAAC;AACH;AAYO,IAAM,2BAA2B,CACtC,KACA,UACA,SACA,QACS;IACT,IAAI,QAAQ,GAAA,CAAI,QAAQ,CAAA;IACxB,OAAO,cAAA,CAAe,KAAK,UAAU;QACnC,MAAM;YACJ,WAAW,UAAU,SAAS,GAAG;YACjC,OAAO;QACT;QACA,KAAI,CAAA,EAAY;YACd,QAAQ;QACV;IACF,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/error.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/types';\n\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n  retryAfter?: number;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\nexport function isKnownError(error: any): error is ClerkAPIResponseError | ClerkRuntimeError | MetamaskError {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param {any} err - The error object to check.\n * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\nexport function isReverificationCancelledError(err: any) {\n  return isClerkRuntimeError(err) && err.code === 'reverification_cancelled';\n}\n\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport function errorToJSON(error: ClerkAPIError | null): ClerkAPIErrorJSON {\n  return {\n    code: error?.code || '',\n    message: error?.message || '',\n    long_message: error?.longMessage,\n    meta: {\n      param_name: error?.meta?.paramName,\n      session_id: error?.meta?.sessionId,\n      email_addresses: error?.meta?.emailAddresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n  retryAfter?: number;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId, retryAfter }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.retryAfter = retryAfter;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, can be used for localization.\n   *\n   * @type {string}\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    const prefix = '🔒 Clerk:';\n    const regex = new RegExp(prefix.replace(' ', '\\\\s*'), 'i');\n    const sanitized = message.replace(regex, '');\n    const _message = `${prefix} ${sanitized.trim()}\\n\\n(code=\"${code}\")\\n`;\n    super(_message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = _message;\n    this.clerkRuntimeError = true;\n    this.name = 'ClerkRuntimeError';\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns {string} A formatted string with the error name and message.\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    this.name = 'EmailLinkError' as const;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err.name === 'EmailLinkError';\n}\n\n/**\n * @deprecated Use `EmailLinkErrorCodeStatus` instead.\n *\n * @hidden\n */\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n};\n\nexport const EmailLinkErrorCodeStatus = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n} as const;\n\nconst DefaultMessages = Object.freeze({\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n\n  throwMissingSecretKeyError(): never;\n\n  throwMissingClerkProviderError(params: { source?: string }): never;\n\n  throw(message: string): never;\n}\n\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n\n    throwMissingSecretKeyError(): never {\n      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));\n    },\n\n    throwMissingClerkProviderError(params: { source?: string }): never {\n      throw new Error(buildMessage(messages.MissingClerkProvider, params));\n    },\n\n    throw(message: string): never {\n      throw new Error(buildMessage(message));\n    },\n  };\n}\n\ntype ClerkWebAuthnErrorCode =\n  // Generic\n  | 'passkey_not_supported'\n  | 'passkey_pa_not_supported'\n  | 'passkey_invalid_rpID_or_domain'\n  | 'passkey_already_exists'\n  | 'passkey_operation_aborted'\n  // Retrieval\n  | 'passkey_retrieval_cancelled'\n  | 'passkey_retrieval_failed'\n  // Registration\n  | 'passkey_registration_cancelled'\n  | 'passkey_registration_failed';\n\nexport class ClerkWebAuthnError extends ClerkRuntimeError {\n  /**\n   * A unique code identifying the error, can be used for localization.\n   */\n  code: ClerkWebAuthnErrorCode;\n\n  constructor(message: string, { code }: { code: ClerkWebAuthnErrorCode }) {\n    super(message, { code });\n    this.code = code;\n  }\n}\n"], "names": ["packageName", "customMessages"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,oBAAoB,CAAA,EAAiB;IACnD,MAAM,SAAS,GAAG;IAClB,MAAM,OAAO,GAAG,QAAA,CAAS,CAAC,CAAA,EAAG;IAC7B,OAAO,SAAS,4BAA4B,WAAW;AACzD;AAEO,SAAS,eAAe,CAAA,EAAmC;IAChE,OAAO;QAAC;QAAmB;QAAuB,uBAAuB;KAAA,CAAE,QAAA,CAAS,EAAE,MAAA,CAAO,CAAC,CAAA,CAAE,IAAI;AACtG;AAEO,SAAS,WAAW,CAAA,EAAiB;IAC1C,MAAM,SAAS,GAAG;IAClB,OAAO,CAAC,CAAC,UAAU,UAAU,OAAO,SAAS;AAC/C;AAEO,SAAS,eAAe,CAAA,EAAiB;IAE9C,MAAM,UAAA,CAAW,GAAG,EAAE,OAAO,GAAG,EAAE,IAAI,EAAA,IAAM,EAAA,EAAI,WAAA,CAAY,EAAE,OAAA,CAAQ,QAAQ,EAAE;IAChF,OAAO,QAAQ,QAAA,CAAS,cAAc;AACxC;AAiBO,SAAS,aAAa,KAAA,EAAgF;IAC3G,OAAO,wBAAwB,KAAK,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,KAAK;AAC9F;AAEO,SAAS,wBAAwB,GAAA,EAAwC;IAC9E,OAAO,gBAAgB;AACzB;AAkBO,SAAS,oBAAoB,GAAA,EAAoC;IACtE,OAAO,uBAAuB;AAChC;AAEO,SAAS,+BAA+B,GAAA,EAAU;IACvD,OAAO,oBAAoB,GAAG,KAAK,IAAI,IAAA,KAAS;AAClD;AAEO,SAAS,gBAAgB,GAAA,EAAgC;IAC9D,OAAO,UAAU,OAAO;QAAC;QAAM;QAAO,KAAK;KAAA,CAAE,QAAA,CAAS,IAAI,IAAI,KAAK,aAAa;AAClF;AAEO,SAAS,kBAAkB,GAAA,EAAU;IAC1C,OAAO,wBAAwB,GAAG,KAAK,IAAI,MAAA,EAAA,CAAS,CAAC,CAAA,EAAG,SAAS;AACnE;AAEO,SAAS,qBAAqB,GAAA,EAAU;IAC7C,OAAO,wBAAwB,GAAG,KAAK,IAAI,MAAA,EAAA,CAAS,CAAC,CAAA,EAAG,SAAS;AACnE;AAEO,SAAS,YAAY,OAA4B,CAAC,CAAA,EAAoB;IAC3E,OAAO,KAAK,MAAA,GAAS,IAAI,KAAK,GAAA,CAAI,UAAU,IAAI,CAAC,CAAA;AACnD;AAEO,SAAS,WAAW,KAAA,EAAyC;IAClE,OAAO;QACL,MAAM,MAAM,IAAA;QACZ,SAAS,MAAM,OAAA;QACf,aAAa,MAAM,YAAA;QACnB,MAAM;YACJ,WAAW,OAAO,MAAM;YACxB,WAAW,OAAO,MAAM;YACxB,gBAAgB,OAAO,MAAM;YAC7B,aAAa,OAAO,MAAM;YAC1B,QAAQ,OAAO,MAAM;QACvB;IACF;AACF;AAEO,SAAS,YAAY,KAAA,EAAgD;IAC1E,OAAO;QACL,MAAM,OAAO,QAAQ;QACrB,SAAS,OAAO,WAAW;QAC3B,cAAc,OAAO;QACrB,MAAM;YACJ,YAAY,OAAO,MAAM;YACzB,YAAY,OAAO,MAAM;YACzB,iBAAiB,OAAO,MAAM;YAC9B,aAAa,OAAO,MAAM;YAC1B,QAAQ,OAAO,MAAM;QACvB;IACF;AACF;AAEO,IAAM,wBAAN,MAAM,+BAA8B,MAAM;IAU/C,YAAY,OAAA,EAAiB,EAAE,IAAA,EAAM,MAAA,EAAQ,YAAA,EAAc,UAAA,CAAW,CAAA,CAA4B;QAChG,KAAA,CAAM,OAAO;QAYf,IAAA,CAAO,QAAA,GAAW,MAAM;YACtB,IAAI,UAAU,CAAA,CAAA,EAAI,IAAA,CAAK,IAAI,CAAA;QAAA,EAAc,IAAA,CAAK,OAAO,CAAA;OAAA,EAAY,IAAA,CAAK,MAAM,CAAA;mBAAA,EAAwB,IAAA,CAAK,MAAA,CAAO,GAAA,CAC9G,CAAA,IAAK,KAAK,SAAA,CAAU,CAAC,IACtB;YAED,IAAI,IAAA,CAAK,YAAA,EAAc;gBACrB,WAAW,CAAA;gBAAA,EAAqB,IAAA,CAAK,YAAY,EAAA;YACnD;YAEA,OAAO;QACT;QApBE,OAAO,cAAA,CAAe,IAAA,EAAM,uBAAsB,SAAS;QAE3D,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,MAAA,GAAS,YAAY,IAAI;IAChC;AAaF;AASO,IAAM,oBAAN,MAAM,2BAA0B,MAAM;IAiB3C,YAAY,OAAA,EAAiB,EAAE,IAAA,CAAK,CAAA,CAAqB;QACvD,MAAM,SAAS;QACf,MAAM,QAAQ,IAAI,OAAO,OAAO,OAAA,CAAQ,KAAK,MAAM,GAAG,GAAG;QACzD,MAAM,YAAY,QAAQ,OAAA,CAAQ,OAAO,EAAE;QAC3C,MAAM,WAAW,GAAG,MAAM,CAAA,CAAA,EAAI,UAAU,IAAA,CAAK,CAAC,CAAA;;OAAA,EAAc,IAAI,CAAA;AAAA,CAAA;QAChE,KAAA,CAAM,QAAQ;QAehB;;;;KAAA,GAAA,IAAA,CAAO,QAAA,GAAW,MAAM;YACtB,OAAO,CAAA,CAAA,EAAI,IAAA,CAAK,IAAI,CAAA;QAAA,EAAc,IAAA,CAAK,OAAO,EAAA;QAChD;QAfE,OAAO,cAAA,CAAe,IAAA,EAAM,mBAAkB,SAAS;QAEvD,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,iBAAA,GAAoB;QACzB,IAAA,CAAK,IAAA,GAAO;IACd;AAUF;AAEO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;IAGxC,YAAY,IAAA,CAAc;QACxB,KAAA,CAAM,IAAI;QACV,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,IAAA,GAAO;QACZ,OAAO,cAAA,CAAe,IAAA,EAAM,gBAAe,SAAS;IACtD;AACF;AAEO,SAAS,iBAAiB,GAAA,EAAmC;IAClE,OAAO,IAAI,IAAA,KAAS;AACtB;AAOO,IAAM,qBAAqB;IAChC,SAAS;IACT,QAAQ;IACR,gBAAgB;AAClB;AAEO,IAAM,2BAA2B;IACtC,SAAS;IACT,QAAQ;IACR,gBAAgB;AAClB;AAEA,IAAM,kBAAkB,OAAO,MAAA,CAAO;IACpC,6BAA6B,CAAA,gJAAA,CAAA;IAC7B,mCAAmC,CAAA,uJAAA,CAAA;IACnC,mCAAmC,CAAA,sGAAA,CAAA;IACnC,8BAA8B,CAAA,iGAAA,CAAA;IAC9B,sBAAsB,CAAA,gIAAA,CAAA;AACxB,CAAC;AA+BM,SAAS,kBAAkB,EAAE,WAAA,EAAa,cAAA,CAAe,CAAA,EAAsC;IACpG,IAAI,MAAM;IAEV,MAAM,WAAW;QACf,GAAG,eAAA;QACH,GAAG,cAAA;IACL;IAEA,SAAS,aAAa,UAAA,EAAoB,YAAA,EAAgD;QACxF,IAAI,CAAC,cAAc;YACjB,OAAO,GAAG,GAAG,CAAA,EAAA,EAAK,UAAU,EAAA;QAC9B;QAEA,IAAI,MAAM;QACV,MAAM,UAAU,WAAW,QAAA,CAAS,uBAAuB;QAE3D,KAAA,MAAW,SAAS,QAAS;YAC3B,MAAM,cAAA,CAAe,YAAA,CAAa,KAAA,CAAM,CAAC,CAAC,CAAA,IAAK,EAAA,EAAI,QAAA,CAAS;YAC5D,MAAM,IAAI,OAAA,CAAQ,CAAA,EAAA,EAAK,KAAA,CAAM,CAAC,CAAC,CAAA,EAAA,CAAA,EAAM,WAAW;QAClD;QAEA,OAAO,GAAG,GAAG,CAAA,EAAA,EAAK,GAAG,EAAA;IACvB;IAEA,OAAO;QACL,gBAAe,EAAE,aAAAA,YAAAA,CAAY,CAAA,EAAsC;YACjE,IAAI,OAAOA,iBAAgB,UAAU;gBACnC,MAAMA;YACR;YACA,OAAO,IAAA;QACT;QAEA,aAAY,EAAE,gBAAAC,eAAAA,CAAe,CAAA,EAAsC;YACjE,OAAO,MAAA,CAAO,UAAUA,mBAAkB,CAAC,CAAC;YAC5C,OAAO,IAAA;QACT;QAEA,iCAAgC,MAAA,EAAiC;YAC/D,MAAM,IAAI,MAAM,aAAa,SAAS,iCAAA,EAAmC,MAAM,CAAC;QAClF;QAEA,sBAAqB,MAAA,EAAiC;YACpD,MAAM,IAAI,MAAM,aAAa,SAAS,2BAAA,EAA6B,MAAM,CAAC;QAC5E;QAEA,kCAAyC;YACvC,MAAM,IAAI,MAAM,aAAa,SAAS,iCAAiC,CAAC;QAC1E;QAEA,6BAAoC;YAClC,MAAM,IAAI,MAAM,aAAa,SAAS,4BAA4B,CAAC;QACrE;QAEA,gCAA+B,MAAA,EAAoC;YACjE,MAAM,IAAI,MAAM,aAAa,SAAS,oBAAA,EAAsB,MAAM,CAAC;QACrE;QAEA,OAAM,OAAA,EAAwB;YAC5B,MAAM,IAAI,MAAM,aAAa,OAAO,CAAC;QACvC;IACF;AACF;AAgBO,IAAM,qBAAN,cAAiC,kBAAkB;IAMxD,YAAY,OAAA,EAAiB,EAAE,IAAA,CAAK,CAAA,CAAqC;QACvE,KAAA,CAAM,SAAS;YAAE;QAAK,CAAC;QACvB,IAAA,CAAK,IAAA,GAAO;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/authorization.ts"], "sourcesContent": ["import type {\n  ActClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  PendingSessionOptions,\n  ReverificationConfig,\n  SessionStatusClaim,\n  SessionVerificationLevel,\n  SessionVerificationTypes,\n  SignOut,\n  UseAuthReturn,\n} from '@clerk/types';\n\ntype TypesToConfig = Record<SessionVerificationTypes, Exclude<ReverificationConfig, SessionVerificationTypes>>;\ntype AuthorizationOptions = {\n  userId: string | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: string | null | undefined;\n  orgPermissions: string[] | null | undefined;\n  factorVerificationAge: [number, number] | null;\n  features: string | null | undefined;\n  plans: string | null | undefined;\n};\n\ntype CheckOrgAuthorization = (\n  params: { role?: OrganizationCustomRoleKey; permission?: OrganizationCustomPermissionKey },\n  options: Pick<AuthorizationOptions, 'orgId' | 'orgRole' | 'orgPermissions'>,\n) => boolean | null;\n\ntype CheckBillingAuthorization = (\n  params: { feature?: string; plan?: string },\n  options: Pick<AuthorizationOptions, 'plans' | 'features'>,\n) => boolean | null;\n\ntype CheckReverificationAuthorization = (\n  params: {\n    reverification?: ReverificationConfig;\n  },\n  { factorVerificationAge }: AuthorizationOptions,\n) => boolean | null;\n\nconst TYPES_TO_OBJECTS: TypesToConfig = {\n  strict_mfa: {\n    afterMinutes: 10,\n    level: 'multi_factor',\n  },\n  strict: {\n    afterMinutes: 10,\n    level: 'second_factor',\n  },\n  moderate: {\n    afterMinutes: 60,\n    level: 'second_factor',\n  },\n  lax: {\n    afterMinutes: 1_440,\n    level: 'second_factor',\n  },\n};\n\nconst ALLOWED_LEVELS = new Set<SessionVerificationLevel>(['first_factor', 'second_factor', 'multi_factor']);\n\nconst ALLOWED_TYPES = new Set<SessionVerificationTypes>(['strict_mfa', 'strict', 'moderate', 'lax']);\n\n// Helper functions\nconst isValidMaxAge = (maxAge: any) => typeof maxAge === 'number' && maxAge > 0;\nconst isValidLevel = (level: any) => ALLOWED_LEVELS.has(level);\nconst isValidVerificationType = (type: any) => ALLOWED_TYPES.has(type);\n\nconst prefixWithOrg = (value: string) => (value.startsWith('org:') ? value : `org:${value}`);\n\n/**\n * Checks if a user has the required organization-level authorization.\n * Verifies if the user has the specified role or permission within their organization.\n * @returns null, if unable to determine due to missing data or unspecified role/permission.\n */\nconst checkOrgAuthorization: CheckOrgAuthorization = (params, options) => {\n  const { orgId, orgRole, orgPermissions } = options;\n  if (!params.role && !params.permission) {\n    return null;\n  }\n\n  if (!orgId || !orgRole || !orgPermissions) {\n    return null;\n  }\n\n  if (params.permission) {\n    return orgPermissions.includes(prefixWithOrg(params.permission));\n  }\n\n  if (params.role) {\n    return orgRole === prefixWithOrg(params.role);\n  }\n  return null;\n};\n\nconst checkForFeatureOrPlan = (claim: string, featureOrPlan: string) => {\n  const { org: orgFeatures, user: userFeatures } = splitByScope(claim);\n  const [scope, _id] = featureOrPlan.split(':');\n  const id = _id || scope;\n\n  if (scope === 'org') {\n    return orgFeatures.includes(id);\n  } else if (scope === 'user') {\n    return userFeatures.includes(id);\n  } else {\n    // Since org scoped features will not exist if there is not an active org, merging is safe.\n    return [...orgFeatures, ...userFeatures].includes(id);\n  }\n};\n\nconst checkBillingAuthorization: CheckBillingAuthorization = (params, options) => {\n  const { features, plans } = options;\n\n  if (params.feature && features) {\n    return checkForFeatureOrPlan(features, params.feature);\n  }\n\n  if (params.plan && plans) {\n    return checkForFeatureOrPlan(plans, params.plan);\n  }\n  return null;\n};\n\nconst splitByScope = (fea: string | null | undefined) => {\n  const features = fea ? fea.split(',').map(f => f.trim()) : [];\n\n  // TODO: make this more efficient\n  return {\n    org: features.filter(f => f.split(':')[0].includes('o')).map(f => f.split(':')[1]),\n    user: features.filter(f => f.split(':')[0].includes('u')).map(f => f.split(':')[1]),\n  };\n};\n\nconst validateReverificationConfig = (config: ReverificationConfig | undefined | null) => {\n  if (!config) {\n    return false;\n  }\n\n  const convertConfigToObject = (config: ReverificationConfig) => {\n    if (typeof config === 'string') {\n      return TYPES_TO_OBJECTS[config];\n    }\n    return config;\n  };\n\n  const isValidStringValue = typeof config === 'string' && isValidVerificationType(config);\n  const isValidObjectValue =\n    typeof config === 'object' && isValidLevel(config.level) && isValidMaxAge(config.afterMinutes);\n\n  if (isValidStringValue || isValidObjectValue) {\n    return convertConfigToObject.bind(null, config);\n  }\n\n  return false;\n};\n\n/**\n * Evaluates if the user meets re-verification authentication requirements.\n * Compares the user's factor verification ages against the specified maxAge.\n * Handles different verification levels (first factor, second factor, multi-factor).\n * @returns null, if requirements or verification data are missing.\n */\nconst checkReverificationAuthorization: CheckReverificationAuthorization = (params, { factorVerificationAge }) => {\n  if (!params.reverification || !factorVerificationAge) {\n    return null;\n  }\n\n  const isValidReverification = validateReverificationConfig(params.reverification);\n  if (!isValidReverification) {\n    return null;\n  }\n\n  const { level, afterMinutes } = isValidReverification();\n  const [factor1Age, factor2Age] = factorVerificationAge;\n\n  // -1 indicates the factor group (1fa,2fa) is not enabled\n  // -1 for 1fa is not a valid scenario, but we need to make sure we handle it properly\n  const isValidFactor1 = factor1Age !== -1 ? afterMinutes > factor1Age : null;\n  const isValidFactor2 = factor2Age !== -1 ? afterMinutes > factor2Age : null;\n\n  switch (level) {\n    case 'first_factor':\n      return isValidFactor1;\n    case 'second_factor':\n      return factor2Age !== -1 ? isValidFactor2 : isValidFactor1;\n    case 'multi_factor':\n      return factor2Age === -1 ? isValidFactor1 : isValidFactor1 && isValidFactor2;\n  }\n};\n\n/**\n * Creates a function for comprehensive user authorization checks.\n * Combines organization-level and reverification authentication checks.\n * The returned function authorizes if both checks pass, or if at least one passes\n * when the other is indeterminate. Fails if userId is missing.\n */\nconst createCheckAuthorization = (options: AuthorizationOptions): CheckAuthorizationWithCustomPermissions => {\n  return (params): boolean => {\n    if (!options.userId) {\n      return false;\n    }\n\n    const billingAuthorization = checkBillingAuthorization(params, options);\n    const orgAuthorization = checkOrgAuthorization(params, options);\n    const reverificationAuthorization = checkReverificationAuthorization(params, options);\n\n    if ([billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === null)) {\n      return [billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === true);\n    }\n\n    return [billingAuthorization || orgAuthorization, reverificationAuthorization].every(a => a === true);\n  };\n};\n\ntype AuthStateOptions = {\n  authObject: {\n    userId?: string | null;\n    sessionId?: string | null;\n    sessionStatus?: SessionStatusClaim | null;\n    sessionClaims?: JwtPayload | null;\n    actor?: ActClaim | null;\n    orgId?: string | null;\n    orgRole?: OrganizationCustomRoleKey | null;\n    orgSlug?: string | null;\n    orgPermissions?: OrganizationCustomPermissionKey[] | null;\n    getToken: GetToken;\n    signOut: SignOut;\n    has: (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => boolean;\n  };\n  options: PendingSessionOptions;\n};\n\n/**\n * Shared utility function that centralizes auth state resolution logic,\n * preventing duplication across different packages\n * @internal\n */\nconst resolveAuthState = ({\n  authObject: {\n    sessionId,\n    sessionStatus,\n    userId,\n    actor,\n    orgId,\n    orgRole,\n    orgSlug,\n    signOut,\n    getToken,\n    has,\n    sessionClaims,\n  },\n  options: { treatPendingAsSignedOut = true },\n}: AuthStateOptions): UseAuthReturn | undefined => {\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      sessionClaims: undefined,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (treatPendingAsSignedOut && sessionStatus === 'pending') {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId: null,\n      userId: null,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n};\n\nexport { createCheckAuthorization, validateReverificationConfig, resolveAuthState, splitByScope };\n"], "names": ["config"], "mappings": ";;;;;;;AA4CA,IAAM,mBAAkC;IACtC,YAAY;QACV,cAAc;QACd,OAAO;IACT;IACA,QAAQ;QACN,cAAc;QACd,OAAO;IACT;IACA,UAAU;QACR,cAAc;QACd,OAAO;IACT;IACA,KAAK;QACH,cAAc;QACd,OAAO;IACT;AACF;AAEA,IAAM,iBAAiB,aAAA,GAAA,IAAI,IAA8B;IAAC;IAAgB;IAAiB,cAAc;CAAC;AAE1G,IAAM,gBAAgB,aAAA,GAAA,IAAI,IAA8B;IAAC;IAAc;IAAU;IAAY,KAAK;CAAC;AAGnG,IAAM,gBAAgB,CAAC,SAAgB,OAAO,WAAW,YAAY,SAAS;AAC9E,IAAM,eAAe,CAAC,QAAe,eAAe,GAAA,CAAI,KAAK;AAC7D,IAAM,0BAA0B,CAAC,OAAc,cAAc,GAAA,CAAI,IAAI;AAErE,IAAM,gBAAgB,CAAC,QAAmB,MAAM,UAAA,CAAW,MAAM,IAAI,QAAQ,CAAA,IAAA,EAAO,KAAK,EAAA;AAOzF,IAAM,wBAA+C,CAAC,QAAQ,YAAY;IACxE,MAAM,EAAE,KAAA,EAAO,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI;IAC3C,IAAI,CAAC,OAAO,IAAA,IAAQ,CAAC,OAAO,UAAA,EAAY;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB;QACzC,OAAO;IACT;IAEA,IAAI,OAAO,UAAA,EAAY;QACrB,OAAO,eAAe,QAAA,CAAS,cAAc,OAAO,UAAU,CAAC;IACjE;IAEA,IAAI,OAAO,IAAA,EAAM;QACf,OAAO,YAAY,cAAc,OAAO,IAAI;IAC9C;IACA,OAAO;AACT;AAEA,IAAM,wBAAwB,CAAC,OAAe,kBAA0B;IACtE,MAAM,EAAE,KAAK,WAAA,EAAa,MAAM,YAAA,CAAa,CAAA,GAAI,aAAa,KAAK;IACnE,MAAM,CAAC,OAAO,GAAG,CAAA,GAAI,cAAc,KAAA,CAAM,GAAG;IAC5C,MAAM,KAAK,OAAO;IAElB,IAAI,UAAU,OAAO;QACnB,OAAO,YAAY,QAAA,CAAS,EAAE;IAChC,OAAA,IAAW,UAAU,QAAQ;QAC3B,OAAO,aAAa,QAAA,CAAS,EAAE;IACjC,OAAO;QAEL,OAAO,CAAC;eAAG,aAAa;eAAG,YAAY;SAAA,CAAE,QAAA,CAAS,EAAE;IACtD;AACF;AAEA,IAAM,4BAAuD,CAAC,QAAQ,YAAY;IAChF,MAAM,EAAE,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI;IAE5B,IAAI,OAAO,OAAA,IAAW,UAAU;QAC9B,OAAO,sBAAsB,UAAU,OAAO,OAAO;IACvD;IAEA,IAAI,OAAO,IAAA,IAAQ,OAAO;QACxB,OAAO,sBAAsB,OAAO,OAAO,IAAI;IACjD;IACA,OAAO;AACT;AAEA,IAAM,eAAe,CAAC,QAAmC;IACvD,MAAM,WAAW,MAAM,IAAI,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,IAAA,CAAK,CAAC,IAAI,CAAC,CAAA;IAG5D,OAAO;QACL,KAAK,SAAS,MAAA,CAAO,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,QAAA,CAAS,GAAG,CAAC,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;QACjF,MAAM,SAAS,MAAA,CAAO,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,QAAA,CAAS,GAAG,CAAC,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC;IACpF;AACF;AAEA,IAAM,+BAA+B,CAAC,WAAoD;IACxF,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,wBAAwB,CAACA,YAAiC;QAC9D,IAAI,OAAOA,YAAW,UAAU;YAC9B,OAAO,gBAAA,CAAiBA,OAAM,CAAA;QAChC;QACA,OAAOA;IACT;IAEA,MAAM,qBAAqB,OAAO,WAAW,YAAY,wBAAwB,MAAM;IACvF,MAAM,qBACJ,OAAO,WAAW,YAAY,aAAa,OAAO,KAAK,KAAK,cAAc,OAAO,YAAY;IAE/F,IAAI,sBAAsB,oBAAoB;QAC5C,OAAO,sBAAsB,IAAA,CAAK,MAAM,MAAM;IAChD;IAEA,OAAO;AACT;AAQA,IAAM,mCAAqE,CAAC,QAAQ,EAAE,qBAAA,CAAsB,CAAA,KAAM;IAChH,IAAI,CAAC,OAAO,cAAA,IAAkB,CAAC,uBAAuB;QACpD,OAAO;IACT;IAEA,MAAM,wBAAwB,6BAA6B,OAAO,cAAc;IAChF,IAAI,CAAC,uBAAuB;QAC1B,OAAO;IACT;IAEA,MAAM,EAAE,KAAA,EAAO,YAAA,CAAa,CAAA,GAAI,sBAAsB;IACtD,MAAM,CAAC,YAAY,UAAU,CAAA,GAAI;IAIjC,MAAM,iBAAiB,eAAe,CAAA,IAAK,eAAe,aAAa;IACvE,MAAM,iBAAiB,eAAe,CAAA,IAAK,eAAe,aAAa;IAEvE,OAAQ,OAAO;QACb,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,eAAe,CAAA,IAAK,iBAAiB;QAC9C,KAAK;YACH,OAAO,eAAe,CAAA,IAAK,iBAAiB,kBAAkB;IAClE;AACF;AAQA,IAAM,2BAA2B,CAAC,YAA2E;IAC3G,OAAO,CAAC,WAAoB;QAC1B,IAAI,CAAC,QAAQ,MAAA,EAAQ;YACnB,OAAO;QACT;QAEA,MAAM,uBAAuB,0BAA0B,QAAQ,OAAO;QACtE,MAAM,mBAAmB,sBAAsB,QAAQ,OAAO;QAC9D,MAAM,8BAA8B,iCAAiC,QAAQ,OAAO;QAEpF,IAAI;YAAC,wBAAwB;YAAkB,2BAA2B;SAAA,CAAE,IAAA,CAAK,CAAA,IAAK,MAAM,IAAI,GAAG;YACjG,OAAO;gBAAC,wBAAwB;gBAAkB,2BAA2B;aAAA,CAAE,IAAA,CAAK,CAAA,IAAK,MAAM,IAAI;QACrG;QAEA,OAAO;YAAC,wBAAwB;YAAkB,2BAA2B;SAAA,CAAE,KAAA,CAAM,CAAA,IAAK,MAAM,IAAI;IACtG;AACF;AAyBA,IAAM,mBAAmB,CAAC,EACxB,YAAY,EACV,SAAA,EACA,aAAA,EACA,MAAA,EACA,KAAA,EACA,KAAA,EACA,OAAA,EACA,OAAA,EACA,OAAA,EACA,QAAA,EACA,GAAA,EACA,aAAA,EACF,EACA,SAAS,EAAE,0BAA0B,IAAA,CAAK,CAAA,EAC5C,KAAmD;IACjD,IAAI,cAAc,KAAA,KAAa,WAAW,KAAA,GAAW;QACnD,OAAO;YACL,UAAU;YACV,YAAY,KAAA;YACZ;YACA,eAAe,KAAA;YACf;YACA,OAAO,KAAA;YACP,OAAO,KAAA;YACP,SAAS,KAAA;YACT,SAAS,KAAA;YACT,KAAK,KAAA;YACL;YACA;QACF;IACF;IAEA,IAAI,cAAc,QAAQ,WAAW,MAAM;QACzC,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA,eAAe;YACf,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,KAAK,IAAM;YACX;YACA;QACF;IACF;IAEA,IAAI,2BAA2B,kBAAkB,WAAW;QAC1D,OAAO;YACL,UAAU;YACV,YAAY;YACZ,WAAW;YACX,QAAQ;YACR,eAAe;YACf,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,KAAK,IAAM;YACX;YACA;QACF;IACF;IAEA,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS;QACtE,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA;YACA,OAAO,SAAS;YAChB;YACA;YACA,SAAS,WAAW;YACpB;YACA;YACA;QACF;IACF;IAEA,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,OAAO;QACxD,OAAO;YACL,UAAU;YACV,YAAY;YACZ;YACA;YACA;YACA,OAAO,SAAS;YAChB,OAAO;YACP,SAAS;YACT,SAAS;YACT;YACA;YACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/jwtPayloadParser.ts"], "sourcesContent": ["import type {\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  SharedSignedInAuthObjectProperties,\n} from '@clerk/types';\n\nimport { splitByScope } from './authorization';\n\nexport const parsePermissions = ({ per, fpm }: { per?: string; fpm?: string }) => {\n  if (!per || !fpm) {\n    return { permissions: [], featurePermissionMap: [] };\n  }\n\n  const permissions = per.split(',').map(p => p.trim());\n\n  // TODO: make this more efficient\n  const featurePermissionMap = fpm\n    .split(',')\n    .map(permission => Number.parseInt(permission.trim(), 10))\n    .map((permission: number) =>\n      permission\n        .toString(2)\n        .padStart(permissions.length, '0')\n        .split('')\n        .map(bit => Number.parseInt(bit, 10))\n        .reverse(),\n    )\n    .filter(Boolean);\n\n  return { permissions, featurePermissionMap };\n};\n\nfunction buildOrgPermissions({\n  features,\n  permissions,\n  featurePermissionMap,\n}: {\n  features?: string[];\n  permissions?: string[];\n  featurePermissionMap?: number[][];\n}) {\n  // Early return if any required input is missing\n  if (!features || !permissions || !featurePermissionMap) {\n    return [];\n  }\n\n  const orgPermissions: string[] = [];\n\n  // Process each feature and its permissions in a single loop\n  for (let featureIndex = 0; featureIndex < features.length; featureIndex++) {\n    const feature = features[featureIndex];\n\n    if (featureIndex >= featurePermissionMap.length) {\n      continue;\n    }\n\n    const permissionBits = featurePermissionMap[featureIndex];\n    if (!permissionBits) continue;\n\n    for (let permIndex = 0; permIndex < permissionBits.length; permIndex++) {\n      if (permissionBits[permIndex] === 1) {\n        orgPermissions.push(`org:${feature}:${permissions[permIndex]}`);\n      }\n    }\n  }\n\n  return orgPermissions;\n}\n\n/**\n * @experimental\n *\n * Resolves the signed-in auth state from JWT claims.\n */\nconst __experimental_JWTPayloadToAuthObjectProperties = (claims: JwtPayload): SharedSignedInAuthObjectProperties => {\n  let orgId: string | undefined;\n  let orgRole: OrganizationCustomRoleKey | undefined;\n  let orgSlug: string | undefined;\n  let orgPermissions: OrganizationCustomPermissionKey[] | undefined;\n\n  // fva can be undefined for instances that have not opt-in\n  const factorVerificationAge = claims.fva ?? null;\n\n  // sts can be undefined for instances that have not opt-in\n  const sessionStatus = claims.sts ?? null;\n\n  switch (claims.v) {\n    case 2: {\n      if (claims.o) {\n        orgId = claims.o?.id;\n        orgSlug = claims.o?.slg;\n\n        if (claims.o?.rol) {\n          orgRole = `org:${claims.o?.rol}`;\n        }\n        const { org } = splitByScope(claims.fea);\n        const { permissions, featurePermissionMap } = parsePermissions({\n          per: claims.o?.per,\n          fpm: claims.o?.fpm,\n        });\n        orgPermissions = buildOrgPermissions({\n          features: org,\n          featurePermissionMap: featurePermissionMap,\n          permissions: permissions,\n        });\n      }\n      break;\n    }\n    default:\n      orgId = claims.org_id;\n      orgRole = claims.org_role;\n      orgSlug = claims.org_slug;\n      orgPermissions = claims.org_permissions;\n      break;\n  }\n\n  return {\n    sessionClaims: claims,\n    sessionId: claims.sid,\n    sessionStatus,\n    actor: claims.act,\n    userId: claims.sub,\n    orgId: orgId,\n    orgRole: orgRole,\n    orgSlug: orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n  };\n};\n\nexport { __experimental_JWTPayloadToAuthObjectProperties };\n"], "names": [], "mappings": ";;;;;;;;;AASO,IAAM,mBAAmB,CAAC,EAAE,GAAA,EAAK,GAAA,CAAI,CAAA,KAAsC;IAChF,IAAI,CAAC,OAAO,CAAC,KAAK;QAChB,OAAO;YAAE,aAAa,CAAC,CAAA;YAAG,sBAAsB,CAAC,CAAA;QAAE;IACrD;IAEA,MAAM,cAAc,IAAI,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,CAAA,IAAK,EAAE,IAAA,CAAK,CAAC;IAGpD,MAAM,uBAAuB,IAC1B,KAAA,CAAM,GAAG,EACT,GAAA,CAAI,CAAA,aAAc,OAAO,QAAA,CAAS,WAAW,IAAA,CAAK,GAAG,EAAE,CAAC,EACxD,GAAA,CAAI,CAAC,aACJ,WACG,QAAA,CAAS,CAAC,EACV,QAAA,CAAS,YAAY,MAAA,EAAQ,GAAG,EAChC,KAAA,CAAM,EAAE,EACR,GAAA,CAAI,CAAA,MAAO,OAAO,QAAA,CAAS,KAAK,EAAE,CAAC,EACnC,OAAA,CAAQ,GAEZ,MAAA,CAAO,OAAO;IAEjB,OAAO;QAAE;QAAa;IAAqB;AAC7C;AAEA,SAAS,oBAAoB,EAC3B,QAAA,EACA,WAAA,EACA,oBAAA,EACF,EAIG;IAED,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,sBAAsB;QACtD,OAAO,CAAC,CAAA;IACV;IAEA,MAAM,iBAA2B,CAAC,CAAA;IAGlC,IAAA,IAAS,eAAe,GAAG,eAAe,SAAS,MAAA,EAAQ,eAAgB;QACzE,MAAM,UAAU,QAAA,CAAS,YAAY,CAAA;QAErC,IAAI,gBAAgB,qBAAqB,MAAA,EAAQ;YAC/C;QACF;QAEA,MAAM,iBAAiB,oBAAA,CAAqB,YAAY,CAAA;QACxD,IAAI,CAAC,eAAgB,CAAA;QAErB,IAAA,IAAS,YAAY,GAAG,YAAY,eAAe,MAAA,EAAQ,YAAa;YACtE,IAAI,cAAA,CAAe,SAAS,CAAA,KAAM,GAAG;gBACnC,eAAe,IAAA,CAAK,CAAA,IAAA,EAAO,OAAO,CAAA,CAAA,EAAI,WAAA,CAAY,SAAS,CAAC,EAAE;YAChE;QACF;IACF;IAEA,OAAO;AACT;AAOA,IAAM,kDAAkD,CAAC,WAA2D;IAClH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IAGJ,MAAM,wBAAwB,OAAO,GAAA,IAAO;IAG5C,MAAM,gBAAgB,OAAO,GAAA,IAAO;IAEpC,OAAQ,OAAO,CAAA,EAAG;QAChB,KAAK;YAAG;gBACN,IAAI,OAAO,CAAA,EAAG;oBACZ,QAAQ,OAAO,CAAA,EAAG;oBAClB,UAAU,OAAO,CAAA,EAAG;oBAEpB,IAAI,OAAO,CAAA,EAAG,KAAK;wBACjB,UAAU,CAAA,IAAA,EAAO,OAAO,CAAA,EAAG,GAAG,EAAA;oBAChC;oBACA,MAAM,EAAE,GAAA,CAAI,CAAA,iRAAI,eAAA,EAAa,OAAO,GAAG;oBACvC,MAAM,EAAE,WAAA,EAAa,oBAAA,CAAqB,CAAA,GAAI,iBAAiB;wBAC7D,KAAK,OAAO,CAAA,EAAG;wBACf,KAAK,OAAO,CAAA,EAAG;oBACjB,CAAC;oBACD,iBAAiB,oBAAoB;wBACnC,UAAU;wBACV;wBACA;oBACF,CAAC;gBACH;gBACA;YACF;QACA;YACE,QAAQ,OAAO,MAAA;YACf,UAAU,OAAO,QAAA;YACjB,UAAU,OAAO,QAAA;YACjB,iBAAiB,OAAO,eAAA;YACxB;IACJ;IAEA,OAAO;QACL,eAAe;QACf,WAAW,OAAO,GAAA;QAClB;QACA,OAAO,OAAO,GAAA;QACd,QAAQ,OAAO,GAAA;QACf;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/compiled/path-to-regexp/index.js", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/pathToRegexp.ts"], "sourcesContent": ["/* eslint-disable no-redeclare, curly */\n\nfunction _(r) {\n  for (var n = [], e = 0; e < r.length; ) {\n    var a = r[e];\n    if (a === '*' || a === '+' || a === '?') {\n      n.push({\n        type: 'MODIFIER',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '\\\\') {\n      n.push({\n        type: 'ESCAPED_CHAR',\n        index: e++,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '{') {\n      n.push({\n        type: 'OPEN',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === '}') {\n      n.push({\n        type: 'CLOSE',\n        index: e,\n        value: r[e++],\n      });\n      continue;\n    }\n    if (a === ':') {\n      for (var u = '', t = e + 1; t < r.length; ) {\n        var c = r.charCodeAt(t);\n        if ((c >= 48 && c <= 57) || (c >= 65 && c <= 90) || (c >= 97 && c <= 122) || c === 95) {\n          u += r[t++];\n          continue;\n        }\n        break;\n      }\n      if (!u) throw new TypeError('Missing parameter name at '.concat(e));\n      n.push({\n        type: 'NAME',\n        index: e,\n        value: u,\n      }),\n        (e = t);\n      continue;\n    }\n    if (a === '(') {\n      var o = 1,\n        m = '',\n        t = e + 1;\n      if (r[t] === '?') throw new TypeError('Pattern cannot start with \"?\" at '.concat(t));\n      for (; t < r.length; ) {\n        if (r[t] === '\\\\') {\n          m += r[t++] + r[t++];\n          continue;\n        }\n        if (r[t] === ')') {\n          if ((o--, o === 0)) {\n            t++;\n            break;\n          }\n        } else if (r[t] === '(' && (o++, r[t + 1] !== '?'))\n          throw new TypeError('Capturing groups are not allowed at '.concat(t));\n        m += r[t++];\n      }\n      if (o) throw new TypeError('Unbalanced pattern at '.concat(e));\n      if (!m) throw new TypeError('Missing pattern at '.concat(e));\n      n.push({\n        type: 'PATTERN',\n        index: e,\n        value: m,\n      }),\n        (e = t);\n      continue;\n    }\n    n.push({\n      type: 'CHAR',\n      index: e,\n      value: r[e++],\n    });\n  }\n  return (\n    n.push({\n      type: 'END',\n      index: e,\n      value: '',\n    }),\n    n\n  );\n}\n\nfunction F(r, n) {\n  n === void 0 && (n = {});\n  for (\n    var e = _(r),\n      a = n.prefixes,\n      u = a === void 0 ? './' : a,\n      t = n.delimiter,\n      c = t === void 0 ? '/#?' : t,\n      o = [],\n      m = 0,\n      h = 0,\n      p = '',\n      f = function (l) {\n        if (h < e.length && e[h].type === l) return e[h++].value;\n      },\n      w = function (l) {\n        var v = f(l);\n        if (v !== void 0) return v;\n        var E = e[h],\n          N = E.type,\n          S = E.index;\n        throw new TypeError('Unexpected '.concat(N, ' at ').concat(S, ', expected ').concat(l));\n      },\n      d = function () {\n        for (var l = '', v; (v = f('CHAR') || f('ESCAPED_CHAR')); ) l += v;\n        return l;\n      },\n      M = function (l) {\n        for (var v = 0, E = c; v < E.length; v++) {\n          var N = E[v];\n          if (l.indexOf(N) > -1) return !0;\n        }\n        return !1;\n      },\n      A = function (l) {\n        var v = o[o.length - 1],\n          E = l || (v && typeof v == 'string' ? v : '');\n        if (v && !E)\n          throw new TypeError('Must have text between two parameters, missing text after \"'.concat(v.name, '\"'));\n        return !E || M(E) ? '[^'.concat(s(c), ']+?') : '(?:(?!'.concat(s(E), ')[^').concat(s(c), '])+?');\n      };\n    h < e.length;\n\n  ) {\n    var T = f('CHAR'),\n      x = f('NAME'),\n      C = f('PATTERN');\n    if (x || C) {\n      var g = T || '';\n      u.indexOf(g) === -1 && ((p += g), (g = '')),\n        p && (o.push(p), (p = '')),\n        o.push({\n          name: x || m++,\n          prefix: g,\n          suffix: '',\n          pattern: C || A(g),\n          modifier: f('MODIFIER') || '',\n        });\n      continue;\n    }\n    var i = T || f('ESCAPED_CHAR');\n    if (i) {\n      p += i;\n      continue;\n    }\n    p && (o.push(p), (p = ''));\n    var R = f('OPEN');\n    if (R) {\n      var g = d(),\n        y = f('NAME') || '',\n        O = f('PATTERN') || '',\n        b = d();\n      w('CLOSE'),\n        o.push({\n          name: y || (O ? m++ : ''),\n          pattern: y && !O ? A(g) : O,\n          prefix: g,\n          suffix: b,\n          modifier: f('MODIFIER') || '',\n        });\n      continue;\n    }\n    w('END');\n  }\n  return o;\n}\n\nfunction H(r, n) {\n  var e = [],\n    a = P(r, e, n);\n  return I(a, e, n);\n}\n\nfunction I(r, n, e) {\n  e === void 0 && (e = {});\n  var a = e.decode,\n    u =\n      a === void 0\n        ? function (t) {\n            return t;\n          }\n        : a;\n  return function (t) {\n    var c = r.exec(t);\n    if (!c) return !1;\n    for (\n      var o = c[0],\n        m = c.index,\n        h = Object.create(null),\n        p = function (w) {\n          if (c[w] === void 0) return 'continue';\n          var d = n[w - 1];\n          d.modifier === '*' || d.modifier === '+'\n            ? (h[d.name] = c[w].split(d.prefix + d.suffix).map(function (M) {\n                return u(M, d);\n              }))\n            : (h[d.name] = u(c[w], d));\n        },\n        f = 1;\n      f < c.length;\n      f++\n    )\n      p(f);\n    return {\n      path: o,\n      index: m,\n      params: h,\n    };\n  };\n}\n\nfunction s(r) {\n  return r.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, '\\\\$1');\n}\n\nfunction D(r) {\n  return r && r.sensitive ? '' : 'i';\n}\n\nfunction $(r, n) {\n  if (!n) return r;\n  for (var e = /\\((?:\\?<(.*?)>)?(?!\\?)/g, a = 0, u = e.exec(r.source); u; )\n    n.push({\n      name: u[1] || a++,\n      prefix: '',\n      suffix: '',\n      modifier: '',\n      pattern: '',\n    }),\n      (u = e.exec(r.source));\n  return r;\n}\n\nfunction W(r, n, e) {\n  var a = r.map(function (u) {\n    return P(u, n, e).source;\n  });\n  return new RegExp('(?:'.concat(a.join('|'), ')'), D(e));\n}\n\nfunction L(r, n, e) {\n  return U(F(r, e), n, e);\n}\n\nfunction U(r, n, e) {\n  e === void 0 && (e = {});\n  for (\n    var a = e.strict,\n      u = a === void 0 ? !1 : a,\n      t = e.start,\n      c = t === void 0 ? !0 : t,\n      o = e.end,\n      m = o === void 0 ? !0 : o,\n      h = e.encode,\n      p =\n        h === void 0\n          ? function (v) {\n              return v;\n            }\n          : h,\n      f = e.delimiter,\n      w = f === void 0 ? '/#?' : f,\n      d = e.endsWith,\n      M = d === void 0 ? '' : d,\n      A = '['.concat(s(M), ']|$'),\n      T = '['.concat(s(w), ']'),\n      x = c ? '^' : '',\n      C = 0,\n      g = r;\n    C < g.length;\n    C++\n  ) {\n    var i = g[C];\n    if (typeof i == 'string') x += s(p(i));\n    else {\n      var R = s(p(i.prefix)),\n        y = s(p(i.suffix));\n      if (i.pattern)\n        if ((n && n.push(i), R || y))\n          if (i.modifier === '+' || i.modifier === '*') {\n            var O = i.modifier === '*' ? '?' : '';\n            x += '(?:'\n              .concat(R, '((?:')\n              .concat(i.pattern, ')(?:')\n              .concat(y)\n              .concat(R, '(?:')\n              .concat(i.pattern, '))*)')\n              .concat(y, ')')\n              .concat(O);\n          } else x += '(?:'.concat(R, '(').concat(i.pattern, ')').concat(y, ')').concat(i.modifier);\n        else {\n          if (i.modifier === '+' || i.modifier === '*')\n            throw new TypeError('Can not repeat \"'.concat(i.name, '\" without a prefix and suffix'));\n          x += '('.concat(i.pattern, ')').concat(i.modifier);\n        }\n      else x += '(?:'.concat(R).concat(y, ')').concat(i.modifier);\n    }\n  }\n  if (m) u || (x += ''.concat(T, '?')), (x += e.endsWith ? '(?='.concat(A, ')') : '$');\n  else {\n    var b = r[r.length - 1],\n      l = typeof b == 'string' ? T.indexOf(b[b.length - 1]) > -1 : b === void 0;\n    u || (x += '(?:'.concat(T, '(?=').concat(A, '))?')), l || (x += '(?='.concat(T, '|').concat(A, ')'));\n  }\n  return new RegExp(x, D(e));\n}\n\nfunction P(r, n, e) {\n  return r instanceof RegExp ? $(r, n) : Array.isArray(r) ? W(r, n, e) : L(r, n, e);\n}\nexport { H as match, P as pathToRegexp };\n", "import type {\n  Match,\n  MatchFunction,\n  ParseOptions,\n  Path,\n  RegexpToFunctionOptions,\n  TokensToRegexpOptions,\n} from './compiled/path-to-regexp';\nimport { match as matchBase, pathToRegexp as pathToRegexpBase } from './compiled/path-to-regexp';\n\nexport const pathToRegexp = (path: string) => {\n  try {\n    // @ts-ignore no types exists for the pre-compiled package\n    return pathToRegexpBase(path);\n  } catch (e: any) {\n    throw new Error(\n      `Invalid path: ${path}.\\nConsult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\\n${e.message}`,\n    );\n  }\n};\n\nexport function match<P extends object = object>(\n  str: Path,\n  options?: ParseOptions & TokensToRegexpOptions & RegexpToFunctionOptions,\n): MatchFunction<P> {\n  try {\n    // @ts-ignore no types exists for the pre-compiled package\n    return matchBase(str, options);\n  } catch (e: any) {\n    throw new Error(\n      `Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\\n${e.message}`,\n    );\n  }\n}\n\nexport { type Match, type MatchFunction };\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,EAAE,CAAA,EAAG;IACZ,IAAA,IAAS,IAAI,CAAC,CAAA,EAAG,IAAI,GAAG,IAAI,EAAE,MAAA,EAAU;QACtC,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA;QACX,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;YACvC,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,MAAM;YACd,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,KAAK;YACb,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,KAAK;YACb,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,CAAA,CAAE,GAAG,CAAA;YACd,CAAC;YACD;QACF;QACA,IAAI,MAAM,KAAK;YACb,IAAA,IAAS,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,MAAA,EAAU;gBAC1C,IAAI,IAAI,EAAE,UAAA,CAAW,CAAC;gBACtB,IAAK,KAAK,MAAM,KAAK,MAAQ,KAAK,MAAM,KAAK,MAAQ,KAAK,MAAM,KAAK,OAAQ,MAAM,IAAI;oBACrF,KAAK,CAAA,CAAE,GAAG,CAAA;oBACV;gBACF;gBACA;YACF;YACA,IAAI,CAAC,EAAG,CAAA,MAAM,IAAI,UAAU,6BAA6B,MAAA,CAAO,CAAC,CAAC;YAClE,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT,CAAC,GACE,IAAI;YACP;QACF;QACA,IAAI,MAAM,KAAK;YACb,IAAI,IAAI,GACN,IAAI,IACJ,IAAI,IAAI;YACV,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,IAAK,CAAA,MAAM,IAAI,UAAU,oCAAoC,MAAA,CAAO,CAAC,CAAC;YACnF,MAAO,IAAI,EAAE,MAAA,EAAU;gBACrB,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,MAAM;oBACjB,KAAK,CAAA,CAAE,GAAG,CAAA,GAAI,CAAA,CAAE,GAAG,CAAA;oBACnB;gBACF;gBACA,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,KAAK;oBAChB,IAAK,KAAK,MAAM,GAAI;wBAClB;wBACA;oBACF;gBACF,OAAA,IAAW,CAAA,CAAE,CAAC,CAAA,KAAM,OAAA,CAAQ,KAAK,CAAA,CAAE,IAAI,CAAC,CAAA,KAAM,GAAA,GAC5C,MAAM,IAAI,UAAU,uCAAuC,MAAA,CAAO,CAAC,CAAC;gBACtE,KAAK,CAAA,CAAE,GAAG,CAAA;YACZ;YACA,IAAI,EAAG,CAAA,MAAM,IAAI,UAAU,yBAAyB,MAAA,CAAO,CAAC,CAAC;YAC7D,IAAI,CAAC,EAAG,CAAA,MAAM,IAAI,UAAU,sBAAsB,MAAA,CAAO,CAAC,CAAC;YAC3D,EAAE,IAAA,CAAK;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT,CAAC,GACE,IAAI;YACP;QACF;QACA,EAAE,IAAA,CAAK;YACL,MAAM;YACN,OAAO;YACP,OAAO,CAAA,CAAE,GAAG,CAAA;QACd,CAAC;IACH;IACA,OACE,EAAE,IAAA,CAAK;QACL,MAAM;QACN,OAAO;QACP,OAAO;IACT,CAAC,GACD;AAEJ;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG;IACf,MAAM,KAAA,KAAA,CAAW,IAAI,CAAC,CAAA;IACtB,IAAA,IACM,IAAI,EAAE,CAAC,GACT,IAAI,EAAE,QAAA,EACN,IAAI,MAAM,KAAA,IAAS,OAAO,GAC1B,IAAI,EAAE,SAAA,EACN,IAAI,MAAM,KAAA,IAAS,QAAQ,GAC3B,IAAI,CAAC,CAAA,EACL,IAAI,GACJ,IAAI,GACJ,IAAI,IACJ,IAAI,SAAU,CAAA,EAAG;QACf,IAAI,IAAI,EAAE,MAAA,IAAU,CAAA,CAAE,CAAC,CAAA,CAAE,IAAA,KAAS,EAAG,CAAA,OAAO,CAAA,CAAE,GAAG,CAAA,CAAE,KAAA;IACrD,GACA,IAAI,SAAU,CAAA,EAAG;QACf,IAAI,IAAI,EAAE,CAAC;QACX,IAAI,MAAM,KAAA,EAAQ,CAAA,OAAO;QACzB,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA,EACT,IAAI,EAAE,IAAA,EACN,IAAI,EAAE,KAAA;QACR,MAAM,IAAI,UAAU,cAAc,MAAA,CAAO,GAAG,MAAM,EAAE,MAAA,CAAO,GAAG,aAAa,EAAE,MAAA,CAAO,CAAC,CAAC;IACxF,GACA,IAAI,WAAY;QACd,IAAA,IAAS,IAAI,IAAI,GAAI,IAAI,EAAE,MAAM,KAAK,EAAE,cAAc,GAAM,CAAA,IAAK;QACjE,OAAO;IACT,GACA,IAAI,SAAU,CAAA,EAAG;QACf,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,MAAA,EAAQ,IAAK;YACxC,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA;YACX,IAAI,EAAE,OAAA,CAAQ,CAAC,IAAI,CAAA,EAAI,CAAA,OAAO;QAChC;QACA,OAAO;IACT,GACA,IAAI,SAAU,CAAA,EAAG;QACf,IAAI,IAAI,CAAA,CAAE,EAAE,MAAA,GAAS,CAAC,CAAA,EACpB,IAAI,KAAA,CAAM,KAAK,OAAO,KAAK,WAAW,IAAI,EAAA;QAC5C,IAAI,KAAK,CAAC,GACR,MAAM,IAAI,UAAU,8DAA8D,MAAA,CAAO,EAAE,IAAA,EAAM,GAAG,CAAC;QACvG,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,MAAA,CAAO,EAAE,CAAC,GAAG,KAAK,IAAI,SAAS,MAAA,CAAO,EAAE,CAAC,GAAG,KAAK,EAAE,MAAA,CAAO,EAAE,CAAC,GAAG,MAAM;IACjG,GACF,IAAI,EAAE,MAAA,EAEN;QACA,IAAI,IAAI,EAAE,MAAM,GACd,IAAI,EAAE,MAAM,GACZ,IAAI,EAAE,SAAS;QACjB,IAAI,KAAK,GAAG;YACV,IAAI,IAAI,KAAK;YACb,EAAE,OAAA,CAAQ,CAAC,MAAM,CAAA,KAAA,CAAQ,KAAK,GAAK,IAAI,EAAA,GACrC,KAAA,CAAM,EAAE,IAAA,CAAK,CAAC,GAAI,IAAI,EAAA,GACtB,EAAE,IAAA,CAAK;gBACL,MAAM,KAAK;gBACX,QAAQ;gBACR,QAAQ;gBACR,SAAS,KAAK,EAAE,CAAC;gBACjB,UAAU,EAAE,UAAU,KAAK;YAC7B,CAAC;YACH;QACF;QACA,IAAI,IAAI,KAAK,EAAE,cAAc;QAC7B,IAAI,GAAG;YACL,KAAK;YACL;QACF;QACA,KAAA,CAAM,EAAE,IAAA,CAAK,CAAC,GAAI,IAAI,EAAA;QACtB,IAAI,IAAI,EAAE,MAAM;QAChB,IAAI,GAAG;YACL,IAAI,IAAI,EAAE,GACR,IAAI,EAAE,MAAM,KAAK,IACjB,IAAI,EAAE,SAAS,KAAK,IACpB,IAAI,EAAE;YACR,EAAE,OAAO,GACP,EAAE,IAAA,CAAK;gBACL,MAAM,KAAA,CAAM,IAAI,MAAM,EAAA;gBACtB,SAAS,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI;gBAC1B,QAAQ;gBACR,QAAQ;gBACR,UAAU,EAAE,UAAU,KAAK;YAC7B,CAAC;YACH;QACF;QACA,EAAE,KAAK;IACT;IACA,OAAO;AACT;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG;IACf,IAAI,IAAI,CAAC,CAAA,EACP,IAAI,EAAE,GAAG,GAAG,CAAC;IACf,OAAO,EAAE,GAAG,GAAG,CAAC;AAClB;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,MAAM,KAAA,KAAA,CAAW,IAAI,CAAC,CAAA;IACtB,IAAI,IAAI,EAAE,MAAA,EACR,IACE,MAAM,KAAA,IACF,SAAU,CAAA,EAAG;QACX,OAAO;IACT,IACA;IACR,OAAO,SAAU,CAAA,EAAG;QAClB,IAAI,IAAI,EAAE,IAAA,CAAK,CAAC;QAChB,IAAI,CAAC,EAAG,CAAA,OAAO;QACf,IAAA,IACM,IAAI,CAAA,CAAE,CAAC,CAAA,EACT,IAAI,EAAE,KAAA,EACN,IAAI,aAAA,GAAA,OAAO,MAAA,CAAO,IAAI,GACtB,IAAI,SAAU,CAAA,EAAG;YACf,IAAI,CAAA,CAAE,CAAC,CAAA,KAAM,KAAA,EAAQ,CAAA,OAAO;YAC5B,IAAI,IAAI,CAAA,CAAE,IAAI,CAAC,CAAA;YACf,EAAE,QAAA,KAAa,OAAO,EAAE,QAAA,KAAa,MAChC,CAAA,CAAE,EAAE,IAAI,CAAA,GAAI,CAAA,CAAE,CAAC,CAAA,CAAE,KAAA,CAAM,EAAE,MAAA,GAAS,EAAE,MAAM,EAAE,GAAA,CAAI,SAAU,CAAA,EAAG;gBAC5D,OAAO,EAAE,GAAG,CAAC;YACf,CAAC,IACA,CAAA,CAAE,EAAE,IAAI,CAAA,GAAI,EAAE,CAAA,CAAE,CAAC,CAAA,EAAG,CAAC;QAC5B,GACA,IAAI,GACN,IAAI,EAAE,MAAA,EACN,IAEA,EAAE,CAAC;QACL,OAAO;YACL,MAAM;YACN,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAEA,SAAS,EAAE,CAAA,EAAG;IACZ,OAAO,EAAE,OAAA,CAAQ,6BAA6B,MAAM;AACtD;AAEA,SAAS,EAAE,CAAA,EAAG;IACZ,OAAO,KAAK,EAAE,SAAA,GAAY,KAAK;AACjC;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG;IACf,IAAI,CAAC,EAAG,CAAA,OAAO;IACf,IAAA,IAAS,IAAI,2BAA2B,IAAI,GAAG,IAAI,EAAE,IAAA,CAAK,EAAE,MAAM,GAAG,GACnE,EAAE,IAAA,CAAK;QACL,MAAM,CAAA,CAAE,CAAC,CAAA,IAAK;QACd,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,SAAS;IACX,CAAC,GACE,IAAI,EAAE,IAAA,CAAK,EAAE,MAAM;IACxB,OAAO;AACT;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,IAAI,IAAI,EAAE,GAAA,CAAI,SAAU,CAAA,EAAG;QACzB,OAAO,EAAE,GAAG,GAAG,CAAC,EAAE,MAAA;IACpB,CAAC;IACD,OAAO,IAAI,OAAO,MAAM,MAAA,CAAO,EAAE,IAAA,CAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AACxD;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,OAAO,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;AACxB;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,MAAM,KAAA,KAAA,CAAW,IAAI,CAAC,CAAA;IACtB,IAAA,IACM,IAAI,EAAE,MAAA,EACR,IAAI,MAAM,KAAA,IAAS,QAAK,GACxB,IAAI,EAAE,KAAA,EACN,IAAI,MAAM,KAAA,IAAS,OAAK,GACxB,IAAI,EAAE,GAAA,EACN,IAAI,MAAM,KAAA,IAAS,OAAK,GACxB,IAAI,EAAE,MAAA,EACN,IACE,MAAM,KAAA,IACF,SAAU,CAAA,EAAG;QACX,OAAO;IACT,IACA,GACN,IAAI,EAAE,SAAA,EACN,IAAI,MAAM,KAAA,IAAS,QAAQ,GAC3B,IAAI,EAAE,QAAA,EACN,IAAI,MAAM,KAAA,IAAS,KAAK,GACxB,IAAI,IAAI,MAAA,CAAO,EAAE,CAAC,GAAG,KAAK,GAC1B,IAAI,IAAI,MAAA,CAAO,EAAE,CAAC,GAAG,GAAG,GACxB,IAAI,IAAI,MAAM,IACd,IAAI,GACJ,IAAI,GACN,IAAI,EAAE,MAAA,EACN,IACA;QACA,IAAI,IAAI,CAAA,CAAE,CAAC,CAAA;QACX,IAAI,OAAO,KAAK,SAAU,CAAA,KAAK,EAAE,EAAE,CAAC,CAAC;aAChC;YACH,IAAI,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,GACnB,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC;YACnB,IAAI,EAAE,OAAA,EACJ,IAAK,KAAK,EAAE,IAAA,CAAK,CAAC,GAAG,KAAK,GACxB,IAAI,EAAE,QAAA,KAAa,OAAO,EAAE,QAAA,KAAa,KAAK;gBAC5C,IAAI,IAAI,EAAE,QAAA,KAAa,MAAM,MAAM;gBACnC,KAAK,MACF,MAAA,CAAO,GAAG,MAAM,EAChB,MAAA,CAAO,EAAE,OAAA,EAAS,MAAM,EACxB,MAAA,CAAO,CAAC,EACR,MAAA,CAAO,GAAG,KAAK,EACf,MAAA,CAAO,EAAE,OAAA,EAAS,MAAM,EACxB,MAAA,CAAO,GAAG,GAAG,EACb,MAAA,CAAO,CAAC;YACb,MAAO,CAAA,KAAK,MAAM,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,EAAE,OAAA,EAAS,GAAG,EAAE,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,EAAE,QAAQ;iBACrF;gBACH,IAAI,EAAE,QAAA,KAAa,OAAO,EAAE,QAAA,KAAa,KACvC,MAAM,IAAI,UAAU,mBAAmB,MAAA,CAAO,EAAE,IAAA,EAAM,+BAA+B,CAAC;gBACxF,KAAK,IAAI,MAAA,CAAO,EAAE,OAAA,EAAS,GAAG,EAAE,MAAA,CAAO,EAAE,QAAQ;YACnD;iBACG,KAAK,MAAM,MAAA,CAAO,CAAC,EAAE,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,EAAE,QAAQ;QAC5D;IACF;IACA,IAAI,EAAG,CAAA,KAAA,CAAM,KAAK,GAAG,MAAA,CAAO,GAAG,GAAG,CAAA,GAAK,KAAK,EAAE,QAAA,GAAW,MAAM,MAAA,CAAO,GAAG,GAAG,IAAI;SAC3E;QACH,IAAI,IAAI,CAAA,CAAE,EAAE,MAAA,GAAS,CAAC,CAAA,EACpB,IAAI,OAAO,KAAK,WAAW,EAAE,OAAA,CAAQ,CAAA,CAAE,EAAE,MAAA,GAAS,CAAC,CAAC,IAAI,CAAA,IAAK,MAAM,KAAA;QACrE,KAAA,CAAM,KAAK,MAAM,MAAA,CAAO,GAAG,KAAK,EAAE,MAAA,CAAO,GAAG,KAAK,CAAA,GAAI,KAAA,CAAM,KAAK,MAAM,MAAA,CAAO,GAAG,GAAG,EAAE,MAAA,CAAO,GAAG,GAAG,CAAA;IACpG;IACA,OAAO,IAAI,OAAO,GAAG,EAAE,CAAC,CAAC;AAC3B;AAEA,SAAS,EAAE,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IAClB,OAAO,aAAa,SAAS,EAAE,GAAG,CAAC,IAAI,MAAM,OAAA,CAAQ,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC;AAClF;;AC/TO,IAAM,eAAe,CAAC,SAAiB;IAC5C,IAAI;QAEF,OAAO,EAAiB,IAAI;IAC9B,EAAA,OAAS,GAAQ;QACf,MAAM,IAAI,MACR,CAAA,cAAA,EAAiB,IAAI,CAAA;;AAAA,EAA6G,EAAE,OAAO,EAAA;IAE/I;AACF;AAEO,SAAS,MACd,GAAA,EACA,OAAA,EACkB;IAClB,IAAI;QAEF,OAAO,EAAU,KAAK,OAAO;IAC/B,EAAA,OAAS,GAAQ;QACf,MAAM,IAAI,MACR,CAAA;AAAA,EAAoI,EAAE,OAAO,EAAA;IAEjJ;AACF", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 1592, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/buildAccountsBaseUrl.ts"], "sourcesContent": ["/**\n * Builds a full origin string pointing to the Account Portal for the given frontend API.\n */\nexport function buildAccountsBaseUrl(frontendApi?: string): string {\n  if (!frontendApi) {\n    return '';\n  }\n\n  // convert url from FAPI to accounts for Kima and legacy (prod & dev) instances\n  const accountsBaseUrl = frontendApi\n    // staging accounts\n    .replace(/clerk\\.accountsstage\\./, 'accountsstage.')\n    .replace(/clerk\\.accounts\\.|clerk\\./, 'accounts.');\n  return `https://${accountsBaseUrl}`;\n}\n"], "names": [], "mappings": ";;;;;;AAGO,SAAS,qBAAqB,WAAA,EAA8B;IACjE,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAGA,MAAM,kBAAkB,YAErB,OAAA,CAAQ,0BAA0B,gBAAgB,EAClD,OAAA,CAAQ,6BAA6B,WAAW;IACnD,OAAO,CAAA,QAAA,EAAW,eAAe,EAAA;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/authorization-errors.ts"], "sourcesContent": ["import type { ReverificationConfig } from '@clerk/types';\n\ntype ClerkError<T> = {\n  clerk_error: T;\n};\n\nconst REVERIFICATION_REASON = 'reverification-error';\n\ntype ReverificationError<M extends { metadata?: any } = { metadata: unknown }> = ClerkError<\n  {\n    type: 'forbidden';\n    reason: typeof REVERIFICATION_REASON;\n  } & M\n>;\n\nconst reverificationError = <MC extends ReverificationConfig>(\n  missingConfig?: MC,\n): ReverificationError<{\n  metadata?: {\n    reverification?: MC;\n  };\n}> => ({\n  clerk_error: {\n    type: 'forbidden',\n    reason: REVERIFICATION_REASON,\n    metadata: {\n      reverification: missingConfig,\n    },\n  },\n});\n\nconst reverificationErrorResponse = (...args: Parameters<typeof reverificationError>) =>\n  new Response(JSON.stringify(reverificationError(...args)), {\n    status: 403,\n  });\n\nconst isReverificationHint = (result: any): result is ReturnType<typeof reverificationError> => {\n  return (\n    result &&\n    typeof result === 'object' &&\n    'clerk_error' in result &&\n    result.clerk_error?.type === 'forbidden' &&\n    result.clerk_error?.reason === REVERIFICATION_REASON\n  );\n};\n\nexport { reverificationError, reverificationErrorResponse, isReverificationHint };\n"], "names": [], "mappings": ";;;;;;AAMA,IAAM,wBAAwB;AAS9B,IAAM,sBAAsB,CAC1B,gBAAA,CAKK;QACL,aAAa;YACX,MAAM;YACN,QAAQ;YACR,UAAU;gBACR,gBAAgB;YAClB;QACF;IACF,CAAA;AAEA,IAAM,8BAA8B,CAAA,GAAI,OACtC,IAAI,SAAS,KAAK,SAAA,CAAU,oBAAoB,GAAG,IAAI,CAAC,GAAG;QACzD,QAAQ;IACV,CAAC;AAEH,IAAM,uBAAuB,CAAC,WAAkE;IAC9F,OACE,UACA,OAAO,WAAW,YAClB,iBAAiB,UACjB,OAAO,WAAA,EAAa,SAAS,eAC7B,OAAO,WAAA,EAAa,WAAW;AAEnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1689, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/apiUrlFromPublishableKey.ts"], "sourcesContent": ["import {\n  LEGACY_DEV_INSTANCE_SUFFIXES,\n  LOCAL_API_URL,\n  LOCAL_ENV_SUFFIXES,\n  PROD_API_URL,\n  STAGING_API_URL,\n  STAGING_ENV_SUFFIXES,\n} from './constants';\nimport { parsePublishableKey } from './keys';\n\n/**\n * Get the correct API url based on the publishable key.\n *\n * @param publishableKey - The publishable key to parse.\n * @returns One of Clerk's API URLs.\n */\nexport const apiUrlFromPublishableKey = (publishableKey: string) => {\n  const frontendApi = parsePublishableKey(publishableKey)?.frontendApi;\n\n  if (frontendApi?.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return PROD_API_URL;\n  }\n\n  if (LOCAL_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return LOCAL_API_URL;\n  }\n  if (STAGING_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return STAGING_API_URL;\n  }\n  return PROD_API_URL;\n};\n"], "names": [], "mappings": ";;;;;;;;AAgBO,IAAM,2BAA2B,CAAC,mBAA2B;IAClE,MAAM,4RAAc,sBAAA,EAAoB,cAAc,GAAG;IAEzD,IAAI,aAAa,WAAW,QAAQ,+QAAK,+BAAA,CAA6B,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACnH,iRAAO,eAAA;IACT;IAEA,6QAAI,sBAAA,CAAmB,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACpE,iRAAO,gBAAA;IACT;IACA,IAAI,iSAAA,CAAqB,IAAA,CAAK,CAAA,SAAU,aAAa,SAAS,MAAM,CAAC,GAAG;QACtE,iRAAO,kBAAA;IACT;IACA,iRAAO,eAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1718, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/underscore.ts"], "sourcesContent": ["/**\n * Convert words to a sentence.\n *\n * @param items - An array of words to be joined.\n * @returns A string with the items joined by a comma and the last item joined by \", or\".\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once Safari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\n/**\n * Checks if a string is a valid IPv4 address.\n *\n * @returns True if the string is a valid IPv4 address, false otherwise.\n */\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\n/**\n * Converts the first character of a string to uppercase.\n *\n * @param str - The string to be converted.\n * @returns The modified string with the rest of the string unchanged.\n *\n * @example\n * ```ts\n * titleize('hello world') // 'Hello world'\n * ```\n */\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\n/**\n * Converts a string from snake_case to camelCase.\n */\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\n/**\n * Converts a string from camelCase to snake_case.\n */\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n\n/**\n * A function to determine if a value is truthy.\n *\n * @returns True for `true`, true, positive numbers. False for `false`, false, 0, negative integers and anything else.\n */\nexport function isTruthy(value: unknown): boolean {\n  // Return if Boolean\n  if (typeof value === `boolean`) {\n    return value;\n  }\n\n  // Return false if null or undefined\n  if (value === undefined || value === null) {\n    return false;\n  }\n\n  // If the String is true or false\n  if (typeof value === `string`) {\n    if (value.toLowerCase() === `true`) {\n      return true;\n    }\n\n    if (value.toLowerCase() === `false`) {\n      return false;\n    }\n  }\n\n  // Now check if it's a number\n  const number = parseInt(value as string, 10);\n  if (isNaN(number)) {\n    return false;\n  }\n\n  if (number > 0) {\n    return true;\n  }\n\n  // Default to false\n  return false;\n}\n\n/**\n * Get all non-undefined values from an object.\n */\nexport function getNonUndefinedValues<T extends object>(obj: T): Partial<T> {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAMO,IAAM,aAAa,CAAC,UAA4B;IAErD,IAAI,MAAM,MAAA,IAAU,GAAG;QACrB,OAAO;IACT;IACA,IAAI,MAAM,MAAA,IAAU,GAAG;QACrB,OAAO,KAAA,CAAM,CAAC,CAAA;IAChB;IACA,IAAI,WAAW,MAAM,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,IAAI;IAC3C,YAAY,CAAA,KAAA,EAAQ,MAAM,KAAA,CAAM,CAAA,CAAE,CAAC,EAAA;IACnC,OAAO;AACT;AAEA,IAAM,sBACJ;AAOK,SAAS,cAAc,GAAA,EAAyC;IACrE,OAAO,oBAAoB,IAAA,CAAK,OAAO,EAAE;AAC3C;AAaO,SAAS,SAAS,GAAA,EAAwC;IAC/D,MAAM,IAAI,OAAO;IACjB,OAAO,EAAE,MAAA,CAAO,CAAC,EAAE,WAAA,CAAY,IAAI,EAAE,KAAA,CAAM,CAAC;AAC9C;AAKO,SAAS,aAAa,GAAA,EAAiC;IAC5D,OAAO,MAAM,IAAI,OAAA,CAAQ,gBAAgB,CAAA,QAAS,MAAM,WAAA,CAAY,EAAE,OAAA,CAAQ,OAAO,EAAE,CAAC,IAAI;AAC9F;AAKO,SAAS,aAAa,GAAA,EAAiC;IAC5D,OAAO,MAAM,IAAI,OAAA,CAAQ,UAAU,CAAA,SAAU,CAAA,CAAA,EAAI,OAAO,WAAA,CAAY,CAAC,EAAE,IAAI;AAC7E;AAEA,IAAM,8BAA8B,CAAC,cAAmB;IACtD,MAAM,gBAAgB,CAAC,QAAkB;QACvC,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QAEA,IAAI,MAAM,OAAA,CAAQ,GAAG,GAAG;YACtB,OAAO,IAAI,GAAA,CAAI,CAAA,OAAM;gBACnB,IAAI,OAAO,OAAO,YAAY,MAAM,OAAA,CAAQ,EAAE,GAAG;oBAC/C,OAAO,cAAc,EAAE;gBACzB;gBACA,OAAO;YACT,CAAC;QACH;QAEA,MAAM,OAAO;YAAE,GAAG,GAAA;QAAI;QACtB,MAAM,OAAO,OAAO,IAAA,CAAK,IAAI;QAC7B,KAAA,MAAW,WAAW,KAAM;YAC1B,MAAM,UAAU,UAAU,QAAQ,QAAA,CAAS,CAAC;YAC5C,IAAI,YAAY,SAAS;gBACvB,IAAA,CAAK,OAAO,CAAA,GAAI,IAAA,CAAK,OAAO,CAAA;gBAC5B,OAAO,IAAA,CAAK,OAAO,CAAA;YACrB;YACA,IAAI,OAAO,IAAA,CAAK,OAAO,CAAA,KAAM,UAAU;gBACrC,IAAA,CAAK,OAAO,CAAA,GAAI,cAAc,IAAA,CAAK,OAAO,CAAC;YAC7C;QACF;QACA,OAAO;IACT;IAEA,OAAO;AACT;AASO,IAAM,mBAAmB,4BAA4B,YAAY;AASjE,IAAM,mBAAmB,4BAA4B,YAAY;AAOjE,SAAS,SAAS,KAAA,EAAyB;IAEhD,IAAI,OAAO,UAAU,CAAA,OAAA,CAAA,EAAW;QAC9B,OAAO;IACT;IAGA,IAAI,UAAU,KAAA,KAAa,UAAU,MAAM;QACzC,OAAO;IACT;IAGA,IAAI,OAAO,UAAU,CAAA,MAAA,CAAA,EAAU;QAC7B,IAAI,MAAM,WAAA,CAAY,MAAM,CAAA,IAAA,CAAA,EAAQ;YAClC,OAAO;QACT;QAEA,IAAI,MAAM,WAAA,CAAY,MAAM,CAAA,KAAA,CAAA,EAAS;YACnC,OAAO;QACT;IACF;IAGA,MAAM,SAAS,SAAS,OAAiB,EAAE;IAC3C,IAAI,MAAM,MAAM,GAAG;QACjB,OAAO;IACT;IAEA,IAAI,SAAS,GAAG;QACd,OAAO;IACT;IAGA,OAAO;AACT;AAKO,SAAS,sBAAwC,GAAA,EAAoB;IAC1E,OAAO,OAAO,OAAA,CAAQ,GAAG,EAAE,MAAA,CAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAA,KAAM;QACvD,IAAI,UAAU,KAAA,GAAW;YACvB,GAAA,CAAI,GAAc,CAAA,GAAI;QACxB;QACA,OAAO;IACT,GAAG,CAAC,CAAe;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1863, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1886, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/logger.ts"], "sourcesContent": ["const loggedMessages: Set<string> = new Set();\n\nexport const logger = {\n  /**\n   * A custom logger that ensures messages are logged only once.\n   * Reduces noise and duplicated messages when logs are in a hot codepath.\n   */\n  warnOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    loggedMessages.add(msg);\n    console.warn(msg);\n  },\n  logOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    console.log(msg);\n    loggedMessages.add(msg);\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA,IAAM,iBAA8B,aAAA,GAAA,IAAI,IAAI;AAErC,IAAM,SAAS;IAAA;;;GAAA,GAKpB,UAAU,CAAC,QAAgB;QACzB,IAAI,eAAe,GAAA,CAAI,GAAG,GAAG;YAC3B;QACF;QAEA,eAAe,GAAA,CAAI,GAAG;QACtB,QAAQ,IAAA,CAAK,GAAG;IAClB;IACA,SAAS,CAAC,QAAgB;QACxB,IAAI,eAAe,GAAA,CAAI,GAAG,GAAG;YAC3B;QACF;QAEA,QAAQ,GAAA,CAAI,GAAG;QACf,eAAe,GAAA,CAAI,GAAG;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1918, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/proxy.ts"], "sourcesContent": ["export function isValidProxyUrl(key: string | undefined) {\n  if (!key) {\n    return true;\n  }\n\n  return isHttpOrHttps(key) || isProxyUrlRelative(key);\n}\n\nexport function isHttpOrHttps(key: string | undefined) {\n  return /^http(s)?:\\/\\//.test(key || '');\n}\n\nexport function isProxyUrlRelative(key: string) {\n  return key.startsWith('/');\n}\n\nexport function proxyUrlToAbsoluteURL(url: string | undefined): string {\n  if (!url) {\n    return '';\n  }\n  return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;\n}\n"], "names": [], "mappings": ";;;;;;;AAAO,SAAS,gBAAgB,GAAA,EAAyB;IACvD,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IAEA,OAAO,cAAc,GAAG,KAAK,mBAAmB,GAAG;AACrD;AAEO,SAAS,cAAc,GAAA,EAAyB;IACrD,OAAO,iBAAiB,IAAA,CAAK,OAAO,EAAE;AACxC;AAEO,SAAS,mBAAmB,GAAA,EAAa;IAC9C,OAAO,IAAI,UAAA,CAAW,GAAG;AAC3B;AAEO,SAAS,sBAAsB,GAAA,EAAiC;IACrE,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,OAAO,mBAAmB,GAAG,IAAI,IAAI,IAAI,KAAK,OAAO,QAAA,CAAS,MAAM,EAAE,QAAA,CAAS,IAAI;AACrF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1974, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1997, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/allSettled.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/logErrorInDevMode.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/fastDeepMerge.ts"], "sourcesContent": ["/**\n * A ES6 compatible utility that implements `Promise.allSettled`\n * @internal\n */\nexport function allSettled<T>(\n  iterable: Iterable<Promise<T>>,\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  const promises = Array.from(iterable).map(p =>\n    p.then(\n      value => ({ status: 'fulfilled', value }) as const,\n      reason => ({ status: 'rejected', reason }) as const,\n    ),\n  );\n  return Promise.all(promises);\n}\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(`Clerk: ${message}`);\n  }\n};\n", "/**\n * Merges 2 objects without creating new object references\n * The merged props will appear on the `target` object\n * If `target` already has a value for a given key it will not be overwritten\n */\nexport const fastDeepMergeAndReplace = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndReplace(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n};\n\nexport const fastDeepMergeAndKeep = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndKeep(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === undefined) {\n      target[key] = source[key];\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;AAIO,SAAS,WACd,QAAA,EACsF;IACtF,MAAM,WAAW,MAAM,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,CAAA,IACxC,EAAE,IAAA,CACA,CAAA,QAAA,CAAU;gBAAE,QAAQ;gBAAa;YAAM,CAAA,GACvC,CAAA,SAAA,CAAW;gBAAE,QAAQ;gBAAY;YAAO,CAAA;IAG5C,OAAO,QAAQ,GAAA,CAAI,QAAQ;AAC7B;;ACZO,IAAM,oBAAoB,CAAC,YAAoB;IACpD,kRAAI,2BAAA,CAAyB,IAAG;QAC9B,QAAQ,KAAA,CAAM,CAAA,OAAA,EAAU,OAAO,EAAE;IACnC;AACF;;ACDO,IAAM,0BAA0B,CACrC,QACA,WACG;IACH,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB;IACF;IAEA,IAAA,MAAW,OAAO,OAAQ;QACxB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,QAAQ,OAAO,MAAA,CAAO,GAAG,CAAA,KAAM,CAAA,MAAA,CAAA,EAAU;YAChH,IAAI,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;gBAC7B,MAAA,CAAO,GAAG,CAAA,GAAI,IAAA,CAAK,OAAO,cAAA,CAAe,MAAA,CAAO,GAAG,CAAC,CAAA,EAAE,WAAA,CAAa;YACrE;YACA,wBAAwB,MAAA,CAAO,GAAG,CAAA,EAAG,MAAA,CAAO,GAAG,CAAC;QAClD,OAAA,IAAW,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,GAAG;YAC5D,MAAA,CAAO,GAAG,CAAA,GAAI,MAAA,CAAO,GAAG,CAAA;QAC1B;IACF;AACF;AAEO,IAAM,uBAAuB,CAClC,QACA,WACG;IACH,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB;IACF;IAEA,IAAA,MAAW,OAAO,OAAQ;QACxB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,QAAQ,OAAO,MAAA,CAAO,GAAG,CAAA,KAAM,CAAA,MAAA,CAAA,EAAU;YAChH,IAAI,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;gBAC7B,MAAA,CAAO,GAAG,CAAA,GAAI,IAAA,CAAK,OAAO,cAAA,CAAe,MAAA,CAAO,GAAG,CAAC,CAAA,EAAE,WAAA,CAAa;YACrE;YACA,qBAAqB,MAAA,CAAO,GAAG,CAAA,EAAG,MAAA,CAAO,GAAG,CAAC;QAC/C,OAAA,IAAW,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,KAAK,MAAA,CAAO,GAAG,CAAA,KAAM,KAAA,GAAW;YACzF,MAAA,CAAO,GAAG,CAAA,GAAI,MAAA,CAAO,GAAG,CAAA;QAC1B;IACF;AACF", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/noop.ts"], "sourcesContent": ["export const noop = (..._args: any[]): void => {\n  // do nothing.\n};\n"], "names": [], "mappings": ";;;;AAAO,IAAM,OAAO,CAAA,GAAI,SAExB,CAF+C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2074, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/createDeferredPromise.ts"], "sourcesContent": ["import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n * A ES6 compatible utility that implements `Promise.withResolvers`\n * @internal\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n"], "names": [], "mappings": ";;;;;;AAUO,IAAM,wBAAwB,MAAM;IACzC,IAAI,oRAAoB,OAAA;IACxB,IAAI,mRAAmB,OAAA;IACvB,MAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;QACxC,UAAU;QACV,SAAS;IACX,CAAC;IACD,OAAO;QAAE;QAAS;QAAS;IAAO;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/utils/handleValueOrFn.ts"], "sourcesContent": ["type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n"], "names": [], "mappings": ";;;;AAGO,SAAS,gBAAmB,KAAA,EAAyB,GAAA,EAAU,YAAA,EAAiC;IACrG,IAAI,OAAO,UAAU,YAAY;QAC/B,OAAQ,MAAwB,GAAG;IACrC;IAEA,IAAI,OAAO,UAAU,aAAa;QAChC,OAAO;IACT;IAEA,IAAI,OAAO,iBAAiB,aAAa;QACvC,OAAO;IACT;IAEA,OAAO,KAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/throttler.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/collector.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/events/component-mounted.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/events/method-called.ts", "file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bshared%403.9.3_react-d_2c6ad16e10e795fc582276053b304840/node_modules/%40clerk/shared/src/telemetry/events/framework-metadata.ts"], "sourcesContent": ["import type { TelemetryEvent } from '@clerk/types';\n\ntype TtlInMilliseconds = number;\n\nconst DEFAULT_CACHE_TTL_MS = 86400000; // 24 hours\n\n/**\n * Manages throttling for telemetry events using the browser's localStorage to\n * mitigate event flooding in frequently executed code paths.\n */\nexport class TelemetryEventThrottler {\n  #storageKey = 'clerk_telemetry_throttler';\n  #cacheTtl = DEFAULT_CACHE_TTL_MS;\n\n  isEventThrottled(payload: TelemetryEvent): boolean {\n    if (!this.#isValidBrowser) {\n      return false;\n    }\n\n    const now = Date.now();\n    const key = this.#generateKey(payload);\n    const entry = this.#cache?.[key];\n\n    if (!entry) {\n      const updatedCache = {\n        ...this.#cache,\n        [key]: now,\n      };\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    const shouldInvalidate = entry && now - entry > this.#cacheTtl;\n    if (shouldInvalidate) {\n      const updatedCache = this.#cache;\n      delete updatedCache[key];\n\n      localStorage.setItem(this.#storageKey, JSON.stringify(updatedCache));\n    }\n\n    return !!entry;\n  }\n\n  /**\n   * Generates a consistent unique key for telemetry events by sorting payload properties.\n   * This ensures that payloads with identical content in different orders produce the same key.\n   */\n  #generateKey(event: TelemetryEvent): string {\n    const { sk: _sk, pk: _pk, payload, ...rest } = event;\n\n    const sanitizedEvent: Omit<TelemetryEvent, 'sk' | 'pk' | 'payload'> & TelemetryEvent['payload'] = {\n      ...payload,\n      ...rest,\n    };\n\n    return JSON.stringify(\n      Object.keys({\n        ...payload,\n        ...rest,\n      })\n        .sort()\n        .map(key => sanitizedEvent[key]),\n    );\n  }\n\n  get #cache(): Record<string, TtlInMilliseconds> | undefined {\n    const cacheString = localStorage.getItem(this.#storageKey);\n\n    if (!cacheString) {\n      return {};\n    }\n\n    return JSON.parse(cacheString);\n  }\n\n  /**\n   * Checks if the browser's localStorage is supported and writable.\n   *\n   * If any of these operations fail, it indicates that localStorage is either\n   * not supported or not writable (e.g., in cases where the storage is full or\n   * the browser is in a privacy mode that restricts localStorage usage).\n   */\n  get #isValidBrowser(): boolean {\n    if (typeof window === 'undefined') {\n      return false;\n    }\n\n    const storage = window.localStorage;\n    if (!storage) {\n      return false;\n    }\n\n    try {\n      const testKey = 'test';\n      storage.setItem(testKey, testKey);\n      storage.removeItem(testKey);\n\n      return true;\n    } catch (err: unknown) {\n      const isQuotaExceededError =\n        err instanceof DOMException &&\n        // Check error names for different browsers\n        (err.name === 'QuotaExceededError' || err.name === 'NS_ERROR_DOM_QUOTA_REACHED');\n\n      if (isQuotaExceededError && storage.length > 0) {\n        storage.removeItem(this.#storageKey);\n      }\n\n      return false;\n    }\n  }\n}\n", "/**\n * The `TelemetryCollector` class handles collection of telemetry events from Clerk <PERSON>. Telemetry is opt-out and can be disabled by setting a CLERK_TELEMETRY_DISABLED environment variable.\n * The `Clerk<PERSON>rovider` also accepts a `telemetry` prop that will be passed to the collector during initialization:\n *\n * ```jsx\n * <ClerkProvider telemetry={false}>\n *    ...\n * </ClerkProvider>\n * ```\n *\n * For more information, please see the telemetry documentation page: https://clerk.com/docs/telemetry\n */\nimport type {\n  InstanceType,\n  TelemetryCollector as TelemetryCollectorInterface,\n  TelemetryEvent,\n  TelemetryEventRaw,\n} from '@clerk/types';\n\nimport { parsePublishableKey } from '../keys';\nimport { isTruthy } from '../underscore';\nimport { TelemetryEventThrottler } from './throttler';\nimport type { TelemetryCollectorOptions } from './types';\n\ntype TelemetryCollectorConfig = Pick<\n  TelemetryCollectorOptions,\n  'samplingRate' | 'disabled' | 'debug' | 'maxBufferSize'\n> & {\n  endpoint: string;\n};\n\ntype TelemetryMetadata = Required<\n  Pick<TelemetryCollectorOptions, 'clerkVersion' | 'sdk' | 'sdkVersion' | 'publishableKey' | 'secretKey'>\n> & {\n  /**\n   * The instance type, derived from the provided publishableKey.\n   */\n  instanceType: InstanceType;\n};\n\nconst DEFAULT_CONFIG: Partial<TelemetryCollectorConfig> = {\n  samplingRate: 1,\n  maxBufferSize: 5,\n  // Production endpoint: https://clerk-telemetry.com\n  // Staging endpoint: https://staging.clerk-telemetry.com\n  // Local: http://localhost:8787\n  endpoint: 'https://clerk-telemetry.com',\n};\n\nexport class TelemetryCollector implements TelemetryCollectorInterface {\n  #config: Required<TelemetryCollectorConfig>;\n  #eventThrottler: TelemetryEventThrottler;\n  #metadata: TelemetryMetadata = {} as TelemetryMetadata;\n  #buffer: TelemetryEvent[] = [];\n  #pendingFlush: any;\n\n  constructor(options: TelemetryCollectorOptions) {\n    this.#config = {\n      maxBufferSize: options.maxBufferSize ?? DEFAULT_CONFIG.maxBufferSize,\n      samplingRate: options.samplingRate ?? DEFAULT_CONFIG.samplingRate,\n      disabled: options.disabled ?? false,\n      debug: options.debug ?? false,\n      endpoint: DEFAULT_CONFIG.endpoint,\n    } as Required<TelemetryCollectorConfig>;\n\n    if (!options.clerkVersion && typeof window === 'undefined') {\n      // N/A in a server environment\n      this.#metadata.clerkVersion = '';\n    } else {\n      this.#metadata.clerkVersion = options.clerkVersion ?? '';\n    }\n\n    // We will try to grab the SDK data lazily when an event is triggered, so it should always be defined once the event is sent.\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdk = options.sdk!;\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.#metadata.sdkVersion = options.sdkVersion!;\n\n    this.#metadata.publishableKey = options.publishableKey ?? '';\n\n    const parsedKey = parsePublishableKey(options.publishableKey);\n    if (parsedKey) {\n      this.#metadata.instanceType = parsedKey.instanceType;\n    }\n\n    if (options.secretKey) {\n      // Only send the first 16 characters of the secret key to to avoid sending the full key. We can still query against the partial key.\n      this.#metadata.secretKey = options.secretKey.substring(0, 16);\n    }\n\n    this.#eventThrottler = new TelemetryEventThrottler();\n  }\n\n  get isEnabled(): boolean {\n    if (this.#metadata.instanceType !== 'development') {\n      return false;\n    }\n\n    // In browser or client environments, we most likely pass the disabled option to the collector, but in environments\n    // where environment variables are available we also check for `CLERK_TELEMETRY_DISABLED`.\n    if (this.#config.disabled || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DISABLED))) {\n      return false;\n    }\n\n    // navigator.webdriver is a property generally set by headless browsers that are running in an automated testing environment.\n    // Data from these environments is not meaningful for us and has the potential to produce a large volume of events, so we disable\n    // collection in this case. (ref: https://developer.mozilla.org/en-US/docs/Web/API/Navigator/webdriver)\n    if (typeof window !== 'undefined' && !!window?.navigator?.webdriver) {\n      return false;\n    }\n\n    return true;\n  }\n\n  get isDebug(): boolean {\n    return this.#config.debug || (typeof process !== 'undefined' && isTruthy(process.env.CLERK_TELEMETRY_DEBUG));\n  }\n\n  record(event: TelemetryEventRaw): void {\n    const preparedPayload = this.#preparePayload(event.event, event.payload);\n\n    this.#logEvent(preparedPayload.event, preparedPayload);\n\n    if (!this.#shouldRecord(preparedPayload, event.eventSamplingRate)) {\n      return;\n    }\n\n    this.#buffer.push(preparedPayload);\n\n    this.#scheduleFlush();\n  }\n\n  #shouldRecord(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    return this.isEnabled && !this.isDebug && this.#shouldBeSampled(preparedPayload, eventSamplingRate);\n  }\n\n  #shouldBeSampled(preparedPayload: TelemetryEvent, eventSamplingRate?: number) {\n    const randomSeed = Math.random();\n\n    const toBeSampled =\n      randomSeed <= this.#config.samplingRate &&\n      (typeof eventSamplingRate === 'undefined' || randomSeed <= eventSamplingRate);\n\n    if (!toBeSampled) {\n      return false;\n    }\n\n    return !this.#eventThrottler.isEventThrottled(preparedPayload);\n  }\n\n  #scheduleFlush(): void {\n    // On the server, we want to flush immediately as we have less guarantees about the lifecycle of the process\n    if (typeof window === 'undefined') {\n      this.#flush();\n      return;\n    }\n\n    const isBufferFull = this.#buffer.length >= this.#config.maxBufferSize;\n    if (isBufferFull) {\n      // If the buffer is full, flush immediately to make sure we minimize the chance of event loss.\n      // Cancel any pending flushes as we're going to flush immediately\n      if (this.#pendingFlush) {\n        const cancel = typeof cancelIdleCallback !== 'undefined' ? cancelIdleCallback : clearTimeout;\n        cancel(this.#pendingFlush);\n      }\n      this.#flush();\n      return;\n    }\n\n    // If we have a pending flush, do nothing\n    if (this.#pendingFlush) {\n      return;\n    }\n\n    if ('requestIdleCallback' in window) {\n      this.#pendingFlush = requestIdleCallback(() => {\n        this.#flush();\n      });\n    } else {\n      // This is not an ideal solution, but it at least waits until the next tick\n      this.#pendingFlush = setTimeout(() => {\n        this.#flush();\n      }, 0);\n    }\n  }\n\n  #flush(): void {\n    fetch(new URL('/v1/event', this.#config.endpoint), {\n      method: 'POST',\n      // TODO: We send an array here with that idea that we can eventually send multiple events.\n      body: JSON.stringify({\n        events: this.#buffer,\n      }),\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n      .catch(() => void 0)\n      .then(() => {\n        this.#buffer = [];\n      })\n      .catch(() => void 0);\n  }\n\n  /**\n   * If running in debug mode, log the event and its payload to the console.\n   */\n  #logEvent(event: TelemetryEvent['event'], payload: Record<string, any>) {\n    if (!this.isDebug) {\n      return;\n    }\n\n    if (typeof console.groupCollapsed !== 'undefined') {\n      console.groupCollapsed('[clerk/telemetry]', event);\n      console.log(payload);\n      console.groupEnd();\n    } else {\n      console.log('[clerk/telemetry]', event, payload);\n    }\n  }\n\n  /**\n   * If in browser, attempt to lazily grab the SDK metadata from the Clerk singleton, otherwise fallback to the initially passed in values.\n   *\n   * This is necessary because the sdkMetadata can be set by the host SDK after the TelemetryCollector is instantiated.\n   */\n  #getSDKMetadata() {\n    let sdkMetadata = {\n      name: this.#metadata.sdk,\n      version: this.#metadata.sdkVersion,\n    };\n\n    // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n    if (typeof window !== 'undefined' && window.Clerk) {\n      // @ts-expect-error -- The global window.Clerk type is declared in clerk-js, but we can't rely on that here\n      sdkMetadata = { ...sdkMetadata, ...window.Clerk.constructor.sdkMetadata };\n    }\n\n    return sdkMetadata;\n  }\n\n  /**\n   * Append relevant metadata from the Clerk singleton to the event payload.\n   */\n  #preparePayload(event: TelemetryEvent['event'], payload: TelemetryEvent['payload']): TelemetryEvent {\n    const sdkMetadata = this.#getSDKMetadata();\n\n    return {\n      event,\n      cv: this.#metadata.clerkVersion ?? '',\n      it: this.#metadata.instanceType ?? '',\n      sdk: sdkMetadata.name,\n      sdkv: sdkMetadata.version,\n      ...(this.#metadata.publishableKey ? { pk: this.#metadata.publishableKey } : {}),\n      ...(this.#metadata.secretKey ? { sk: this.#metadata.secretKey } : {}),\n      payload,\n    };\n  }\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_COMPONENT_MOUNTED = 'COMPONENT_MOUNTED';\nconst EVENT_COMPONENT_OPENED = 'COMPONENT_OPENED';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype ComponentMountedBase = {\n  component: string;\n};\n\ntype EventPrebuiltComponent = ComponentMountedBase & {\n  appearanceProp: boolean;\n  elements: boolean;\n  variables: boolean;\n  baseTheme: boolean;\n};\n\ntype EventComponentMounted = ComponentMountedBase & TelemetryEventRaw['payload'];\n\nfunction createPrebuiltComponentEvent(event: typeof EVENT_COMPONENT_MOUNTED | typeof EVENT_COMPONENT_OPENED) {\n  return function (\n    component: string,\n    props?: Record<string, any>,\n    additionalPayload?: TelemetryEventRaw['payload'],\n  ): TelemetryEventRaw<EventPrebuiltComponent> {\n    return {\n      event,\n      eventSamplingRate: EVENT_SAMPLING_RATE,\n      payload: {\n        component,\n        appearanceProp: Bo<PERSON>an(props?.appearance),\n        baseTheme: <PERSON><PERSON><PERSON>(props?.appearance?.baseTheme),\n        elements: <PERSON><PERSON><PERSON>(props?.appearance?.elements),\n        variables: Boolean(props?.appearance?.variables),\n        ...additionalPayload,\n      },\n    };\n  };\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is mounted.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentMounted('SignUp', props));\n */\nexport function eventPrebuiltComponentMounted(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_MOUNTED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a prebuilt (AIO) component is opened as a modal.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Will be filtered to a known list of props.\n * @param additionalPayload - Additional data to send with the event.\n *\n * @example\n * telemetry.record(eventPrebuiltComponentOpened('GoogleOneTap', props));\n */\nexport function eventPrebuiltComponentOpened(\n  component: string,\n  props?: Record<string, any>,\n  additionalPayload?: TelemetryEventRaw['payload'],\n): TelemetryEventRaw<EventPrebuiltComponent> {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_OPENED)(component, props, additionalPayload);\n}\n\n/**\n * Helper function for `telemetry.record()`. Create a consistent event object for when a component is mounted. Use `eventPrebuiltComponentMounted` for prebuilt components.\n *\n * **Caution:** Filter the `props` you pass to this function to avoid sending too much data.\n *\n * @param component - The name of the component.\n * @param props - The props passed to the component. Ideally you only pass a handful of props here.\n *\n * @example\n * telemetry.record(eventComponentMounted('SignUp', props));\n */\nexport function eventComponentMounted(\n  component: string,\n  props: TelemetryEventRaw['payload'] = {},\n): TelemetryEventRaw<EventComponentMounted> {\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      ...props,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_METHOD_CALLED = 'METHOD_CALLED';\n\ntype EventMethodCalled = {\n  method: string;\n} & Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventMethodCalled(\n  method: string,\n  payload?: Record<string, unknown>,\n): TelemetryEventRaw<EventMethodCalled> {\n  return {\n    event: EVENT_METHOD_CALLED,\n    payload: {\n      method,\n      ...payload,\n    },\n  };\n}\n", "import type { TelemetryEventRaw } from '@clerk/types';\n\nconst EVENT_FRAMEWORK_METADATA = 'FRAMEWORK_METADATA';\nconst EVENT_SAMPLING_RATE = 0.1;\n\ntype EventFrameworkMetadata = Record<string, string | number | boolean>;\n\n/**\n * Fired when a helper method is called from a Clerk SDK.\n */\nexport function eventFrameworkMetadata(payload: EventFrameworkMetadata): TelemetryEventRaw<EventFrameworkMetadata> {\n  return {\n    event: EVENT_FRAMEWORK_METADATA,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload,\n  };\n}\n"], "names": ["EVENT_SAMPLING_RATE"], "mappings": ";;;;;;;;;;;;;;;AAIA,IAAM,uBAAuB;AAJ7B,IAAA,aAAA,WAAA,oCAAA,gBAAA,WAAA;AAUO,IAAM,0BAAN,MAA8B;IAA9B,aAAA;QAAA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACL,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA,aAAc;QACd,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA,WAAY;IAAA;IAEZ,iBAAiB,OAAA,EAAkC;QACjD,IAAI,CAAC,6RAAA,EAAA,IAAA,EAAK,oCAAA,qBAAiB;YACzB,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAA,CAAI;QACrB,MAAM,MAAM,gSAAA,EAAA,IAAA,EAAK,oCAAA,gBAAL,IAAA,CAAA,IAAA,EAAkB;QAC9B,MAAM,sRAAQ,eAAA,EAAA,IAAA,EAAK,oCAAA,YAAA,CAAS,GAAG,CAAA;QAE/B,IAAI,CAAC,OAAO;YACV,MAAM,eAAe;gBACnB,iRAAG,eAAA,EAAA,IAAA,EAAK,oCAAA,UAAA;gBACR,CAAC,GAAG,CAAA,EAAG;YACT;YAEA,aAAa,OAAA,+QAAQ,eAAA,EAAA,IAAA,EAAK,cAAa,KAAK,SAAA,CAAU,YAAY,CAAC;QACrE;QAEA,MAAM,mBAAmB,SAAS,MAAM,sRAAQ,eAAA,EAAA,IAAA,EAAK;QACrD,IAAI,kBAAkB;YACpB,MAAM,6RAAe,eAAA,EAAA,IAAA,EAAK,oCAAA;YAC1B,OAAO,YAAA,CAAa,GAAG,CAAA;YAEvB,aAAa,OAAA,CAAQ,6RAAA,EAAA,IAAA,EAAK,cAAa,KAAK,SAAA,CAAU,YAAY,CAAC;QACrE;QAEA,OAAO,CAAC,CAAC;IACX;AAsEF;AApGE,cAAA,IAAA;AACA,YAAA,IAAA;AAFK,qCAAA,IAAA;AAAA;;;CAAA,GAqCL,iBAAY,SAAC,KAAA,EAA+B;IAC1C,MAAM,EAAE,IAAI,GAAA,EAAK,IAAI,GAAA,EAAK,OAAA,EAAS,GAAG,KAAK,CAAA,GAAI;IAE/C,MAAM,iBAA4F;QAChG,GAAG,OAAA;QACH,GAAG,IAAA;IACL;IAEA,OAAO,KAAK,SAAA,CACV,OAAO,IAAA,CAAK;QACV,GAAG,OAAA;QACH,GAAG,IAAA;IACL,CAAC,EACE,IAAA,CAAK,EACL,GAAA,CAAI,CAAA,MAAO,cAAA,CAAe,GAAG,CAAC;AAErC;AAEI,YAAM,WAAkD;IAC1D,MAAM,cAAc,aAAa,OAAA,+QAAQ,eAAA,EAAA,IAAA,EAAK,YAAW;IAEzD,IAAI,CAAC,aAAa;QAChB,OAAO,CAAC;IACV;IAEA,OAAO,KAAK,KAAA,CAAM,WAAW;AAC/B;AASI,qBAAe,WAAY;IAC7B,IAAI,OAAO,WAAW,kBAAa;QACjC,OAAO;IACT;;IAEA,MAAM,UAAU,OAAO;AAuBzB;;ACtEF,IAAM,iBAAoD;IACxD,cAAc;IACd,eAAe;IAAA,mDAAA;IAAA,wDAAA;IAAA,+BAAA;IAIf,UAAU;AACZ;AA/CA,IAAA,SAAA,iBAAA,WAAA,SAAA,eAAA,+BAAA,iBAAA,oBAAA,kBAAA,UAAA,aAAA,mBAAA;AAiDO,IAAM,qBAAN,MAAgE;IAOrE,YAAY,OAAA,CAAoC;QAP3C,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACL,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA,WAA+B,CAAC;QAChC,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA,SAA4B,CAAC,CAAA;QAC7B,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QAGE,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAU;YACb,eAAe,QAAQ,aAAA,IAAiB,eAAe,aAAA;YACvD,cAAc,QAAQ,YAAA,IAAgB,eAAe,YAAA;YACrD,UAAU,QAAQ,QAAA,IAAY;YAC9B,OAAO,QAAQ,KAAA,IAAS;YACxB,UAAU,eAAe,QAAA;QAC3B;QAEA,IAAI,CAAC,QAAQ,YAAA,IAAgB,OAAO,SAAW,aAAa;YAE1D,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe;QAChC,OAAO;YACL,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe,QAAQ,YAAA,IAAgB;QACxD;QAIA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,GAAA,GAAM,QAAQ,GAAA;QAE7B,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,UAAA,GAAa,QAAQ,UAAA;QAEpC,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,cAAA,GAAiB,QAAQ,cAAA,IAAkB;QAE1D,MAAM,0RAAY,sBAAA,EAAoB,QAAQ,cAAc;QAC5D,IAAI,WAAW;YACb,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,GAAe,UAAU,YAAA;QAC1C;QAEA,IAAI,QAAQ,SAAA,EAAW;YAErB,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,SAAA,GAAY,QAAQ,SAAA,CAAU,SAAA,CAAU,GAAG,EAAE;QAC9D;QAEA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,iBAAkB,IAAI,wBAAwB;IACrD;IAEA,IAAI,YAAqB;QACvB,kRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,KAAiB,eAAe;YACjD,OAAO;QACT;QAIA,kRAAI,eAAA,EAAA,IAAA,EAAK,SAAQ,QAAA,IAAa,OAAO,YAAY,4RAAe,YAAA,EAAS,QAAQ,GAAA,CAAI,wBAAwB,GAAI;YAC/G,OAAO;QACT;QAKA,IAAI,OAAO,SAAW,eAAe,CAAC,CAAC,QAAQ,WAAW,WAAW;;QAErE;QAEA,OAAO;IACT;IAEA,IAAI,UAAmB;QACrB,qRAAO,eAAA,EAAA,IAAA,EAAK,SAAQ,KAAA,IAAU,OAAO,YAAY,6RAAe,WAAA,EAAS,QAAQ,GAAA,CAAI,qBAAqB;IAC5G;IAEA,OAAO,KAAA,EAAgC;QACrC,MAAM,kBAAkB,gSAAA,EAAA,IAAA,EAAK,+BAAA,mBAAL,IAAA,CAAA,IAAA,EAAqB,MAAM,KAAA,EAAO,MAAM,OAAA;QAEhE,CAAA,GAAA,yQAAA,CAAA,kBAAA,EAAA,IAAA,EAAK,+BAAA,aAAL,IAAA,CAAA,IAAA,EAAe,gBAAgB,KAAA,EAAO;QAEtC,IAAI,+QAAC,kBAAA,EAAA,IAAA,EAAK,+BAAA,iBAAL,IAAA,CAAA,IAAA,EAAmB,iBAAiB,MAAM,iBAAA,GAAoB;YACjE;QACF;QAEA,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAQ,IAAA,CAAK,eAAe;QAEjC,CAAA,GAAA,yQAAA,CAAA,kBAAA,EAAA,IAAA,EAAK,+BAAA,kBAAL,IAAA,CAAA,IAAA;IACF;AAgIF;AAhNE,UAAA,IAAA;AACA,kBAAA,IAAA;AACA,YAAA,IAAA;AACA,UAAA,IAAA;AACA,gBAAA,IAAA;AALK,gCAAA,IAAA;AAmFL,kBAAa,SAAC,eAAA,EAAiC,iBAAA,EAA4B;IACzE,OAAO,IAAA,CAAK,SAAA,IAAa,CAAC,IAAA,CAAK,OAAA,kRAAW,kBAAA,EAAA,IAAA,EAAK,+BAAA,oBAAL,IAAA,CAAA,IAAA,EAAsB,iBAAiB;AACnF;AAEA,qBAAgB,SAAC,eAAA,EAAiC,iBAAA,EAA4B;IAC5E,MAAM,aAAa,KAAK,MAAA,CAAO;IAE/B,MAAM,cACJ,4RAAc,eAAA,EAAA,IAAA,EAAK,SAAQ,YAAA,IAAA,CAC1B,OAAO,sBAAsB,eAAe,cAAc,iBAAA;IAE7D,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,OAAO,CAAC,6RAAA,EAAA,IAAA,EAAK,iBAAgB,gBAAA,CAAiB,eAAe;AAC/D;AAEA,mBAAc,WAAS;IAErB,IAAI,OAAO,WAAW,kBAAa;QACjC,CAAA,GAAA,yQAAA,CAAA,kBAAA,EAAA,IAAA,EAAK,+BAAA,UAAL,IAAA,CAAA,IAAA;QACA;IACF;;IAEA,MAAM,eAAe,mBAAK,SAAQ,UAAU,mBAAK,SAAQ;AA2B3D;AAEA,WAAM,WAAS;IACb,MAAM,IAAI,IAAI,iBAAa,yRAAA,EAAA,IAAA,EAAK,SAAQ,QAAQ,GAAG;QACjD,QAAQ;QAAA,0FAAA;QAER,MAAM,KAAK,SAAA,CAAU;YACnB,QAAQ,6RAAA,EAAA,IAAA,EAAK;QACf,CAAC;QACD,SAAS;YACP,gBAAgB;QAClB;IACF,CAAC,EACE,KAAA,CAAM,IAAM,KAAA,CAAM,EAClB,IAAA,CAAK,MAAM;QACV,CAAA,GAAA,yQAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAU,CAAC,CAAA;IAClB,CAAC,EACA,KAAA,CAAM,IAAM,KAAA,CAAM;AACvB;AAAA;;CAAA,GAKA,cAAS,SAAC,KAAA,EAAgC,OAAA,EAA8B;IACtE,IAAI,CAAC,IAAA,CAAK,OAAA,EAAS;QACjB;IACF;IAEA,IAAI,OAAO,QAAQ,cAAA,KAAmB,aAAa;QACjD,QAAQ,cAAA,CAAe,qBAAqB,KAAK;QACjD,QAAQ,GAAA,CAAI,OAAO;QACnB,QAAQ,QAAA,CAAS;IACnB,OAAO;QACL,QAAQ,GAAA,CAAI,qBAAqB,OAAO,OAAO;IACjD;AACF;AAAA;;;;CAAA,GAOA,oBAAe,WAAG;IAChB,IAAI,cAAc;QAChB,MAAM,6RAAA,EAAA,IAAA,EAAK,WAAU,GAAA;QACrB,uRAAS,eAAA,EAAA,IAAA,EAAK,WAAU,UAAA;IAC1B;IAGA,IAAI,OAAO,WAAW,eAAe,EAAc,KAAP;;IAG5C;IAEA,OAAO;AACT;AAAA;;CAAA,GAKA,oBAAe,SAAC,KAAA,EAAgC,OAAA,EAAoD;IAClG,MAAM,4RAAc,kBAAA,EAAA,IAAA,EAAK,+BAAA,mBAAL,IAAA,CAAA,IAAA;IAEpB,OAAO;QACL;QACA,kRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,YAAA,IAAgB;QACnC,iRAAI,gBAAA,EAAA,IAAA,EAAK,WAAU,YAAA,IAAgB;QACnC,KAAK,YAAY,IAAA;QACjB,MAAM,YAAY,OAAA;QAClB,GAAI,6RAAA,EAAA,IAAA,EAAK,WAAU,cAAA,GAAiB;YAAE,kRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,cAAA;QAAe,IAAI,CAAC,CAAA;QAC7E,iRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,SAAA,GAAY;YAAE,kRAAI,eAAA,EAAA,IAAA,EAAK,WAAU,SAAA;QAAU,IAAI,CAAC,CAAA;QACnE;IACF;AACF;;AC/PF,IAAM,0BAA0B;AAChC,IAAM,yBAAyB;AAC/B,IAAM,sBAAsB;AAe5B,SAAS,6BAA6B,KAAA,EAAuE;IAC3G,OAAO,SACL,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;QAC3C,OAAO;YACL;YACA,mBAAmB;YACnB,SAAS;gBACP;gBACA,gBAAgB,QAAQ,OAAO,UAAU;gBACzC,WAAW,QAAQ,OAAO,YAAY,SAAS;gBAC/C,UAAU,QAAQ,OAAO,YAAY,QAAQ;gBAC7C,WAAW,QAAQ,OAAO,YAAY,SAAS;gBAC/C,GAAG,iBAAA;YACL;QACF;IACF;AACF;AAYO,SAAS,8BACd,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;IAC3C,OAAO,6BAA6B,uBAAuB,EAAE,WAAW,OAAO,iBAAiB;AAClG;AAYO,SAAS,6BACd,SAAA,EACA,KAAA,EACA,iBAAA,EAC2C;IAC3C,OAAO,6BAA6B,sBAAsB,EAAE,WAAW,OAAO,iBAAiB;AACjG;AAaO,SAAS,sBACd,SAAA,EACA,QAAsC,CAAC,CAAA,EACG;IAC1C,OAAO;QACL,OAAO;QACP,mBAAmB;QACnB,SAAS;YACP;YACA,GAAG,KAAA;QACL;IACF;AACF;;ACjGA,IAAM,sBAAsB;AASrB,SAAS,kBACd,MAAA,EACA,OAAA,EACsC;IACtC,OAAO;QACL,OAAO;QACP,SAAS;YACP;YACA,GAAG,OAAA;QACL;IACF;AACF;;ACpBA,IAAM,2BAA2B;AACjC,IAAMA,uBAAsB;AAOrB,SAAS,uBAAuB,OAAA,EAA4E;IACjH,OAAO;QACL,OAAO;QACP,mBAAmBA;QACnB;IACF;AACF", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}, {"offset": {"line": 2458, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}