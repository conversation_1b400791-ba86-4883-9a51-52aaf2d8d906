{"greeting": "Welcome to Roo Code", "task": {"title": "Task", "seeMore": "See more", "seeLess": "See less", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API Cost:", "condenseContext": "Intelligently condense context", "contextWindow": "Context Length:", "closeAndStart": "Close task and start a new one", "export": "Export task history", "delete": "Delete Task (<PERSON><PERSON> + <PERSON>lick to skip confirmation)"}, "unpin": "Unpin", "pin": "<PERSON>n", "retry": {"title": "Retry", "tooltip": "Try the operation again"}, "startNewTask": {"title": "Start New Task", "tooltip": "Begin a new task"}, "proceedAnyways": {"title": "Proceed Anyways", "tooltip": "Continue while command executes"}, "save": {"title": "Save", "tooltip": "Save the file changes"}, "tokenProgress": {"availableSpace": "Available space: {{amount}} tokens", "tokensUsed": "Tokens used: {{used}} of {{total}}", "reservedForResponse": "Reserved for model response: {{amount}} tokens"}, "reject": {"title": "Reject", "tooltip": "Reject this action"}, "completeSubtaskAndReturn": "Complete Subtask and Return", "approve": {"title": "Approve", "tooltip": "Approve this action"}, "read-batch": {"approve": {"title": "Approve All"}, "deny": {"title": "<PERSON><PERSON>"}}, "runCommand": {"title": "Run Command", "tooltip": "Execute this command"}, "proceedWhileRunning": {"title": "Proceed While Running", "tooltip": "Continue despite warnings"}, "killCommand": {"title": "Kill Command", "tooltip": "Kill the current command"}, "resumeTask": {"title": "Resume Task", "tooltip": "Continue the current task"}, "terminate": {"title": "Terminate", "tooltip": "End the current task"}, "cancel": {"title": "Cancel", "tooltip": "Cancel the current operation"}, "scrollToBottom": "Scroll to bottom of chat", "about": "Generate, refactor, and debug code with AI assistance. Check out our <DocsLink>documentation</DocsLink> to learn more.", "onboarding": "Your task list in this workspace is empty.", "rooTips": {"boomerangTasks": {"title": "Boomerang Tasks", "description": "Split tasks into smaller, manageable parts"}, "stickyModels": {"title": "Sticky Models", "description": "Each mode remembers your last used model"}, "tools": {"title": "Tools", "description": "Allow the AI to solve problems by browsing the web, running commands, and more"}, "customizableModes": {"title": "Customizable Modes", "description": "Specialized personas with their own behaviors and assigned models"}}, "selectMode": "Select mode for interaction", "selectApiConfig": "Select API configuration", "enhancePrompt": "Enhance prompt with additional context", "enhancePromptDescription": "The 'Enhance Prompt' button helps improve your prompt by providing additional context, clarification, or rephrasing. Try typing a prompt in here and clicking the button again to see how it works.", "addImages": "Add images to message", "sendMessage": "Send message", "typeMessage": "Type a message...", "typeTask": "Type your task here...", "addContext": "@ to add context, / to switch modes", "dragFiles": "hold shift to drag in files", "dragFilesImages": "hold shift to drag in files/images", "errorReadingFile": "Error reading file:", "noValidImages": "No valid images were processed", "separator": "Separator", "edit": "Edit...", "forNextMode": "for next mode", "apiRequest": {"title": "API Request", "failed": "API Request Failed", "streaming": "API Request...", "cancelled": "API Request Cancelled", "streamingFailed": "API Streaming Failed"}, "checkpoint": {"initial": "Initial Checkpoint", "regular": "Checkpoint", "initializingWarning": "Still initializing checkpoint... If this takes too long, you can disable checkpoints in <settingsLink>settings</settingsLink> and restart your task.", "menu": {"viewDiff": "View Diff", "restore": "Restore Checkpoint", "restoreFiles": "Restore Files", "restoreFilesDescription": "Restores your project's files back to a snapshot taken at this point.", "restoreFilesAndTask": "Restore Files & Task", "confirm": "Confirm", "cancel": "Cancel", "cannotUndo": "This action cannot be undone.", "restoreFilesAndTaskDescription": "Restores your project's files back to a snapshot taken at this point and deletes all messages after this point."}, "current": "Current"}, "contextCondense": {"title": "Context Condensed", "condensing": "Condensing context...", "errorHeader": "Failed to condense context", "tokens": "tokens"}, "instructions": {"wantsToFetch": "<PERSON><PERSON><PERSON> wants to fetch detailed instructions to assist with the current task"}, "fileOperations": {"wantsToRead": "q<PERSON>t wants to read this file:", "wantsToReadMultiple": "qapt wants to read multiple files:", "wantsToReadAndXMore": "qapt wants to read this file and {{count}} more:", "wantsToReadOutsideWorkspace": "qapt wants to read this file outside of the workspace:", "didRead": "qapt read this file:", "wantsToEdit": "qapt wants to edit this file:", "wantsToEditOutsideWorkspace": "qapt wants to edit this file outside of the workspace:", "wantsToCreate": "qapt wants to create a new file:", "wantsToSearchReplace": "qapt wants to search and replace in this file:", "didSearchReplace": "qapt performed search and replace on this file:", "wantsToInsert": "qapt wants to insert content into this file:", "wantsToInsertWithLineNumber": "qapt wants to insert content into this file at line {{lineNumber}}:", "wantsToInsertAtEnd": "qapt wants to append content to the end of this file:"}, "directoryOperations": {"wantsToViewTopLevel": "qapt wants to view the top level files in this directory:", "didViewTopLevel": "qapt viewed the top level files in this directory:", "wantsToViewRecursive": "qapt wants to recursively view all files in this directory:", "didViewRecursive": "qapt recursively viewed all files in this directory:", "wantsToViewDefinitions": "qapt wants to view source code definition names used in this directory:", "didViewDefinitions": "qapt viewed source code definition names used in this directory:", "wantsToSearch": "qapt wants to search this directory for <code>{{regex}}</code>:", "didSearch": "qapt searched this directory for <code>{{regex}}</code>:"}, "codebaseSearch": {"wantsToSearch": "qapt wants to search the codebase for <code>{{query}}</code>:", "wantsToSearchWithPath": "qapt wants to search the codebase for <code>{{query}}</code> in <code>{{path}}</code>:", "didSearch": "Found {{count}} result(s) for <code>{{query}}</code>:"}, "commandOutput": "Command Output", "response": "Response", "arguments": "Arguments", "mcp": {"wantsToUseTool": "Qapt wants to use a tool on the {{serverName}} MCP server:", "wantsToAccessResource": "Qapt wants to access a resource on the {{serverName}} MCP server:"}, "modes": {"wantsToSwitch": "Qapt wants to switch to {{mode}} mode", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> wants to switch to {{mode}} mode because: {{reason}}", "didSwitch": "Qapt switched to {{mode}} mode", "didSwitchWithReason": "Qapt switched to {{mode}} mode because: {{reason}}"}, "subtasks": {"wantsToCreate": "Qapt wants to create a new subtask in {{mode}} mode:", "wantsToFinish": "<PERSON><PERSON><PERSON> wants to finish this subtask", "newTaskContent": "Subtask Instructions", "completionContent": "Subtask Completed", "resultContent": "Subtask Results", "defaultResult": "Please continue to the next task.", "completionInstructions": "Subtask completed! You can review the results and suggest any corrections or next steps. If everything looks good, confirm to return the result to the parent task."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> has a question:"}, "taskCompleted": "Results", "error": "Error", "diffError": {"title": "Edit Unsuccessful"}, "troubleMessage": "<PERSON><PERSON> is having trouble...", "powershell": {"issues": "It seems like you're having Windows PowerShell issues, please see this"}, "autoApprove": {"title": "Auto-approve:", "none": "None", "description": "Auto-approve allows Roo Code to perform actions without asking for permission. Only enable for actions you fully trust. More detailed configuration available in <settingsLink>Settings</settingsLink>."}, "announcement": {"title": "🎉 Roo Code {{version}} Released", "description": "Roo Code {{version}} brings powerful new features and improvements based on your feedback.", "whatsNew": "What's New", "feature1": "<bold>Intelligent Context Condensing Enabled by De<PERSON>ult</bold>: Context condensing is now enabled by default with configurable settings for when automatic condensing happens", "feature2": "<bold>Manual Condensing Button</bold>: New button in the task header allows you to manually trigger context condensing at any time", "feature3": "<bold>Enhanced Condensing Settings</bold>: Fine-tune when and how automatic condensing occurs through the <contextSettingsLink>Context Settings</contextSettingsLink>", "hideButton": "Hide announcement", "detailsDiscussLinks": "Get more details and discuss in <discordLink>Discord</discordLink> and <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "Thinking", "seconds": "{{count}}s"}, "followUpSuggest": {"copyToInput": "Copy to input (same as shift + click)"}, "browser": {"rooWantsToUse": "<PERSON><PERSON> wants to use the browser:", "consoleLogs": "Console <PERSON>gs", "noNewLogs": "(No new logs)", "screenshot": "Browser screenshot", "cursor": "cursor", "navigation": {"step": "Step {{current}} of {{total}}", "previous": "Previous", "next": "Next"}, "sessionStarted": "Browser Session Started", "actions": {"title": "Browse Action: ", "launch": "Launch browser at {{url}}", "click": "Click ({{coordinate}})", "type": "Type \"{{text}}\"", "scrollDown": "Scroll down", "scrollUp": "Scroll up", "close": "Close browser"}}, "codeblock": {"tooltips": {"expand": "Expand code block", "collapse": "Collapse code block", "enable_wrap": "Enable word wrap", "disable_wrap": "Disable word wrap", "copy_code": "Copy code"}}, "systemPromptWarning": "WARNING: Custom system prompt override active. This can severely break functionality and cause unpredictable behavior.", "profileViolationWarning": "The current profile violates your organization's settings", "shellIntegration": {"title": "Command Execution Warning", "description": "Your command is being executed without VSCode terminal shell integration. To suppress this warning you can disable shell integration in the <strong>Terminal</strong> section of the <settingsLink>Roo Code settings</settingsLink> or troubleshoot VSCode terminal integration using the link below.", "troubleshooting": "Click here for shell integration documentation."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Auto-Approved Request Limit Reached", "description": "<PERSON><PERSON> has reached the auto-approved limit of {{count}} API request(s). Would you like to reset the count and proceed with the task?", "button": "Reset and Continue"}}}