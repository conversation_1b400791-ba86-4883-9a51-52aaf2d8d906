{"title": "Серверы MCP", "done": "Готово", "description": "Включи Model Context Protocol (MCP), чтобы Roo Code мог использовать дополнительные инструменты и сервисы с внешних серверов. Это расширяет возможности Roo. <0>Подробнее</0>", "enableToggle": {"title": "Включить серверы MCP", "description": "Включи, что<PERSON>ы Roo мог использовать инструменты с подключённых серверов MCP. Это даст Roo больше возможностей. Если не планируешь использовать эти дополнительные инструменты, выключи для экономии токенов API."}, "enableServerCreation": {"title": "Включить создание серверов MCP", "description": "Включи, что<PERSON>ы Roo помогал создавать <1>новые</1> кастомные серверы MCP. <0>Подробнее о создании серверов</0>", "hint": "Совет: чтобы снизить расходы на токены API, отключай эту настройку, когда не просишь Roo создать новый сервер MCP."}, "editGlobalMCP": "Редактировать глобальный MCP", "editProjectMCP": "Редактировать проектный MCP", "learnMoreEditingSettings": "Подробнее о редактировании файлов настроек MCP", "tool": {"alwaysAllow": "Всегда разрешать", "parameters": "Параметры", "noDescription": "Нет описания"}, "tabs": {"tools": "Инструменты", "resources": "Ресурсы", "errors": "Ошибки"}, "emptyState": {"noTools": "Инструменты не найдены", "noResources": "Ресурсы не найдены", "noLogs": "Логи не найдены", "noErrors": "Ошибки не найдены"}, "networkTimeout": {"label": "Тайм-аут сети", "description": "Максимальное время ожидания ответа сервера", "options": {"15seconds": "15 секунд", "30seconds": "30 секунд", "1minute": "1 минута", "5minutes": "5 минут", "10minutes": "10 минут", "15minutes": "15 минут", "30minutes": "30 минут", "60minutes": "60 минут"}}, "deleteDialog": {"title": "Удалить сервер MCP", "description": "Ты уверен, что хочешь удалить сервер MCP \"{{serverName}}\"? Это действие нельзя отменить.", "cancel": "Отмена", "delete": "Удалить"}, "serverStatus": {"retrying": "Повторная попытка...", "retryConnection": "Повторить подключение"}}