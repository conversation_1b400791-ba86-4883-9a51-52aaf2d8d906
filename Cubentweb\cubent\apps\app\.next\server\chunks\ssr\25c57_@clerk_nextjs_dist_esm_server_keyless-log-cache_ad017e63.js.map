{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40clerk%2Bnextjs%406.20.0_next%401_c77f473bcb010f4557dab79e25c1da72/node_modules/%40clerk/nextjs/src/server/keyless-log-cache.ts"], "sourcesContent": ["import type { AccountlessApplication } from '@clerk/backend';\nimport { isDevelopmentEnvironment } from '@clerk/shared/utils';\n// 10 minutes in milliseconds\nconst THROTTLE_DURATION_MS = 10 * 60 * 1000;\n\nfunction createClerkDevCache() {\n  if (!isDevelopmentEnvironment()) {\n    return;\n  }\n\n  if (!global.__clerk_internal_keyless_logger) {\n    global.__clerk_internal_keyless_logger = {\n      __cache: new Map<string, { expiresAt: number; data?: unknown }>(),\n\n      log: function ({ cacheKey, msg }) {\n        if (this.__cache.has(cacheKey) && Date.now() < (this.__cache.get(cacheKey)?.expiresAt || 0)) {\n          return;\n        }\n\n        console.log(msg);\n\n        this.__cache.set(cacheKey, {\n          expiresAt: Date.now() + THROTTLE_DURATION_MS,\n        });\n      },\n      run: async function (\n        callback,\n        { cacheKey, onSuccessStale = THROTTLE_DURATION_MS, onErrorStale = THROTTLE_DURATION_MS },\n      ) {\n        if (this.__cache.has(cacheKey) && Date.now() < (this.__cache.get(cacheKey)?.expiresAt || 0)) {\n          return this.__cache.get(cacheKey)?.data;\n        }\n\n        try {\n          const result = await callback();\n\n          this.__cache.set(cacheKey, {\n            expiresAt: Date.now() + onSuccessStale,\n            data: result,\n          });\n          return result;\n        } catch (e) {\n          this.__cache.set(cacheKey, {\n            expiresAt: Date.now() + onErrorStale,\n          });\n\n          throw e;\n        }\n      },\n    };\n  }\n\n  return globalThis.__clerk_internal_keyless_logger;\n}\n\nexport const createKeylessModeMessage = (keys: AccountlessApplication) => {\n  return `\\n\\x1b[35m\\n[Clerk]:\\x1b[0m You are running in keyless mode.\\nYou can \\x1b[35mclaim your keys\\x1b[0m by visiting ${keys.claimUrl}\\n`;\n};\n\nexport const createConfirmationMessage = () => {\n  return `\\n\\x1b[35m\\n[Clerk]:\\x1b[0m Your application is running with your claimed keys.\\nYou can safely remove the \\x1b[35m.clerk/\\x1b[0m from your project.\\n`;\n};\n\nexport const clerkDevelopmentCache = createClerkDevCache();\n"], "names": [], "mappings": ";;;;;AACA,SAAS,gCAAgC;;;;AAEzC,MAAM,uBAAuB,KAAK,KAAK;AAEvC,SAAS,sBAAsB;IAC7B,IAAI,+QAAC,2BAAA,CAAyB,IAAG;QAC/B;IACF;IAEA,IAAI,CAAC,OAAO,+BAAA,EAAiC;QAC3C,OAAO,+BAAA,GAAkC;YACvC,SAAS,aAAA,GAAA,IAAI,IAAmD;YAEhE,KAAK,SAAU,EAAE,QAAA,EAAU,GAAA,CAAI,CAAA,EAAG;gBAdxC,IAAA;gBAeQ,IAAI,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,QAAQ,KAAK,KAAK,GAAA,CAAI,IAAA,CAAA,CAAA,CAAK,KAAA,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,QAAQ,CAAA,KAAzB,OAAA,KAAA,IAAA,GAA4B,SAAA,KAAa,CAAA,GAAI;oBAC3F;gBACF;gBAEA,QAAQ,GAAA,CAAI,GAAG;gBAEf,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,UAAU;oBACzB,WAAW,KAAK,GAAA,CAAI,IAAI;gBAC1B,CAAC;YACH;YACA,KAAK,eACH,QAAA,EACA,EAAE,QAAA,EAAU,iBAAiB,oBAAA,EAAsB,eAAe,oBAAA,CAAqB,CAAA,EACvF;gBA5BR,IAAA,IAAA;gBA6BQ,IAAI,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,QAAQ,KAAK,KAAK,GAAA,CAAI,IAAA,CAAA,CAAA,CAAK,KAAA,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,QAAQ,CAAA,KAAzB,OAAA,KAAA,IAAA,GAA4B,SAAA,KAAa,CAAA,GAAI;oBAC3F,OAAA,CAAO,KAAA,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,QAAQ,CAAA,KAAzB,OAAA,KAAA,IAAA,GAA4B,IAAA;gBACrC;gBAEA,IAAI;oBACF,MAAM,SAAS,MAAM,SAAS;oBAE9B,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,UAAU;wBACzB,WAAW,KAAK,GAAA,CAAI,IAAI;wBACxB,MAAM;oBACR,CAAC;oBACD,OAAO;gBACT,EAAA,OAAS,GAAG;oBACV,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,UAAU;wBACzB,WAAW,KAAK,GAAA,CAAI,IAAI;oBAC1B,CAAC;oBAED,MAAM;gBACR;YACF;QACF;IACF;IAEA,OAAO,WAAW,+BAAA;AACpB;AAEO,MAAM,2BAA2B,CAAC,SAAiC;IACxE,OAAO,CAAA;;;mDAAA,EAAoH,KAAK,QAAQ,CAAA;AAAA,CAAA;AAC1I;AAEO,MAAM,4BAA4B,MAAM;IAC7C,OAAO,CAAA;;;;AAAA,CAAA;AACT;AAEO,MAAM,wBAAwB,oBAAoB", "ignoreList": [0], "debugId": null}}]}