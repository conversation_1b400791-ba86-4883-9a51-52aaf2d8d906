{"title": "MCP सर्वर", "done": "हो गया", "description": "Model Context Protocol (MCP) सक्षम करें ताकि Roo Code बाहरी सर्वरों से अतिरिक्त टूल्स और सेवाएँ इस्तेमाल कर सके। इससे Roo तुम्हारे लिए और भी काम कर सकता है। <0>और जानें</0>", "enableToggle": {"title": "MCP सर्वर सक्षम करें", "description": "इसे ON करो ताकि Roo जुड़े हुए MCP सर्वरों से टूल्स इस्तेमाल कर सके। इससे Roo को और क्षमताएँ मिलती हैं। अगर तुम ये अतिरिक्त टूल्स इस्तेमाल नहीं करना चाहते, तो इसे OFF करो ताकि API टोकन लागत कम हो सके।"}, "enableServerCreation": {"title": "MCP सर्वर बनाना सक्षम करें", "description": "इसे ON करो ताकि Roo तुम्हारी मदद से <1>नए</1> कस्टम MCP सर्वर बना सके। <0>सर्वर बनाना जानें</0>", "hint": "टिप: API टोकन लागत कम करने के लिए, ज<PERSON>oo से नया MCP सर्वर नहीं बनवा रहे हो तो इस सेटिंग को बंद कर दो।"}, "editGlobalMCP": "ग्लोबल MCP एडिट करें", "editProjectMCP": "प्रोजेक्ट MCP एडिट करें", "learnMoreEditingSettings": "MCP सेटिंग्स फाइल एडिट करने के बारे में जानें", "tool": {"alwaysAllow": "हमेशा अनुमति दें", "parameters": "पैरामीटर", "noDescription": "कोई विवरण नहीं"}, "tabs": {"tools": "टूल्स", "resources": "संसाधन", "errors": "त्रुटियाँ"}, "emptyState": {"noTools": "कोई टूल नहीं मिला", "noResources": "कोई संसाधन नहीं मिला", "noLogs": "कोई लॉग नहीं मिला", "noErrors": "कोई त्रुटि नहीं मिली"}, "networkTimeout": {"label": "नेटवर्क टाइमआउट", "description": "सर्वर रिस्पॉन्स के लिए अधिकतम प्रतीक्षा समय", "options": {"15seconds": "15 सेकंड", "30seconds": "30 सेकंड", "1minute": "1 मिनट", "5minutes": "5 मिनट", "10minutes": "10 मिनट", "15minutes": "15 मिनट", "30minutes": "30 मिनट", "60minutes": "60 मिनट"}}, "deleteDialog": {"title": "MCP सर्वर हटाएँ", "description": "क्या तुम वाकई MCP सर्वर \"{{serverName}}\" हटाना चाहते हो? यह क्रिया वापस नहीं ली जा सकती।", "cancel": "रद्<PERSON> करें", "delete": "हटाएँ"}, "serverStatus": {"retrying": "फिर से कोशिश कर रहा है...", "retryConnection": "कनेक्शन फिर से आज़माएँ"}}