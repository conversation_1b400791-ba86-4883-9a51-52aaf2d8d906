{"extension.displayName": "Roo Code (旧 Roo Cline)", "extension.description": "エディタ内のAIエージェントによる開発チーム。", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.activitybar.title": "Roo Code", "views.sidebar.name": "Roo Code", "command.newTask.title": "新しいタスク", "command.mcpServers.title": "MCPサーバー", "command.prompts.title": "モード", "command.history.title": "履歴", "command.openInEditor.title": "エディタで開く", "command.settings.title": "設定", "command.documentation.title": "ドキュメント", "command.openInNewTab.title": "新しいタブで開く", "command.explainCode.title": "コードの説明", "command.fixCode.title": "コードの修正", "command.improveCode.title": "コードの改善", "command.addToContext.title": "コンテキストに追加", "command.focusInput.title": "入力フィールドにフォーカス", "command.setCustomStoragePath.title": "カスタムストレージパスの設定", "command.terminal.addToContext.title": "ターミナルの内容をコンテキストに追加", "command.terminal.fixCommand.title": "このコマンドを修正", "command.terminal.explainCommand.title": "このコマンドを説明", "command.acceptInput.title": "入力/提案を承認", "configuration.title": "qapt coder", "commands.allowedCommands.description": "'常に実行操作を承認する'が有効な場合に自動実行できるコマンド", "settings.vsCodeLmModelSelector.description": "VSCode 言語モデル API の設定", "settings.vsCodeLmModelSelector.vendor.description": "言語モデルのベンダー（例：copilot）", "settings.vsCodeLmModelSelector.family.description": "言語モデルのファミリー（例：gpt-4）", "settings.customStoragePath.description": "カスタムストレージパス。デフォルトの場所を使用する場合は空のままにします。絶対パスをサポートします（例：'D:\\qaptCoderStorage'）", "settings.qaptCoderCloudEnabled.description": "qapt coder Cloud を有効にする。"}