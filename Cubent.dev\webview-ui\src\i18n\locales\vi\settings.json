{"common": {"save": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON> th<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "reset": "Đặt lại", "select": "<PERSON><PERSON><PERSON>", "add": "<PERSON>hê<PERSON> tiêu đề", "remove": "Xóa"}, "header": {"title": "Cài đặt", "saveButtonTooltip": "<PERSON><PERSON><PERSON> thay đổi", "nothingChangedTooltip": "<PERSON><PERSON><PERSON><PERSON> có gì thay đổi", "doneButtonTooltip": "<PERSON><PERSON>y thay đổi chưa lưu và đóng bảng cài đặt"}, "unsavedChangesDialog": {"title": "<PERSON><PERSON> đ<PERSON>i ch<PERSON>a lưu", "description": "Bạn có muốn hủy thay đổi và tiếp tục không?", "cancelButton": "<PERSON><PERSON><PERSON>", "discardButton": "<PERSON><PERSON><PERSON> thay đổi"}, "sections": {"providers": "<PERSON><PERSON><PERSON> cung cấp", "autoApprove": "<PERSON><PERSON>", "browser": "<PERSON><PERSON><PERSON><PERSON>", "checkpoints": "<PERSON><PERSON><PERSON><PERSON> kiểm tra", "notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "contextManagement": "<PERSON><PERSON> c<PERSON>", "terminal": "Terminal", "prompts": "<PERSON><PERSON><PERSON>", "experimental": "<PERSON><PERSON><PERSON>", "language": "<PERSON><PERSON><PERSON>", "about": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u"}, "prompts": {"description": "<PERSON><PERSON><PERSON> hình các lời nhắc hỗ trợ được sử dụng cho các hành động nhanh như cải thiện lời nhắc, gi<PERSON><PERSON> thích mã và khắc phục sự cố. Những lời nhắc này giúp Roo cung cấp hỗ trợ tốt hơn cho các tác vụ phát triển phổ biến."}, "codeIndex": {"title": "<PERSON><PERSON><PERSON> chỉ mục mã nguồn", "enableLabel": "<PERSON><PERSON><PERSON> lập chỉ mục mã nguồn", "enableDescription": "<0><PERSON><PERSON><PERSON> chỉ mục mã nguồn</0> là một tính năng thử nghiệm tạo ra chỉ mục tìm kiếm ngữ nghĩa cho dự án của bạn bằng cách sử dụng AI embeddings. <PERSON>i<PERSON>u này cho phép Roo Code hiểu rõ hơn và điều hướng các codebase lớn bằng cách tìm mã liên quan dựa trên ý nghĩa thay vì chỉ từ khóa.", "providerLabel": "<PERSON><PERSON><PERSON> cung cấp nhúng", "selectProviderPlaceholder": "<PERSON><PERSON><PERSON> nhà cung cấp", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "openaiKeyLabel": "Khóa OpenAI:", "modelLabel": "<PERSON><PERSON>", "selectModelPlaceholder": "<PERSON><PERSON><PERSON> mô hình", "ollamaUrlLabel": "URL Ollama:", "qdrantUrlLabel": "URL Qdrant", "qdrantKeyLabel": "<PERSON><PERSON><PERSON><PERSON>nt:", "startIndexingButton": "<PERSON><PERSON><PERSON> đ<PERSON>u lập chỉ mục", "clearIndexDataButton": "<PERSON><PERSON><PERSON> dữ liệu chỉ mục", "unsavedSettingsMessage": "<PERSON><PERSON> lòng lưu cài đặt của bạn trước khi bắt đầu quá trình lập chỉ mục.", "clearDataDialog": {"title": "Bạn có chắc không?", "description": "Hành động này không thể hoàn tác. Điều này sẽ xóa vĩnh viễn dữ liệu chỉ mục mã nguồn của bạn.", "cancelButton": "<PERSON><PERSON><PERSON>", "confirmButton": "<PERSON><PERSON><PERSON> dữ liệu"}}, "autoApprove": {"description": "<PERSON> phép Roo tự động thực hiện các hoạt động mà không cần phê duyệt. Chỉ bật những cài đặt này nếu bạn hoàn toàn tin tưởng AI và hiểu rõ các rủi ro bảo mật liên quan.", "readOnly": {"label": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bậ<PERSON>, <PERSON><PERSON> sẽ tự động xem nội dung thư mục và đọc tệp mà không yêu cầu bạn nhấp vào nút Phê duyệt.", "outsideWorkspace": {"label": "<PERSON><PERSON> g<PERSON><PERSON> các tệp ngo<PERSON>i không gian làm việc", "description": "<PERSON> phép Roo đọc các tệp bên ngoài không gian làm việc hiện tại mà không yêu cầu phê duy<PERSON>t."}}, "write": {"label": "<PERSON><PERSON>", "description": "Tự động tạo và chỉnh sửa tệp mà không cần phê duyệt", "delayLabel": "<PERSON><PERSON><PERSON> hoãn sau khi ghi để cho phép chẩn đoán phát hiện các vấn đề tiềm ẩn", "outsideWorkspace": {"label": "<PERSON><PERSON> g<PERSON><PERSON> các tệp ngo<PERSON>i không gian làm việc", "description": "<PERSON> phép Roo tạo và chỉnh sửa các tệp bên ngoài không gian làm việc hiện tại mà không yêu cầu phê duyệt."}}, "browser": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Tự động thực hiện các hành động trình duyệt mà không cần phê duyệt. Lưu ý: Chỉ áp dụng khi mô hình hỗ trợ sử dụng máy tính"}, "retry": {"label": "<PERSON><PERSON><PERSON> lại", "description": "Tự động thử lại các yêu cầu API thất bại khi máy chủ trả về phản hồi lỗi", "delayLabel": "<PERSON><PERSON><PERSON> hoãn tr<PERSON><PERSON><PERSON> khi thử lại yêu cầu"}, "mcp": {"label": "MCP", "description": "Bật tự động phê duyệt các công cụ MCP riêng lẻ trong chế độ xem Máy chủ MCP (yêu cầu cả cài đặt này và hộp kiểm \"<PERSON><PERSON><PERSON> cho phép\" của công cụ)"}, "modeSwitch": {"label": "<PERSON><PERSON> độ", "description": "Tự động chuyển đổi giữa các chế độ khác nhau mà không cần phê duyệt"}, "subtasks": {"label": "<PERSON><PERSON>ng vi<PERSON><PERSON> phụ", "description": "<PERSON> phép tạo và hoàn thành các công việc phụ mà không cần phê duy<PERSON>t"}, "execute": {"label": "Th<PERSON>c thi", "description": "Tự động thực thi các lệnh terminal đư<PERSON><PERSON> phép mà không cần phê duyệt", "allowedCommands": "<PERSON><PERSON><PERSON> l<PERSON>nh tự động thực thi đ<PERSON><PERSON><PERSON> phép", "allowedCommandsDescription": "Tiền tố lệnh có thể được tự động thực thi khi \"<PERSON><PERSON><PERSON> phê duyệt các hoạt động thực thi\" đ<PERSON><PERSON><PERSON> bật. Thêm * để cho phép tất cả các lệnh (sử dụng cẩn thận).", "commandPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiền tố lệnh (ví dụ: 'git ')", "addButton": "<PERSON><PERSON><PERSON><PERSON>"}, "apiRequestLimit": {"title": "<PERSON><PERSON> lư<PERSON><PERSON> yêu cầu tối đa", "description": "Tự động thực hiện số lượng API request này trước khi yêu cầu phê duyệt để tiếp tục với nhiệm vụ.", "unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn"}}, "providers": {"providerDocumentation": "<PERSON><PERSON><PERSON> li<PERSON>u {{provider}}", "configProfile": "<PERSON><PERSON> sơ cấu hình", "description": "<PERSON><PERSON><PERSON> các cấu hình <PERSON> khác nhau để nhanh chóng chuyển đổi giữa các nhà cung cấp và cài đặt.", "apiProvider": "Nhà cung cấp API", "model": "Mẫu", "nameEmpty": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "nameExists": "<PERSON><PERSON> tồn tại một hồ sơ với tên này", "deleteProfile": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "invalidArnFormat": "Định dạng ARN không hợp lệ. <PERSON><PERSON> lòng kiểm tra các ví dụ ở trên.", "enterNewName": "<PERSON><PERSON><PERSON><PERSON> tên mới", "addProfile": "<PERSON><PERSON><PERSON><PERSON> hồ s<PERSON>", "renameProfile": "<PERSON><PERSON><PERSON> tên hồ sơ", "newProfile": "<PERSON><PERSON> sơ cấu hình mới", "enterProfileName": "<PERSON><PERSON><PERSON><PERSON> tên hồ sơ", "createProfile": "<PERSON><PERSON><PERSON> hồ sơ", "cannotDeleteOnlyProfile": "<PERSON><PERSON><PERSON><PERSON> thể x<PERSON>a hồ sơ duy nhất", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON><PERSON> hồ sơ", "noMatchFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấ<PERSON> hồ sơ phù hợp", "vscodeLmDescription": "API Mô hình Ngôn ngữ VS Code cho phép bạn chạy các mô hình được cung cấp bởi các tiện ích mở rộng khác của VS Code (bao gồm nhưng không giới hạn ở GitHub Copilot). Cách dễ nhất để bắt đầu là cài đặt các tiện ích mở rộng Copilot và Copilot Chat từ VS Code Marketplace.", "awsCustomArnUse": "<PERSON><PERSON><PERSON><PERSON> một ARN Amazon Bedrock hợp lệ cho mô hình bạn muốn sử dụng. <PERSON><PERSON> dụ về định dạng:", "awsCustomArnDesc": "<PERSON><PERSON><PERSON> bảo rằng vùng trong ARN khớp với vùng AWS đã chọn ở trên.", "openRouterApiKey": "Khóa API OpenRouter", "getOpenRouterApiKey": "Lấy khóa API OpenRouter", "apiKeyStorageNotice": "Khóa API được lưu trữ an toàn trong Bộ lưu trữ bí mật của VSCode", "glamaApiKey": "Khóa API Glama", "getGlamaApiKey": "Lấy khóa API Glama", "useCustomBaseUrl": "Sử dụng URL cơ sở tùy chỉnh", "useReasoning": "<PERSON><PERSON><PERSON> lý lu<PERSON>n", "useHostHeader": "Sử dụng tiêu đề Host tùy chỉnh", "useLegacyFormat": "Sử dụng định dạng API OpenAI cũ", "customHeaders": "Tiêu đề tùy chỉnh", "headerName": "<PERSON>ên tiêu đề", "headerValue": "<PERSON><PERSON><PERSON> trị tiêu đề", "noCustomHeaders": "<PERSON><PERSON>a có tiêu đề tùy chỉnh nào được định nghĩa. Nhấp vào nút + để thêm.", "requestyApiKey": "Khóa API Requesty", "refreshModels": {"label": "<PERSON><PERSON><PERSON> mới mô hình", "hint": "<PERSON><PERSON> lòng mở lại cài đặt để xem các mô hình mới nhất.", "loading": "<PERSON><PERSON> làm mới danh sách mô hình...", "success": "<PERSON>h sách mô hình đã đư<PERSON><PERSON> làm mới thành công!", "error": "<PERSON><PERSON><PERSON><PERSON> thể làm mới danh sách mô hình. <PERSON><PERSON> lòng thử lại."}, "getRequestyApiKey": "Lấy khóa API Requesty", "openRouterTransformsText": "<PERSON><PERSON> lời nhắc và chuỗi tin nhắn theo kích thước ngữ cảnh (<a>OpenRouter Transforms</a>)", "anthropicApiKey": "Khóa API Anthropic", "getAnthropicApiKey": "Lấy khóa API Anthropic", "anthropicUseAuthToken": "Truyền khóa API Anthropic dưới dạng tiêu đề Authorization thay vì X-Api-Key", "chutesApiKey": "Khóa API Chutes", "getChutesApiKey": "Lấy khóa API Chutes", "deepSeekApiKey": "Khóa API DeepSeek", "getDeepSeekApiKey": "Lấy khóa API DeepSeek", "geminiApiKey": "Khóa API Gemini", "getGroqApiKey": "Lấy khóa API Groq", "groqApiKey": "Khóa API Groq", "getGeminiApiKey": "Lấy khóa API Gemini", "openAiApiKey": "Khóa API OpenAI", "apiKey": "Khóa API", "openAiBaseUrl": "URL cơ sở", "getOpenAiApiKey": "Lấy khóa API OpenAI", "mistralApiKey": "Khóa API Mistral", "getMistralApiKey": "Lấy khóa API Mistral / Codestral", "codestralBaseUrl": "URL cơ sở Codestral (<PERSON><PERSON><PERSON>)", "codestralBaseUrlDesc": "Đặt URL thay thế cho mô hình Codestral.", "xaiApiKey": "Khóa API xAI", "getXaiApiKey": "Lấy khóa API xAI", "litellmApiKey": "Khóa API LiteLLM", "litellmBaseUrl": "URL cơ sở LiteLLM", "awsCredentials": "Thông tin xác thực AWS", "awsProfile": "Hồ sơ AWS", "awsProfileName": "<PERSON><PERSON><PERSON> <PERSON>ồ s<PERSON> AWS", "awsAccessKey": "Khóa truy c<PERSON>p AWS", "awsSecretKey": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> mật AWS", "awsSessionToken": "Token phiên AWS", "awsRegion": "Vùng AWS", "awsCrossRegion": "Sử dụng suy luận liên vùng", "awsBedrockVpc": {"useCustomVpcEndpoint": "Sử dụng điểm cuối VPC tùy chỉnh", "vpcEndpointUrlPlaceholder": "Nhập URL điểm cuối VPC (tù<PERSON> ch<PERSON><PERSON>)", "examples": "<PERSON><PERSON> dụ:"}, "enablePromptCaching": "<PERSON><PERSON><PERSON> bộ nhớ đệm lời nhắc", "enablePromptCachingTitle": "Bật bộ nhớ đệm lời nhắc để cải thiện hiệu suất và giảm chi phí cho các mô hình được hỗ trợ.", "cacheUsageNote": "Lưu ý: <PERSON><PERSON><PERSON> bạn không thấy việc sử dụng bộ nhớ đệm, hãy thử chọn một mô hình khác và sau đó chọn lại mô hình mong muốn của bạn.", "vscodeLmModel": "<PERSON><PERSON> hình ngôn ngữ", "vscodeLmWarning": "Lưu ý: <PERSON><PERSON><PERSON> là tích hợp thử nghiệm và hỗ trợ nhà cung cấp có thể khác nhau. Nếu bạn nhận được lỗi về mô hình không được hỗ trợ, đó là vấn đề từ phía nhà cung cấp.", "googleCloudSetup": {"title": "<PERSON><PERSON> sử dụng Google Cloud Vertex AI, bạn cần:", "step1": "1. <PERSON><PERSON><PERSON> t<PERSON>ho<PERSON>n Google Cloud, kích hoạt Vertex AI API và kích hoạt các mô hình <PERSON> mong muốn.", "step2": "2. Cài đặt Google Cloud CLI và cấu hình thông tin xác thực mặc định của ứng dụng.", "step3": "3. Hoặc tạo tài k<PERSON>n dịch vụ với thông tin xác thực."}, "googleCloudCredentials": "Thông tin xác thực Google Cloud", "googleCloudKeyFile": "Đường dẫn tệp khóa Google Cloud", "googleCloudProjectId": "ID dự án Google Cloud", "googleCloudRegion": "Vùng Google Cloud", "lmStudio": {"baseUrl": "URL cơ sở (t<PERSON><PERSON> ch<PERSON>)", "modelId": "ID mô hình", "speculativeDecoding": "<PERSON>ật gi<PERSON>i mã suy đoán", "draftModelId": "ID mô hình nháp", "draftModelDesc": "<PERSON>ô hình nháp phải từ cùng một họ mô hình để giải mã suy đoán hoạt động chính xác.", "selectDraftModel": "<PERSON><PERSON><PERSON> mô hình nh<PERSON>p", "noModelsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mô hình nháp nào. <PERSON>ui lòng đảm bảo LM Studio đang chạy với chế độ máy chủ đư<PERSON><PERSON> bật.", "description": "LM Studio cho phép bạn chạy các mô hình cục bộ trên máy tính của bạn. <PERSON><PERSON> biết hướng dẫn về cách bắt đầu, xem <a>hướng dẫn nhanh</a> của họ. Bạn cũng sẽ cần khởi động tính năng <b>máy chủ cục bộ</b> của LM Studio để sử dụng nó với tiện ích mở rộng này. <span>Lưu ý:</span> Roo Code sử dụng các lời nhắc phức tạp và hoạt động tốt nhất với các mô hình Claude. Các mô hình kém mạnh hơn có thể không hoạt động như mong đợi."}, "ollama": {"baseUrl": "URL cơ sở (t<PERSON><PERSON> ch<PERSON>)", "modelId": "ID mô hình", "description": "<PERSON>llama cho phép bạn chạy các mô hình cục bộ trên máy tính của bạn. <PERSON><PERSON> biết hướng dẫn về cách bắt đầu, xem hướng dẫn nhanh của họ.", "warning": "Lưu ý: <PERSON><PERSON> Code sử dụng các lời nhắc phức tạp và hoạt động tốt nhất với các mô hình Claude. Các mô hình kém mạnh hơn có thể không hoạt động như mong đợi."}, "unboundApiKey": "Khóa API Unbound", "getUnboundApiKey": "Lấy khóa API Unbound", "unboundRefreshModelsSuccess": "<PERSON><PERSON> cập nhật danh sách mô hình! Bây giờ bạn có thể chọn từ các mô hình mới nhất.", "unboundInvalidApiKey": "Khóa API không hợp lệ. Vui lòng kiểm tra khóa API của bạn và thử lại.", "humanRelay": {"description": "Không cần khóa API, nhưng người dùng cần giúp sao chép và dán thông tin vào AI trò chuyện web.", "instructions": "Trong quá trình sử dụng, một hộp thoại sẽ xuất hiện và tin nhắn hiện tại sẽ được tự động sao chép vào clipboard. Bạn cần dán chúng vào các phiên bản web của AI (như ChatGPT hoặc Claude), sau đó sao chép phản hồi của AI trở lại hộp thoại và nhấp vào nút xác nhận."}, "openRouter": {"providerRouting": {"title": "<PERSON><PERSON><PERSON> tuyến nhà cung cấp OpenRouter", "description": "OpenRouter chuyển hướng yêu cầu đến các nhà cung cấp tốt nhất hiện có cho mô hình của bạn. <PERSON> mặc định, các yêu cầu được cân bằng giữa các nhà cung cấp hàng đầu để tối đa hóa thời gian hoạt động. <PERSON><PERSON>, bạn có thể chọn một nhà cung cấp cụ thể để sử dụng cho mô hình này.", "learnMore": "<PERSON><PERSON>m hiểu thêm về định tuyến nhà cung cấp"}}, "customModel": {"capabilities": "<PERSON><PERSON>u hình các khả năng và giá cả cho mô hình tương thích OpenAI tùy chỉnh của bạn. H<PERSON>y cẩn thận khi chỉ định khả năng của mô hình, vì chúng có thể ảnh hưởng đến cách Roo Code hoạt động.", "maxTokens": {"label": "<PERSON><PERSON> <PERSON> đầu ra tối đa", "description": "Số lượng token tối đa mà mô hình có thể tạo ra trong một phản hồi. (Chỉ định -1 để cho phép máy chủ đặt số token tối đa.)"}, "contextWindow": {"label": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> c<PERSON>a sổ ngữ cảnh", "description": "Tổng số token (đầu vào + đầu ra) mà mô hình có thể xử lý."}, "imageSupport": {"label": "Hỗ trợ hình <PERSON>nh", "description": "<PERSON>ô hình này có khả năng xử lý và hiểu hình ảnh không?"}, "computerUse": {"label": "Sử dụng máy t<PERSON>h", "description": "<PERSON><PERSON> hình này có khả năng tương tác với trình duyệt không? (ví dụ: <PERSON> 3.7 Sonnet)."}, "promptCache": {"label": "<PERSON>ộ nhớ đệm lời nh<PERSON>c", "description": "<PERSON>ô hình này có khả năng lưu trữ lời nhắc trong bộ nhớ đệm không?"}, "pricing": {"input": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>u vào", "description": "Chi phí cho mỗi triệu token trong đầu vào/lời nhắc. Điều này ảnh hưởng đến chi phí gửi ngữ cảnh và hướng dẫn đến mô hình."}, "output": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>u ra", "description": "Chi phí cho mỗi triệu token trong phản hồi của mô hình. Điều này ảnh hưởng đến chi phí của nội dung được tạo ra và hoàn thành."}, "cacheReads": {"label": "<PERSON><PERSON><PERSON> bộ nhớ đệm", "description": "<PERSON> phí cho mỗi triệu token khi đọc từ bộ nhớ đệm. <PERSON><PERSON><PERSON> là giá được tính khi một phản hồi được lưu trong bộ nhớ đệm được truy xuất."}, "cacheWrites": {"label": "<PERSON><PERSON><PERSON> ghi bộ nhớ đệm", "description": "Chi phí cho mỗi triệu token khi ghi vào bộ nhớ đệm. Đ<PERSON>y là giá được tính khi một lời nhắc được lưu vào bộ nhớ đệm lần đầu tiên."}}, "resetDefaults": "Đặt lại về mặc định"}, "rateLimitSeconds": {"label": "<PERSON><PERSON><PERSON><PERSON> hạn tốc độ", "description": "Thời gian tối thiểu giữa các yêu cầu API."}, "reasoningEffort": {"label": "Nỗ lực suy luận của mô hình", "high": "<PERSON>", "medium": "<PERSON>rung bình", "low": "<PERSON><PERSON><PERSON><PERSON>"}, "setReasoningLevel": "<PERSON><PERSON><PERSON> ho<PERSON> nỗ lực suy luận"}, "browser": {"enable": {"label": "<PERSON><PERSON><PERSON> công cụ trình du<PERSON>", "description": "<PERSON><PERSON> đư<PERSON><PERSON> b<PERSON>, <PERSON><PERSON> có thể sử dụng trình duyệt để tương tác với các trang web khi sử dụng các mô hình hỗ trợ sử dụng máy tính. <0>Tìm hiểu thêm</0>"}, "viewport": {"label": "<PERSON><PERSON><PERSON><PERSON> khung nh<PERSON>n", "description": "<PERSON><PERSON><PERSON> kích thước khung nhìn cho tương tác trình duyệt. <PERSON><PERSON><PERSON><PERSON> này ảnh hưởng đến cách trang web được hiển thị và tương tác.", "options": {"largeDesktop": "<PERSON><PERSON><PERSON> t<PERSON>h để bàn lớn (1280x800)", "smallDesktop": "<PERSON><PERSON><PERSON> t<PERSON>h để bàn nhỏ (900x600)", "tablet": "<PERSON><PERSON><PERSON> (768x1024)", "mobile": "<PERSON> (360x640)"}}, "screenshotQuality": {"label": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON>nh chụp màn hình", "description": "<PERSON>iều chỉnh chất lượng WebP của ảnh chụp màn hình trình duyệt. Gi<PERSON> trị cao hơn cung cấp ảnh chụp màn hình rõ ràng hơn nhưng tăng sử dụng token."}, "remote": {"label": "Sử dụng kết nối trình duyệt từ xa", "description": "Kết nối với trình duyệt Chrome đang chạy với tính năng gỡ lỗi từ xa được bật (--remote-debugging-port=9222).", "urlPlaceholder": "URL tùy chỉnh (ví dụ: http://localhost:9222)", "testButton": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testingButton": "<PERSON><PERSON> kiểm tra...", "instructions": "Nhập địa chỉ DevTools Protocol hoặc để trống để tự động phát hiện các instance Chrome cục bộ. N<PERSON>t <PERSON>ểm tra kết nối sẽ thử URL tùy chỉnh nếu được cung cấp, hoặc tự động phát hiện nếu trường này trống."}}, "checkpoints": {"enable": {"label": "<PERSON><PERSON><PERSON> điểm kiểm tra tự động", "description": "<PERSON><PERSON> đư<PERSON><PERSON> b<PERSON>, <PERSON><PERSON> sẽ tự động tạo các điểm kiểm tra trong quá trình thực hiện nhiệm vụ, gi<PERSON><PERSON> dễ dàng xem lại các thay đổi hoặc quay lại trạng thái trước đó. <0>Tìm hiểu thêm</0>"}}, "notifications": {"sound": {"label": "<PERSON><PERSON><PERSON> hi<PERSON><PERSON>", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> b<PERSON>, <PERSON><PERSON> sẽ phát hiệu ứng âm thanh cho thông báo và sự kiện.", "volumeLabel": "<PERSON><PERSON>"}, "tts": {"label": "<PERSON><PERSON><PERSON> chuyển văn bản thành giọng nói", "description": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> bật, <PERSON><PERSON> sẽ đọc to các phản hồi của nó bằng chức năng chuyển văn bản thành giọng nói.", "speedLabel": "<PERSON><PERSON><PERSON>"}}, "contextManagement": {"description": "<PERSON><PERSON><PERSON> soát thông tin nào được đưa vào cửa sổ ngữ cảnh của AI, ảnh hưởng đến việc sử dụng token và chất lượng phản hồi", "autoCondenseContextPercent": {"label": "Ngưỡng kích hoạt nén ngữ cảnh thông minh", "description": "<PERSON><PERSON> cửa sổ ngữ cảnh đạt đến ngưỡng này, <PERSON><PERSON> sẽ tự động nén nó."}, "condensingApiConfiguration": {"label": "<PERSON><PERSON><PERSON> hình <PERSON> cho Tóm tắt Ngữ cảnh", "description": "<PERSON><PERSON><PERSON> cấu hình API để sử dụng cho các thao tác tóm tắt ngữ cảnh. <PERSON><PERSON> trống để sử dụng cấu hình đang hoạt động hiện tại.", "useCurrentConfig": "Mặc định"}, "customCondensingPrompt": {"label": "<PERSON><PERSON><PERSON> nhắc nén ngữ cảnh tùy chỉnh", "description": "<PERSON><PERSON><PERSON> nhắc hệ thống tùy chỉnh cho việc nén ngữ cảnh. Đ<PERSON> trống để sử dụng lời nhắc mặc định.", "placeholder": "<PERSON><PERSON><PERSON><PERSON> prompt tóm tắt tùy chỉnh của bạn tại đây...\n\nBạn có thể sử dụng cùng cấu trúc như prompt mặc định:\n- <PERSON><PERSON><PERSON><PERSON> hội thoại trước\n- Công việc hiện tại\n- <PERSON><PERSON><PERSON><PERSON> niệm kỹ thuật chính\n- Tệp và mã liên quan\n- Gi<PERSON>i quyết vấn đề\n- Công việc đang chờ và các bước tiếp theo", "reset": "<PERSON><PERSON><PERSON><PERSON> phục mặc định", "hint": "<PERSON><PERSON> trống = sử dụng prompt mặc định"}, "autoCondenseContext": {"name": "Tự động kích hoạt nén ngữ cảnh thông minh"}, "openTabs": {"label": "<PERSON><PERSON><PERSON><PERSON> hạn ngữ cảnh tab đang mở", "description": "S<PERSON> lượng tab VSCode đang mở tối đa để đưa vào ngữ cảnh. Gi<PERSON> trị cao hơn cung cấp nhiều ngữ cảnh hơn nhưng tăng sử dụng token."}, "workspaceFiles": {"label": "<PERSON><PERSON><PERSON><PERSON> hạn ngữ cảnh tệp workspace", "description": "<PERSON><PERSON> lượng tệp tối đa để đưa vào chi tiết thư mục làm việc hiện tại. Gi<PERSON> trị cao hơn cung cấp nhiều ngữ cảnh hơn nhưng tăng sử dụng token."}, "rooignore": {"label": "<PERSON><PERSON><PERSON> thị tệp .roo<PERSON><PERSON> trong danh sách và tìm kiếm", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bật, các tệp khớp với mẫu trong .rooignore sẽ được hiển thị trong danh sách với biểu tượng khóa. <PERSON><PERSON> bị tắt, các tệp này sẽ hoàn toàn bị ẩn khỏi danh sách tệp và tìm kiếm."}, "maxReadFile": {"label": "Ngưỡng tự động cắt ngắn khi đọc tệp", "description": "<PERSON>oo đọc số dòng này khi mô hình không chỉ định giá trị bắt đầu/kết thúc. Nếu số này nhỏ hơn tổng số dòng của tệp, Roo sẽ tạo một chỉ mục số dòng của các định nghĩa mã. Trường hợp đặc biệt: -1 chỉ thị Roo đọc toàn bộ tệp (không tạo chỉ mục), và 0 chỉ thị không đọc dòng nào và chỉ cung cấp chỉ mục dòng cho ngữ cảnh tối thiểu. Gi<PERSON> trị thấp hơn giảm thiểu việc sử dụng ngữ cảnh ban đầu, cho phép đọc chính xác các phạm vi dòng sau này. Các yêu cầu có chỉ định bắt đầu/kết thúc rõ ràng không bị giới hạn bởi cài đặt này.", "lines": "dòng", "always_full_read": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> toàn bộ tệp"}, "maxConcurrentFileReads": {"label": "<PERSON><PERSON><PERSON><PERSON> hạn đ<PERSON>c file đồng thời", "description": "Số lượng file tối đa mà công cụ 'read_file' có thể xử lý cùng lúc. Giá trị cao hơn có thể tăng tốc độ đọc nhiều file nhỏ nhưng sẽ tăng mức sử dụng bộ nhớ."}}, "terminal": {"basic": {"label": "Cài đặt Terminal: Cơ bản", "description": "Cài đặt cơ bản cho terminal"}, "advanced": {"label": "Cài đặt Terminal: Nâng cao", "description": "<PERSON><PERSON><PERSON> tùy chọn sau có thể yêu cầu khởi động lại terminal để áp dụng cài đặt."}, "outputLineLimit": {"label": "Giới hạn đầu ra terminal", "description": "Số dòng tối đa để đưa vào đầu ra terminal khi thực hiện lệnh. <PERSON><PERSON> vư<PERSON><PERSON> quá, các dòng sẽ bị xóa khỏi phần giữa, tiết kiệm token. <0>Tìm hiểu thêm</0>"}, "shellIntegrationTimeout": {"label": "Thời gian chờ tích hợp shell terminal", "description": "Thời gian tối đa để chờ tích hợp shell khởi tạo trước khi thực hiện lệnh. Đối với người dùng có thời gian khởi động shell dài, giá trị này có thể cần được tăng lên nếu bạn thấy lỗi \"Shell Integration Unavailable\" trong terminal. <0>Tìm hiểu thêm</0>"}, "shellIntegrationDisabled": {"label": "Tắt tích hợp shell terminal", "description": "Bật tùy chọn này nếu lệnh terminal không hoạt động chính xác hoặc bạn thấy lỗi 'Shell Integration Unavailable'. Tùy chọn này sử dụng phương pháp đơn giản hơn để chạy lệnh, bỏ qua một số tính năng terminal nâng cao. <0>Tìm hiểu thêm</0>"}, "commandDelay": {"label": "Độ trễ lệnh terminal", "description": "<PERSON><PERSON> trễ tính bằng mili giây để thêm vào sau khi thực hiện lệnh. Cài đặt mặc định là 0 sẽ tắt hoàn toàn độ trễ. Điều này có thể giúp đảm bảo đầu ra lệnh được ghi lại đầy đủ trong các terminal có vấn đề về thời gian. Trong hầu hết các terminal, điều này được thực hiện bằng cách đặt `PROMPT_COMMAND='sleep N'` và PowerShell thêm `start-sleep` vào cuối mỗi lệnh. Ban đầu là giải pháp cho lỗi VSCode#237208 và có thể không cần thiết. <0>Tìm hiểu thêm</0>"}, "compressProgressBar": {"label": "<PERSON><PERSON> đ<PERSON>u ra thanh tiến trình", "description": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> b<PERSON>, xử lý đầu ra terminal với các ký tự carriage return (\\r) để mô phỏng cách terminal thật hiển thị nội dung. Điều này loại bỏ các trạng thái trung gian của thanh tiến trình, chỉ giữ lại trạng thái cuối cùng, gi<PERSON><PERSON> tiết kiệm không gian ngữ cảnh cho thông tin quan trọng hơn. <0>Tìm hiểu thêm</0>"}, "powershellCounter": {"label": "<PERSON><PERSON>t g<PERSON><PERSON><PERSON> ph<PERSON>p bộ đếm PowerShell", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bật, thê<PERSON> một bộ đếm vào các lệnh PowerShell để đảm bảo thực thi lệnh chính xác. Điều này giúp ích với các terminal PowerShell có thể gặp vấn đề về ghi lại đầu ra. <0>Tìm hiểu thêm</0>"}, "zshClearEolMark": {"label": "<PERSON>óa dấu cuối dòng ZSH", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bật, xóa dấu cuối dòng ZSH bằng cách đặt PROMPT_EOL_MARK=''. Điều này ngăn chặn các vấn đề về diễn giải đầu ra lệnh khi kết thúc bằng các ký tự đặc biệt như '%'. <0>Tìm hiểu thêm</0>"}, "zshOhMy": {"label": "<PERSON><PERSON><PERSON> t<PERSON>ch hợp <PERSON> Zsh", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bậ<PERSON>, đặt ITERM_SHELL_INTEGRATION_INSTALLED=Yes để kích hoạt các tính năng tích hợp shell của Oh My Zsh. Việc áp dụng cài đặt này có thể yêu cầu khởi động lại IDE. <0>Tìm hiểu thêm</0>"}, "zshP10k": {"label": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợp Powerlevel10k", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bậ<PERSON>, đặt POWERLEVEL9K_TERM_SHELL_INTEGRATION=true để kích hoạt các tính năng tích hợp shell của Powerlevel10k. <0>Tìm hiểu thêm</0>"}, "zdotdir": {"label": "Bật xử lý ZDOTDIR", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bậ<PERSON>, t<PERSON><PERSON> thư mục tạm thời cho ZDOTDIR để xử lý tích hợp shell zsh một cách chính xác. Điều này đảm bảo tích hợp shell VSCode hoạt động chính xác với zsh trong khi vẫn giữ nguyên cấu hình zsh của bạn. <0>Tìm hiểu thêm</0>"}, "inheritEnv": {"label": "<PERSON><PERSON> thừa biến môi trường", "description": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> b<PERSON>, terminal sẽ kế thừa các biến môi trường từ tiến trình cha của VSCode, nh<PERSON> các cài đặt tích hợp shell được định nghĩa trong hồ sơ người dùng. Điều này trực tiếp chuyển đổi cài đặt toàn cục của VSCode `terminal.integrated.inheritEnv`. <0>Tìm hiểu thêm</0>"}}, "advanced": {"diff": {"label": "<PERSON><PERSON><PERSON> chỉnh sửa qua diff", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bật, <PERSON><PERSON> sẽ có thể chỉnh sửa tệp nhanh hơn và sẽ tự động từ chối ghi toàn bộ tệp bị cắt ngắn. Hoạt động tốt nhất với mô hình <PERSON> 3.7 Sonnet mới nhất.", "strategy": {"label": "<PERSON><PERSON><PERSON> diff", "options": {"standard": "<PERSON><PERSON><PERSON><PERSON> (khối đơn)", "multiBlock": "Thử nghiệm: <PERSON><PERSON> đa kh<PERSON>i", "unified": "Th<PERSON> nghiệm: <PERSON><PERSON> thống nh<PERSON>t"}, "descriptions": {"standard": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> diff tiêu chuẩn áp dụng thay đổi cho một khối mã tại một thời điểm.", "unified": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> diff thống nhất thực hiện nhiều cách tiếp cận để áp dụng diff và chọn cách tiếp cận tốt nhất.", "multiBlock": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> diff đa khối cho phép cập nhật nhiều khối mã trong một tệp trong một yêu cầu."}}, "matchPrecision": {"label": "<PERSON><PERSON> ch<PERSON>h x<PERSON>c kh<PERSON>p", "description": "<PERSON><PERSON> trư<PERSON><PERSON> này kiểm soát mức độ chính xác các phần mã phải khớp khi áp dụng diff. G<PERSON><PERSON> trị thấp hơn cho phép khớp linh hoạt hơn nhưng tăng nguy cơ thay thế không chính xác. Sử dụng giá trị dưới 100% với sự thận trọng cao."}}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Sử dụng chiến l<PERSON> diff thống nhất thử nghiệm", "description": "Bật chiến lượ<PERSON> diff thống nhất thử nghiệm. Chiến lược này có thể giảm số lần thử lại do lỗi mô hình nhưng có thể gây ra hành vi không mong muốn hoặc chỉnh sửa không chính xác. Chỉ bật nếu bạn hiểu rõ các rủi ro và sẵn sàng xem xét cẩn thận tất cả các thay đổi."}, "SEARCH_AND_REPLACE": {"name": "Sử dụng công cụ tìm kiếm và thay thế thử nghiệm", "description": "<PERSON><PERSON>t công cụ tìm kiếm và thay thế thử nghiệm, cho ph<PERSON><PERSON> Roo thay thế nhiều phiên bản của một thuật ngữ tìm kiếm trong một yêu cầu."}, "INSERT_BLOCK": {"name": "Sử dụng công cụ chèn nội dung thử nghiệm", "description": "<PERSON><PERSON><PERSON> công cụ chèn nội dung thử nghiệm, cho ph<PERSON><PERSON> <PERSON>oo chèn nội dung tại số dòng cụ thể mà không cần tạo diff."}, "POWER_STEERING": {"name": "Sử dụng chế độ \"power steering\" thử nghiệm", "description": "<PERSON><PERSON> đư<PERSON><PERSON> bật, <PERSON><PERSON> sẽ nhắc nhở mô hình về chi tiết định nghĩa chế độ hiện tại thường xuyên hơn. Điều này sẽ dẫn đến việc tuân thủ chặt chẽ hơn các định nghĩa vai trò và hướng dẫn tùy chỉnh, nhưng sẽ sử dụng nhiều token hơn cho mỗi tin nhắn."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Sử dụng công cụ diff đa khối thử nghiệm", "description": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> bậ<PERSON>, <PERSON><PERSON> sẽ sử dụng công cụ diff đa khối. <PERSON><PERSON><PERSON>u này sẽ cố gắng cập nhật nhiều khối mã trong tệp trong một yêu cầu."}, "CONCURRENT_FILE_READS": {"name": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> tệp đồng thời", "description": "<PERSON><PERSON> b<PERSON><PERSON>, <PERSON><PERSON> có thể đọc nhiều tệp trong một yêu cầu duy nhất (tối đa 15 tệp). <PERSON><PERSON> tắ<PERSON>, <PERSON><PERSON> phải đọc từng tệp một. <PERSON>i<PERSON><PERSON> tắt có thể hữu ích khi làm việc với các mô hình ít khả năng hơn hoặc khi bạn muốn kiểm soát nhiều hơn quyền truy cập tệp."}}, "promptCaching": {"label": "<PERSON><PERSON><PERSON> bộ nhớ đệm prompt", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> sẽ không sử dụng bộ nhớ đệm prompt cho mô hình này."}, "temperature": {"useCustom": "Sử dụng nhiệt độ tùy chỉnh", "description": "<PERSON><PERSON><PERSON> so<PERSON>t tính ngẫu nhiên trong phản hồi của mô hình.", "rangeDescription": "Gi<PERSON> trị cao hơn làm cho đầu ra ngẫu nhiên hơn, giá trị thấp hơn làm cho nó xác định hơn."}, "modelInfo": {"supportsImages": "Hỗ trợ hình <PERSON>nh", "noImages": "<PERSON>hông hỗ trợ hình ảnh", "supportsComputerUse": "Hỗ trợ sử dụng máy tính", "noComputerUse": "Không hỗ trợ sử dụng máy tính", "supportsPromptCache": "Hỗ trợ bộ nhớ đệm lời nhắc", "noPromptCache": "<PERSON>hông hỗ trợ bộ nhớ đệm lời nhắc", "maxOutput": "<PERSON><PERSON><PERSON> ra tối đa", "inputPrice": "<PERSON><PERSON><PERSON> đ<PERSON>u vào", "outputPrice": "<PERSON><PERSON><PERSON> đ<PERSON>u ra", "cacheReadsPrice": "<PERSON><PERSON><PERSON> bộ nhớ đệm", "cacheWritesPrice": "<PERSON><PERSON><PERSON> ghi bộ nhớ đệm", "enableStreaming": "Bật streaming", "enableR1Format": "<PERSON><PERSON><PERSON> ho<PERSON>t tham số mô hình R1", "enableR1FormatTips": "<PERSON><PERSON><PERSON> k<PERSON>ch hoạt khi sử dụng các mô hình R1 như QWQ, để tránh lỗi 400", "useAzure": "Sử dụng Azure", "azureApiVersion": "Đặt phiên bản API Azure", "gemini": {"freeRequests": "* <PERSON><PERSON><PERSON> phí đến {{count}} yêu cầu mỗi phút. <PERSON><PERSON> đ<PERSON>, thanh to<PERSON> phụ thuộc vào kích thước lời nh<PERSON>c.", "pricingDetails": "<PERSON><PERSON> biết thêm thông tin, xem chi tiết giá.", "billingEstimate": "* Thanh toán là ước tính - chi phí chính xác phụ thuộc vào kích thước lời nhắc."}}, "modelPicker": {"automaticFetch": "Tiện ích mở rộng tự động lấy danh sách mới nhất các mô hình có sẵn trên <serviceLink>{{serviceName}}</serviceLink>. Nếu bạn không chắc chắn nên chọn mô hình nào, Roo Code hoạt động tốt nhất với <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Bạn cũng có thể thử tìm kiếm \"free\" cho các tùy chọn miễn phí hiện có.", "label": "<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON>", "noMatchFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "useCustomModel": "Sử dụng tùy chỉnh: {{modelId}}"}, "footer": {"feedback": "<PERSON><PERSON><PERSON> bạn có bất kỳ câu hỏi hoặc phản hồi nào, vui lòng mở một vấn đề tại <githubLink>github.com/RooCodeInc/Roo-Code</githubLink> hoặc tham gia <redditLink>reddit.com/r/RooCode</redditLink> hoặc <discordLink>discord.gg/roocode</discordLink>", "telemetry": {"label": "<PERSON> phép báo cáo lỗi và sử dụng ẩn danh", "description": "<PERSON><PERSON><PERSON><PERSON> cải thiện Roo Code bằng cách gửi dữ liệu sử dụng ẩn danh và báo cáo lỗi. Không bao giờ gửi mã, lời nhắc hoặc thông tin cá nhân. <PERSON>em ch<PERSON>h sách bảo mật của chúng tôi để biết thêm chi tiết."}, "settings": {"import": "<PERSON><PERSON><PERSON><PERSON>", "export": "<PERSON><PERSON><PERSON>", "reset": "Đặt lại"}}, "thinkingBudget": {"maxTokens": "Tokens tối đa", "maxThinkingTokens": "Tokens suy nghĩ tối đa"}, "validation": {"apiKey": "Bạn phải cung cấp khóa API hợp lệ.", "awsRegion": "Bạn phải chọn một vùng để sử dụng Amazon Bedrock.", "googleCloud": "Bạn phải cung cấp ID dự án và vùng Google Cloud hợp lệ.", "modelId": "Bạn ph<PERSON>i cung cấp ID mô hình hợp lệ.", "modelSelector": "Bạn ph<PERSON>i cung cấp bộ chọn mô hình hợp lệ.", "openAi": "Bạn phải cung cấp URL cơ sở, khóa API và ID mô hình hợp lệ.", "arn": {"invalidFormat": "Định dạng ARN không hợp lệ. <PERSON><PERSON> lòng kiểm tra yêu cầu về định dạng.", "regionMismatch": "Cảnh báo: <PERSON>ù<PERSON> trong ARN của bạn ({{arnRegion}}) không khớp với vùng bạn đã chọn ({{region}}). <PERSON><PERSON><PERSON>u này có thể gây ra vấn đề truy cập. <PERSON><PERSON><PERSON> cung cấp sẽ sử dụng vùng từ ARN."}, "modelAvailability": "ID mô hình ({{modelId}}) bạn đã cung cấp không khả dụng. <PERSON><PERSON> lòng chọn một mô hình khác.", "providerNotAllowed": "Nhà cung cấp '{{provider}}' không được phép bởi tổ chức của bạn", "modelNotAllowed": "Mô hình '{{model}}' không được phép cho nhà cung cấp '{{provider}}' bởi tổ chức của bạn", "profileInvalid": "<PERSON><PERSON> sơ này chứa một nhà cung cấp hoặc mô hình không được phép bởi tổ chức của bạn"}, "placeholders": {"apiKey": "Nhập khóa API...", "profileName": "<PERSON><PERSON><PERSON><PERSON> tên hồ sơ", "accessKey": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>a truy cập...", "secretKey": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> b<PERSON> mật...", "sessionToken": "Nhập token phiên...", "credentialsJson": "Nhập JSON thông tin xác thực...", "keyFilePath": "<PERSON><PERSON><PERSON><PERSON> đường dẫn tệp khóa...", "projectId": "Nhập ID dự án...", "customArn": "Nhập ARN (vd: arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Nhập URL cơ sở...", "modelId": {"lmStudio": "vd: meta-llama-3.1-8b-instruct", "lmStudioDraft": "vd: lmstudio-community/llama-3.2-1b-instruct", "ollama": "vd: llama3.1"}, "numbers": {"maxTokens": "vd: 4096", "contextWindow": "vd: 128000", "inputPrice": "vd: 0.0001", "outputPrice": "vd: 0.0002", "cacheWritePrice": "vd: 0.00005"}}, "defaults": {"ollamaUrl": "Mặc định: http://localhost:11434", "lmStudioUrl": "Mặc định: http://localhost:1234", "geminiUrl": "Mặc định: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "ARN tùy chỉnh", "useCustomArn": "Sử dụng ARN tùy chỉnh..."}}