{"$schema": "https://turbo.build/schema.json", "tasks": {"lint": {}, "check-types": {}, "test": {"dependsOn": ["@qapt-coder/types#build"]}, "format": {}, "clean": {"cache": false}, "build": {"outputs": ["dist/**"], "inputs": ["src/**", "package.json", "tsconfig.json", "tsup.config.ts"]}, "build:nightly": {}, "bundle": {"dependsOn": ["^build"], "cache": false}, "bundle:nightly": {"dependsOn": ["^build"], "cache": false}, "vsix": {"dependsOn": ["bundle", "@qapt-coder/vscode-webview#build"], "cache": false}, "vsix:nightly": {"dependsOn": ["bundle:nightly", "@qapt-coder/vscode-webview#build:nightly"], "cache": false}, "watch:bundle": {"dependsOn": ["@qapt-coder/build#build", "@qapt-coder/types#build"], "cache": false}, "watch:tsc": {"cache": false}}}