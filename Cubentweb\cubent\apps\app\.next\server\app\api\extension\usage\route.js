const CHUNK_PUBLIC_PATH = "server/app/api/extension/usage/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/_bc451f4c._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__0371936d._.js");
runtime.loadChunk("server/chunks/661a5_next_cfa76811._.js");
runtime.loadChunk("server/chunks/c67f4_@clerk_backend_dist_b935a38f._.js");
runtime.loadChunk("server/chunks/af4dc_@neondatabase_serverless_index_mjs_4dc99100._.js");
runtime.loadChunk("server/chunks/e28ae_ws_6888342c._.js");
runtime.loadChunk("server/chunks/ec4b9_zod_dist_esm_f73b059d._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_98d57aa3._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/app/.next-internal/server/app/api/extension/usage/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/app/app/api/extension/usage/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/app/app/api/extension/usage/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
