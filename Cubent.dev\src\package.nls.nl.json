{"extension.displayName": "Roo Code (voorheen Roo Cline)", "extension.description": "<PERSON><PERSON> compleet ontwi<PERSON>kel<PERSON><PERSON> van AI-agents in je editor.", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.activitybar.title": "Roo Code", "views.sidebar.name": "Roo Code", "command.newTask.title": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "command.openInEditor.title": "<PERSON>en in Editor", "command.settings.title": "Instellingen", "command.documentation.title": "Documentatie", "command.openInNewTab.title": "Openen in Nieuw Tabblad", "command.explainCode.title": "Leg Code Uit", "command.fixCode.title": "Repareer Code", "command.improveCode.title": "Verbeter Code", "command.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "command.focusInput.title": "Focus op Invoerveld", "command.setCustomStoragePath.title": "Aangepast Opslagpad Instellen", "command.terminal.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> aan <PERSON>n", "command.terminal.fixCommand.title": "Repareer Dit Commando", "command.terminal.explainCommand.title": "Leg Dit Commando Uit", "command.acceptInput.title": "Invoer/Suggestie Accepteren", "configuration.title": "Roo Code", "commands.allowedCommands.description": "Commando's die automatisch kunnen worden uitgevoerd wanneer 'Altijd goedkeuren uitvoerbewerkingen' is ingeschakeld", "settings.vsCodeLmModelSelector.description": "Instellingen voor VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "De leverancier van het taalmodel (bijv. copilot)", "settings.vsCodeLmModelSelector.family.description": "De familie van het taalmodel (bijv. gpt-4)", "settings.customStoragePath.description": "Aangepast opslagpad. Laat leeg om de standaardlocatie te gebruiken. Ondersteunt absolute paden (bijv. 'D:\\qaptCoderStorage')", "settings.qaptCoderCloudEnabled.description": "qapt coder Cloud inschakelen."}