(()=>{var e={};e.id=6855,e.ids=[6855],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,s,t)=>{"use strict";e.exports=t(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>I,routeModule:()=>m,serverHooks:()=>j,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>w});var r={};t.r(r),t.d(r,{DELETE:()=>f,GET:()=>A,PATCH:()=>v,POST:()=>x});var n=t(26142),i=t(94327),o=t(34862),a=t(37838),u=t(18815),d=t(26239),c=t(25);let p=c.z.object({sessionId:c.z.string().min(1),extensionVersion:c.z.string().optional(),vscodeVersion:c.z.string().optional(),platform:c.z.string().optional(),metadata:c.z.record(c.z.any()).optional()}),l=c.z.object({sessionId:c.z.string().min(1),isActive:c.z.boolean().optional(),tokensUsed:c.z.number().int().min(0).optional(),requestsMade:c.z.number().int().min(0).optional(),metadata:c.z.record(c.z.any()).optional()});async function x(e){try{let{userId:s}=await (0,a.j)();if(!s)return d.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),r=p.safeParse(t);if(!r.success)return d.NextResponse.json({error:"Invalid session data",details:r.error.errors},{status:400});let{sessionId:n,extensionVersion:i,vscodeVersion:o,platform:c,metadata:l}=r.data,x=await u.database.user.findUnique({where:{clerkId:s}});if(!x)return d.NextResponse.json({error:"User not found"},{status:404});let v=await u.database.extensionSession.upsert({where:{userId_sessionId:{userId:x.id,sessionId:n}},update:{isActive:!0,lastActiveAt:new Date,extensionVersion:i,vscodeVersion:o,platform:c,metadata:l},create:{userId:x.id,sessionId:n,isActive:!0,extensionVersion:i,vscodeVersion:o,platform:c,metadata:l,tokensUsed:0,requestsMade:0,createdAt:new Date,lastActiveAt:new Date}});return d.NextResponse.json({success:!0,session:{id:v.id,sessionId:v.sessionId,isActive:v.isActive,createdAt:v.createdAt,lastActiveAt:v.lastActiveAt}})}catch(e){return console.error("Session creation error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function v(e){try{let{userId:s}=await (0,a.j)();if(!s)return d.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),r=l.safeParse(t);if(!r.success)return d.NextResponse.json({error:"Invalid session update data",details:r.error.errors},{status:400});let{sessionId:n,isActive:i,tokensUsed:o,requestsMade:c,metadata:p}=r.data,x=await u.database.user.findUnique({where:{clerkId:s}});if(!x)return d.NextResponse.json({error:"User not found"},{status:404});let v={lastActiveAt:new Date};"boolean"==typeof i&&(v.isActive=i),"number"==typeof o&&(v.tokensUsed={increment:o}),"number"==typeof c&&(v.requestsMade={increment:c}),p&&(v.metadata=p);let A=await u.database.extensionSession.update({where:{userId_sessionId:{userId:x.id,sessionId:n}},data:v});return d.NextResponse.json({success:!0,session:{id:A.id,sessionId:A.sessionId,isActive:A.isActive,tokensUsed:A.tokensUsed,requestsMade:A.requestsMade,lastActiveAt:A.lastActiveAt}})}catch(e){return console.error("Session update error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function A(){try{let{userId:e}=await (0,a.j)();if(!e)return d.NextResponse.json({error:"Unauthorized"},{status:401});let s=await u.database.user.findUnique({where:{clerkId:e}});if(!s)return d.NextResponse.json({error:"User not found"},{status:404});let t=await u.database.extensionSession.findMany({where:{userId:s.id},orderBy:{lastActiveAt:"desc"}}),r=t.filter(e=>e.isActive),n=t.length;return d.NextResponse.json({sessions:t.map(e=>({id:e.id,sessionId:e.sessionId,isActive:e.isActive,extensionVersion:e.extensionVersion,vscodeVersion:e.vscodeVersion,platform:e.platform,tokensUsed:e.tokensUsed,requestsMade:e.requestsMade,createdAt:e.createdAt,lastActiveAt:e.lastActiveAt,metadata:e.metadata})),summary:{totalSessions:n,activeSessions:r.length,lastActiveSession:t[0]?.lastActiveAt||null}})}catch(e){return console.error("Sessions fetch error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e){try{let{userId:s}=await (0,a.j)();if(!s)return d.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("sessionId"),n="true"===t.get("all"),i=await u.database.user.findUnique({where:{clerkId:s}});if(!i)return d.NextResponse.json({error:"User not found"},{status:404});if(n){let e=await u.database.extensionSession.updateMany({where:{userId:i.id,isActive:!0},data:{isActive:!1,lastActiveAt:new Date}});return d.NextResponse.json({success:!0,message:`Terminated ${e.count} active sessions`,terminatedSessions:e.count})}if(!r)return d.NextResponse.json({error:"Session ID required or use ?all=true to terminate all sessions"},{status:400});{let e=await u.database.extensionSession.update({where:{userId_sessionId:{userId:i.id,sessionId:r}},data:{isActive:!1,lastActiveAt:new Date}});return d.NextResponse.json({success:!0,message:"Session terminated successfully",session:{id:e.id,sessionId:e.sessionId,isActive:e.isActive,lastActiveAt:e.lastActiveAt}})}}catch(e){return console.error("Session termination error:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/sessions/route",pathname:"/api/extension/sessions",filename:"route",bundlePath:"app/api/extension/sessions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\sessions\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:q,workUnitAsyncStorage:w,serverHooks:j}=m;function I(){return(0,o.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:w})}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>t(94442));module.exports=r})();