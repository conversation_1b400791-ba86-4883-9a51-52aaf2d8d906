(()=>{var e={};e.id=1823,e.ids=[1823],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8473:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>y,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>x});var i=t(26142),u=t(94327),o=t(34862),n=t(37838),a=t(18815),p=t(26239),c=t(55511);async function x(){try{let{userId:e}=await (0,n.j)();if(!e)return p.NextResponse.json({error:"Unauthorized"},{status:401});let r=await a.database.user.findUnique({where:{clerkId:e}});if(!r)return p.NextResponse.json({error:"User not found"},{status:404});let t=`cubent_${(0,c.randomBytes)(32).toString("hex")}`;return await a.database.user.update({where:{id:r.id},data:{extensionApiKey:t}}),await a.database.extensionSession.updateMany({where:{userId:r.id},data:{isActive:!1}}),p.NextResponse.json({success:!0,message:"New API key generated successfully",apiKey:t})}catch(e){return console.error("API key generation error:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/extension/generate-key/route",pathname:"/api/extension/generate-key",filename:"route",bundlePath:"app/api/extension/generate-key/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\generate-key\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:q,workUnitAsyncStorage:l,serverHooks:y}=d;function h(){return(0,o.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:l})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,r,t)=>{"use strict";e.exports=t(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>t(8473));module.exports=s})();