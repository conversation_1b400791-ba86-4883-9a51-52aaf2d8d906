(()=>{var e={};e.id=2825,e.ids=[2825],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12901:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var r=s(8741),i=s(23056),n=s(76315),a=s(97495);let o=new(s(16698)).AsyncLocalStorage;var l=s(60606);let c=async()=>{var e,t;let s;try{let e=await (0,i.TG)(),t=(0,a._b)(e,r.AA.Headers.ClerkRequestData);s=(0,l.Kk)(t)}catch(e){if(e&&(0,i.Sz)(e))throw e}let c=null!=(t=null==(e=o.getStore())?void 0:e.get("requestData"))?t:s;return(null==c?void 0:c.secretKey)||(null==c?void 0:c.publishableKey)?(0,n.n)(c):(0,n.n)({})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},26790:(e,t,s)=>{"use strict";s.d(t,{z:()=>T});var r,i,n,a,o,l,c,u,d,h,p,S,g,f,k,y,m,w,v,b=s(45940);s(92867);var x=s(37081);s(27322),s(6264);var R=s(49530),K=s(57136),q=s(94051),E=class{constructor(){(0,q.VK)(this,n),(0,q.VK)(this,r,"clerk_telemetry_throttler"),(0,q.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,q.S7)(this,n,l))return!1;let t=Date.now(),s=(0,q.jq)(this,n,a).call(this,e),c=(0,q.S7)(this,n,o)?.[s];if(!c){let e={...(0,q.S7)(this,n,o),[s]:t};localStorage.setItem((0,q.S7)(this,r),JSON.stringify(e))}if(c&&t-c>(0,q.S7)(this,i)){let e=(0,q.S7)(this,n,o);delete e[s],localStorage.setItem((0,q.S7)(this,r),JSON.stringify(e))}return!!c}};r=new WeakMap,i=new WeakMap,n=new WeakSet,a=function(e){let{sk:t,pk:s,payload:r,...i}=e,n={...r,...i};return JSON.stringify(Object.keys({...r,...i}).sort().map(e=>n[e]))},o=function(){let e=localStorage.getItem((0,q.S7)(this,r));return e?JSON.parse(e):{}},l=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,q.S7)(this,r)),!1}};var j={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},V=class{constructor(e){(0,q.VK)(this,S),(0,q.VK)(this,c),(0,q.VK)(this,u),(0,q.VK)(this,d,{}),(0,q.VK)(this,h,[]),(0,q.VK)(this,p),(0,q.OV)(this,c,{maxBufferSize:e.maxBufferSize??j.maxBufferSize,samplingRate:e.samplingRate??j.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:j.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,q.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,q.S7)(this,d).clerkVersion="",(0,q.S7)(this,d).sdk=e.sdk,(0,q.S7)(this,d).sdkVersion=e.sdkVersion,(0,q.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,K.q5)(e.publishableKey);t&&((0,q.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,q.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,q.OV)(this,u,new E)}get isEnabled(){return!("development"!==(0,q.S7)(this,d).instanceType||(0,q.S7)(this,c).disabled||"undefined"!=typeof process&&(0,R.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,q.S7)(this,c).debug||"undefined"!=typeof process&&(0,R.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,q.jq)(this,S,v).call(this,e.event,e.payload);(0,q.jq)(this,S,m).call(this,t.event,t),(0,q.jq)(this,S,g).call(this,t,e.eventSamplingRate)&&((0,q.S7)(this,h).push(t),(0,q.jq)(this,S,k).call(this))}};c=new WeakMap,u=new WeakMap,d=new WeakMap,h=new WeakMap,p=new WeakMap,S=new WeakSet,g=function(e,t){return this.isEnabled&&!this.isDebug&&(0,q.jq)(this,S,f).call(this,e,t)},f=function(e,t){let s=Math.random();return!!(s<=(0,q.S7)(this,c).samplingRate&&(void 0===t||s<=t))&&!(0,q.S7)(this,u).isEventThrottled(e)},k=function(){if("undefined"==typeof window)return void(0,q.jq)(this,S,y).call(this);if((0,q.S7)(this,h).length>=(0,q.S7)(this,c).maxBufferSize){(0,q.S7)(this,p)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,q.S7)(this,p)),(0,q.jq)(this,S,y).call(this);return}(0,q.S7)(this,p)||("requestIdleCallback"in window?(0,q.OV)(this,p,requestIdleCallback(()=>{(0,q.jq)(this,S,y).call(this)})):(0,q.OV)(this,p,setTimeout(()=>{(0,q.jq)(this,S,y).call(this)},0)))},y=function(){fetch(new URL("/v1/event",(0,q.S7)(this,c).endpoint),{method:"POST",body:JSON.stringify({events:(0,q.S7)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,q.OV)(this,h,[])}).catch(()=>void 0)},m=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},w=function(){let e={name:(0,q.S7)(this,d).sdk,version:(0,q.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},v=function(e,t){let s=(0,q.jq)(this,S,w).call(this);return{event:e,cv:(0,q.S7)(this,d).clerkVersion??"",it:(0,q.S7)(this,d).instanceType??"",sdk:s.name,sdkv:s.version,...(0,q.S7)(this,d).publishableKey?{pk:(0,q.S7)(this,d).publishableKey}:{},...(0,q.S7)(this,d).secretKey?{sk:(0,q.S7)(this,d).secretKey}:{},payload:t}};function T(e){let t={...e},s=(0,b.y3)(t),r=(0,b.Bs)({options:t,apiClient:s}),i=new V({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...r,telemetry:i}}(0,x.C)(b.nr)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73999:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>k,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>h});var i=s(26142),n=s(94327),a=s(34862),o=s(37838),l=s(12901),c=s(26239),u=s(25);let d=u.z.object({state:u.z.string().min(1),auth_redirect:u.z.string().url()});async function h(e){try{let{searchParams:t}=new URL(e.url),s=t.get("state"),r=t.get("auth_redirect"),i=d.safeParse({state:s,auth_redirect:r});if(!i.success)return c.NextResponse.json({error:"Invalid parameters",details:i.error.errors},{status:400});let{state:n,auth_redirect:a}=i.data,{userId:u}=await (0,o.j)();if(u){let e=await (0,l.$)(),t=await e.signInTokens.createSignInToken({userId:u,expiresInSeconds:300}),s=new URL(a);return s.searchParams.set("ticket",t.token),s.searchParams.set("state",n),c.NextResponse.redirect(s.toString())}let h=new URL("/sign-in",e.url),p=new URL("/api/extension/sign-in",e.url);return p.searchParams.set("state",n),p.searchParams.set("auth_redirect",a),h.searchParams.set("redirect_url",p.toString()),c.NextResponse.redirect(h.toString())}catch(e){return console.error("Extension sign-in error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/sign-in/route",pathname:"/api/extension/sign-in",filename:"route",bundlePath:"app/api/extension/sign-in/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\sign-in\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:S,workUnitAsyncStorage:g,serverHooks:f}=p;function k(){return(0,a.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:g})}},76315:(e,t,s)=>{"use strict";s.d(t,{n:()=>a});var r=s(26790),i=s(54726);let n={secretKey:i.rB,publishableKey:i.At,apiUrl:i.H$,apiVersion:i.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i.Rg,domain:i.V2,isSatellite:i.fS,sdkMetadata:i.tm,telemetry:{disabled:i.nN,debug:i.Mh}},a=e=>(0,r.z)({...n,...e})},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},89259:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838],()=>s(73999));module.exports=r})();