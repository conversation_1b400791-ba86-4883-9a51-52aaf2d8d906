(()=>{var e={};e.id=2082,e.ids=[2082],e.modules={1359:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});var r=s(12901),i=s(37838);async function n(){s(1447);let{userId:e}=await (0,i.j)();return e?(await (0,r.$)()).users.getUser(e):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},12901:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var r=s(8741),i=s(23056),n=s(76315),a=s(97495);let o=new(s(16698)).AsyncLocalStorage;var u=s(60606);let c=async()=>{var e,t;let s;try{let e=await (0,i.TG)(),t=(0,a._b)(e,r.AA.Headers.ClerkRequestData);s=(0,u.Kk)(t)}catch(e){if(e&&(0,i.Sz)(e))throw e}let c=null!=(t=null==(e=o.getStore())?void 0:e.get("requestData"))?t:s;return(null==c?void 0:c.secretKey)||(null==c?void 0:c.publishableKey)?(0,n.n)(c):(0,n.n)({})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},26790:(e,t,s)=>{"use strict";s.d(t,{z:()=>V});var r,i,n,a,o,u,c,l,d,p,h,f,x,S,y,m,w,b,g,k=s(45940);s(92867);var v=s(37081);s(27322),s(6264);var q=s(49530),j=s(57136),K=s(94051),R=class{constructor(){(0,K.VK)(this,n),(0,K.VK)(this,r,"clerk_telemetry_throttler"),(0,K.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,K.S7)(this,n,u))return!1;let t=Date.now(),s=(0,K.jq)(this,n,a).call(this,e),c=(0,K.S7)(this,n,o)?.[s];if(!c){let e={...(0,K.S7)(this,n,o),[s]:t};localStorage.setItem((0,K.S7)(this,r),JSON.stringify(e))}if(c&&t-c>(0,K.S7)(this,i)){let e=(0,K.S7)(this,n,o);delete e[s],localStorage.setItem((0,K.S7)(this,r),JSON.stringify(e))}return!!c}};r=new WeakMap,i=new WeakMap,n=new WeakSet,a=function(e){let{sk:t,pk:s,payload:r,...i}=e,n={...r,...i};return JSON.stringify(Object.keys({...r,...i}).sort().map(e=>n[e]))},o=function(){let e=localStorage.getItem((0,K.S7)(this,r));return e?JSON.parse(e):{}},u=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,K.S7)(this,r)),!1}};var E={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},A=class{constructor(e){(0,K.VK)(this,f),(0,K.VK)(this,c),(0,K.VK)(this,l),(0,K.VK)(this,d,{}),(0,K.VK)(this,p,[]),(0,K.VK)(this,h),(0,K.OV)(this,c,{maxBufferSize:e.maxBufferSize??E.maxBufferSize,samplingRate:e.samplingRate??E.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:E.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,K.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,K.S7)(this,d).clerkVersion="",(0,K.S7)(this,d).sdk=e.sdk,(0,K.S7)(this,d).sdkVersion=e.sdkVersion,(0,K.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,j.q5)(e.publishableKey);t&&((0,K.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,K.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,K.OV)(this,l,new R)}get isEnabled(){return!("development"!==(0,K.S7)(this,d).instanceType||(0,K.S7)(this,c).disabled||"undefined"!=typeof process&&(0,q.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,K.S7)(this,c).debug||"undefined"!=typeof process&&(0,q.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,K.jq)(this,f,g).call(this,e.event,e.payload);(0,K.jq)(this,f,w).call(this,t.event,t),(0,K.jq)(this,f,x).call(this,t,e.eventSamplingRate)&&((0,K.S7)(this,p).push(t),(0,K.jq)(this,f,y).call(this))}};c=new WeakMap,l=new WeakMap,d=new WeakMap,p=new WeakMap,h=new WeakMap,f=new WeakSet,x=function(e,t){return this.isEnabled&&!this.isDebug&&(0,K.jq)(this,f,S).call(this,e,t)},S=function(e,t){let s=Math.random();return!!(s<=(0,K.S7)(this,c).samplingRate&&(void 0===t||s<=t))&&!(0,K.S7)(this,l).isEventThrottled(e)},y=function(){if("undefined"==typeof window)return void(0,K.jq)(this,f,m).call(this);if((0,K.S7)(this,p).length>=(0,K.S7)(this,c).maxBufferSize){(0,K.S7)(this,h)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,K.S7)(this,h)),(0,K.jq)(this,f,m).call(this);return}(0,K.S7)(this,h)||("requestIdleCallback"in window?(0,K.OV)(this,h,requestIdleCallback(()=>{(0,K.jq)(this,f,m).call(this)})):(0,K.OV)(this,h,setTimeout(()=>{(0,K.jq)(this,f,m).call(this)},0)))},m=function(){fetch(new URL("/v1/event",(0,K.S7)(this,c).endpoint),{method:"POST",body:JSON.stringify({events:(0,K.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,K.OV)(this,p,[])}).catch(()=>void 0)},w=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},b=function(){let e={name:(0,K.S7)(this,d).sdk,version:(0,K.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},g=function(e,t){let s=(0,K.jq)(this,f,b).call(this);return{event:e,cv:(0,K.S7)(this,d).clerkVersion??"",it:(0,K.S7)(this,d).instanceType??"",sdk:s.name,sdkv:s.version,...(0,K.S7)(this,d).publishableKey?{pk:(0,K.S7)(this,d).publishableKey}:{},...(0,K.S7)(this,d).secretKey?{sk:(0,K.S7)(this,d).secretKey}:{},payload:t}};function V(e){let t={...e},s=(0,k.y3)(t),r=(0,k.Bs)({options:t,apiClient:s}),i=new A({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...r,telemetry:i}}(0,v.C)(k.nr)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76315:(e,t,s)=>{"use strict";s.d(t,{n:()=>a});var r=s(26790),i=s(54726);let n={secretKey:i.rB,publishableKey:i.At,apiUrl:i.H$,apiVersion:i.mG,userAgent:"@clerk/nextjs@6.20.0",proxyUrl:i.Rg,domain:i.V2,isSatellite:i.fS,sdkMetadata:i.tm,telemetry:{disabled:i.nN,debug:i.Mh}},a=e=>(0,r.z)({...n,...e})},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},93786:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>b,routeModule:()=>S,serverHooks:()=>w,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>m});var r={};s.r(r),s.d(r,{DELETE:()=>x,POST:()=>f});var i=s(26142),n=s(94327),a=s(34862),o=s(37838),u=s(1359),c=s(18815),l=s(26239),d=s(25),p=s(55511);let h=d.z.object({sessionId:d.z.string(),extensionVersion:d.z.string(),platform:d.z.string().optional()});async function f(e){try{let{userId:t}=await (0,o.j)(),s=await (0,u.N)();if(!t||!s)return l.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),{sessionId:i,extensionVersion:n,platform:a}=h.parse(r),d=await c.database.user.findUnique({where:{clerkId:t}});if(d||(d=await c.database.user.create({data:{clerkId:t,email:s.emailAddresses[0]?.emailAddress||"",name:`${s.firstName||""} ${s.lastName||""}`.trim()||null,picture:s.imageUrl}})),!d.termsAccepted)return l.NextResponse.json({error:"Terms not accepted",requiresTermsAcceptance:!0,redirectUrl:"/terms"},{status:403});let f=d.extensionApiKey;return f||(f=`cubent_${(0,p.randomBytes)(32).toString("hex")}`,await c.database.user.update({where:{id:d.id},data:{extensionApiKey:f}})),await c.database.extensionSession.upsert({where:{userId_sessionId:{userId:d.id,sessionId:i}},update:{isActive:!0,lastActiveAt:new Date},create:{userId:d.id,sessionId:i,isActive:!0,lastActiveAt:new Date}}),await c.database.user.update({where:{id:d.id},data:{lastExtensionSync:new Date}}),l.NextResponse.json({success:!0,message:"Extension connected successfully",apiKey:f,user:{id:d.id,name:d.name,email:d.email,subscriptionTier:d.subscriptionTier,subscriptionStatus:d.subscriptionStatus},settings:{extensionSettings:d.extensionSettings||{},preferences:d.preferences||{}}})}catch(e){return console.error("Extension connect error:",e),l.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){try{let{userId:t}=await (0,o.j)();if(!t)return l.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),r=s.get("sessionId"),i=await c.database.user.findUnique({where:{clerkId:t}});if(!i)return l.NextResponse.json({error:"User not found"},{status:404});return r?await c.database.extensionSession.updateMany({where:{userId:i.id,sessionId:r},data:{isActive:!1}}):await c.database.extensionSession.updateMany({where:{userId:i.id},data:{isActive:!1}}),l.NextResponse.json({success:!0,message:"Extension disconnected successfully"})}catch(e){return console.error("Extension disconnect error:",e),l.NextResponse.json({error:"Internal server error"},{status:500})}}let S=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extension/connect/route",pathname:"/api/extension/connect",filename:"route",bundlePath:"app/api/extension/connect/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\connect\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:y,workUnitAsyncStorage:m,serverHooks:w}=S;function b(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:m})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>s(93786));module.exports=r})();