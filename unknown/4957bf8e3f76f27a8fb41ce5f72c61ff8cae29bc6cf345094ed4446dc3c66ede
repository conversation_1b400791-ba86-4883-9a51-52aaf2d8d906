(()=>{var e={};e.id=2428,e.ids=[2428],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,t,s)=>{"use strict";e.exports=s(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63102:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>k,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>q});var r={};s.r(r),s.d(r,{GET:()=>d});var o=s(26142),a=s(94327),n=s(34862),i=s(37838),u=s(18815),c=s(26239);async function d(e){try{var t,s,r,o,a,n,d;let g,{userId:x}=await (0,i.j)();if(!x)return c.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:q}=new URL(e.url),h=q.get("period")||"30d",k=q.get("groupBy")||"day",m=await u.database.user.findUnique({where:{clerkId:x}});if(!m)return c.NextResponse.json({error:"User not found"},{status:404});let f=new Date;switch(h){case"1d":g=new Date(f.getTime()-864e5);break;case"7d":g=new Date(f.getTime()-6048e5);break;case"30d":default:g=new Date(f.getTime()-2592e6);break;case"90d":g=new Date(f.getTime()-7776e6);break;case"1y":g=new Date(f.getTime()-31536e6)}let y=await u.database.usageMetrics.findMany({where:{userId:m.id,date:{gte:g}},orderBy:{date:"asc"}}),A=await u.database.extensionSession.findMany({where:{userId:m.id,createdAt:{gte:g}},orderBy:{createdAt:"asc"}}),U=y.reduce((e,t)=>({tokensUsed:e.tokensUsed+t.tokensUsed,requestsMade:e.requestsMade+t.requestsMade,costAccrued:e.costAccrued+t.costAccrued}),{tokensUsed:0,requestsMade:0,costAccrued:0}),w="day"===k?p(y,g,f):"week"===k?(t=y,s=g,r=f,p(t,s,r)):(o=y,a=g,n=f,p(o,a,n)),v={totalSessions:A.length,activeSessions:A.filter(e=>e.isActive).length,averageSessionDuration:(d=A,0===d.length?0:Math.round(d.reduce((e,t)=>t.lastActiveAt&&t.createdAt?e+(t.lastActiveAt.getTime()-t.createdAt.getTime()):e,0)/d.length/6e4)),mostUsedVersion:function(e){let t={};return e.forEach(e=>{e.extensionVersion&&(t[e.extensionVersion]=(t[e.extensionVersion]||0)+1)}),Object.entries(t).sort(([,e],[,t])=>t-e)[0]?.[0]||"Unknown"}(A),platformDistribution:function(e){let t={};return e.forEach(e=>{e.platform&&(t[e.platform]=(t[e.platform]||0)+1)}),t}(A)},M=new Date(g.getTime()-(f.getTime()-g.getTime())),b=(await u.database.usageMetrics.findMany({where:{userId:m.id,date:{gte:M,lt:g}}})).reduce((e,t)=>({tokensUsed:e.tokensUsed+t.tokensUsed,requestsMade:e.requestsMade+t.requestsMade,costAccrued:e.costAccrued+t.costAccrued}),{tokensUsed:0,requestsMade:0,costAccrued:0}),T={tokensUsed:l(U.tokensUsed,b.tokensUsed),requestsMade:l(U.requestsMade,b.requestsMade),costAccrued:l(U.costAccrued,b.costAccrued)},D=y.sort((e,t)=>t.tokensUsed-e.tokensUsed).slice(0,5).map(e=>({date:e.date,tokensUsed:e.tokensUsed,requestsMade:e.requestsMade,costAccrued:e.costAccrued})),j={period:h,dateRange:{start:g,end:f},totals:U,trends:T,timeSeries:w,sessions:v,topUsageDays:D,insights:function(e,t,s){let r=[];return s.tokensUsed>20?r.push({type:"positive",message:`Token usage increased by ${s.tokensUsed}% compared to the previous period`}):s.tokensUsed<-20&&r.push({type:"neutral",message:`Token usage decreased by ${Math.abs(s.tokensUsed)}% compared to the previous period`}),t.averageSessionDuration>60&&r.push({type:"positive",message:`Long average session duration (${t.averageSessionDuration} minutes) indicates high engagement`}),e.costAccrued>10&&r.push({type:"warning",message:`High cost accrued ($${e.costAccrued.toFixed(2)}). Consider optimizing usage or upgrading plan`}),r}(U,v,T)};return c.NextResponse.json(j)}catch(e){return console.error("Analytics fetch error:",e),c.NextResponse.json({error:"Internal server error"},{status:500})}}function p(e,t,s){let r={},o=new Date(t);for(;o<=s;){let e=o.toISOString().split("T")[0];r[e]={date:e,tokensUsed:0,requestsMade:0,costAccrued:0},o.setDate(o.getDate()+1)}return e.forEach(e=>{let t=e.date.toISOString().split("T")[0];r[t]&&(r[t].tokensUsed+=e.tokensUsed,r[t].requestsMade+=e.requestsMade,r[t].costAccrued+=e.costAccrued)}),Object.values(r)}function l(e,t){return 0===t?100*(e>0):Math.round((e-t)/t*100)}let g=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/extension/analytics/route",pathname:"/api/extension/analytics",filename:"route",bundlePath:"app/api/extension/analytics/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\analytics\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:x,workUnitAsyncStorage:q,serverHooks:h}=g;function k(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:q})}},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>s(63102));module.exports=r})();