(()=>{var e={};e.id=5409,e.ids=[5409],e.modules={1378:(e,r,t)=>{Promise.resolve().then(t.bind(t,86332))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},8963:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=8963,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,r,t)=>{"use strict";e.exports=t(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},32599:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{GET:()=>g});var i=t(26142),o=t(94327),n=t(34862),u=t(18815),a=t(74433),p=t(76741),c=t(26239),d=t(25);let x=d.z.object({device_id:d.z.string().min(1,"Device ID is required"),state:d.z.string().min(1,"State parameter is required")}),l=new Map,q=e=>{let r=Date.now(),t=l.get(e);return!t||r>t.resetTime?(l.set(e,{count:1,resetTime:r+6e4}),!0):!(t.count>=10)&&(t.count++,!0)},g=async e=>{try{let r=e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown";if(!q(r))return p.R.warn("Rate limit exceeded for token endpoint",{ip:r}),c.NextResponse.json({error:"Rate limit exceeded",message:"Too many requests"},{status:429});let{searchParams:t}=new URL(e.url),s={device_id:t.get("device_id"),state:t.get("state")},{device_id:i,state:o}=x.parse(s);await u.database.pendingLogin.deleteMany({where:{expiresAt:{lt:new Date}}});let n=await u.database.pendingLogin.findFirst({where:{deviceId:i,state:o,expiresAt:{gt:new Date}}});if(!n)return p.R.info("Token not found or expired",{deviceId:i.slice(0,8)+"...",state:o.slice(0,8)+"...",ip:r}),c.NextResponse.json({error:"Not found",message:"Token not found or expired"},{status:404});return await u.database.pendingLogin.update({where:{id:n.id},data:{expiresAt:new Date(Date.now()+3e5)}}),p.R.info("Token retrieved successfully",{deviceId:i.slice(0,8)+"...",state:o.slice(0,8)+"...",ip:r}),c.NextResponse.json({token:n.token,success:!0})}catch(r){let e=(0,a.u)(r);if(p.R.error("Token retrieval failed",{error:e}),r instanceof d.z.ZodError)return c.NextResponse.json({error:"Validation error",message:r.errors[0]?.message||"Invalid parameters"},{status:400});return c.NextResponse.json({error:"Internal server error",message:"Failed to retrieve token"},{status:500})}},m=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/token/route",pathname:"/api/token",filename:"route",bundlePath:"app/api/token/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\token\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:v,serverHooks:f}=m;function w(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:v})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67098:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,21034,23))},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74433:(e,r,t)=>{"use strict";t.d(r,{u:()=>o});var s=t(4520),i=t(76741);let o=e=>{let r="An error occurred";r=e instanceof Error||e&&"object"==typeof e&&"message"in e?e.message:String(e);try{(0,s.captureException)(e),i.R.error(`Parsing error: ${r}`)}catch(e){console.error("Error parsing error:",e)}return r}},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76741:(e,r,t)=>{"use strict";t.d(r,{R:()=>s});let s=t(42870).log},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2923,25,5480,3319,864],()=>t(32599));module.exports=s})();