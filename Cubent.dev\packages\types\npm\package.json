{"name": "@qapt-coder/types", "version": "1.24.0", "description": "TypeScript type definitions for qapt coder.", "publishConfig": {"access": "public", "name": "@qapt-coder/types"}, "author": "qapt coder Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/qaptcoder/qapt-coder.git"}, "bugs": {"url": "https://github.com/qaptcoder/qapt-coder/issues"}, "homepage": "https://github.com/qaptcoder/qapt-coder/tree/main/packages/types", "keywords": ["qapt", "qapt-coder", "ai"], "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "files": ["dist"]}