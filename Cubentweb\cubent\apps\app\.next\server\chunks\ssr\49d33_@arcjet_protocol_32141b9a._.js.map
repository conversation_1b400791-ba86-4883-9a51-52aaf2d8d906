{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40arcjet%2Bprotocol%401.0.0-beta.7/node_modules/%40arcjet/protocol/proto/decide/v1alpha1/decide_pb.js"], "sourcesContent": ["// @generated by protoc-gen-es v1.10.0\n// @generated from file proto/decide/v1alpha1/decide.proto (package proto.decide.v1alpha1, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { proto3, Timestamp } from \"@bufbuild/protobuf\";\n\n/**\n * Represents whether we think the client is a bot or not. This should be used\n * alongside the bot score which represents the level of certainty of our\n * detection.\n *\n * @generated from enum proto.decide.v1alpha1.BotType\n */\nexport const BotType = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.BotType\",\n  [\n    {no: 0, name: \"BOT_TYPE_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n    {no: 1, name: \"BOT_TYPE_NOT_ANALYZED\", localName: \"NOT_ANALYZED\"},\n    {no: 2, name: \"BOT_TYPE_AUTOMATED\", localName: \"AUTOMATED\"},\n    {no: 3, name: \"BOT_TYPE_LIKELY_AUTOMATED\", localName: \"LIKELY_AUTOMATED\"},\n    {no: 4, name: \"BOT_TYPE_LIKELY_NOT_A_BOT\", localName: \"LIKELY_NOT_A_BOT\"},\n    {no: 5, name: \"BOT_TYPE_VERIFIED_BOT\", localName: \"VERIFIED_BOT\"},\n  ],\n);\n\n/**\n * Represents the type of email address submitted.\n *\n * @generated from enum proto.decide.v1alpha1.EmailType\n */\nexport const EmailType = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.EmailType\",\n  [\n    {no: 0, name: \"EMAIL_TYPE_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n    {no: 1, name: \"EMAIL_TYPE_DISPOSABLE\", localName: \"DISPOSABLE\"},\n    {no: 2, name: \"EMAIL_TYPE_FREE\", localName: \"FREE\"},\n    {no: 3, name: \"EMAIL_TYPE_NO_MX_RECORDS\", localName: \"NO_MX_RECORDS\"},\n    {no: 4, name: \"EMAIL_TYPE_NO_GRAVATAR\", localName: \"NO_GRAVATAR\"},\n    {no: 5, name: \"EMAIL_TYPE_INVALID\", localName: \"INVALID\"},\n  ],\n);\n\n/**\n * The mode to run in. This can be either `DRY_RUN` or `LIVE`. In `DRY_RUN`\n * mode, all requests will be allowed and you can review what the action would\n * have been from your dashboard. In `LIVE` mode, requests will be allowed,\n * challenged or blocked based on the returned decision.\n *\n * @generated from enum proto.decide.v1alpha1.Mode\n */\nexport const Mode = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.Mode\",\n  [\n    {no: 0, name: \"MODE_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n    {no: 1, name: \"MODE_DRY_RUN\", localName: \"DRY_RUN\"},\n    {no: 2, name: \"MODE_LIVE\", localName: \"LIVE\"},\n  ],\n);\n\n/**\n * @generated from enum proto.decide.v1alpha1.RuleState\n */\nexport const RuleState = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.RuleState\",\n  [\n    {no: 0, name: \"RULE_STATE_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n    {no: 1, name: \"RULE_STATE_RUN\", localName: \"RUN\"},\n    {no: 2, name: \"RULE_STATE_NOT_RUN\", localName: \"NOT_RUN\"},\n    {no: 3, name: \"RULE_STATE_DRY_RUN\", localName: \"DRY_RUN\"},\n    {no: 4, name: \"RULE_STATE_CACHED\", localName: \"CACHED\"},\n  ],\n);\n\n/**\n * The conclusion for the request based on the Arcjet analysis and any specific\n * configuration.\n *\n * @generated from enum proto.decide.v1alpha1.Conclusion\n */\nexport const Conclusion = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.Conclusion\",\n  [\n    {no: 0, name: \"CONCLUSION_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n    {no: 1, name: \"CONCLUSION_ALLOW\", localName: \"ALLOW\"},\n    {no: 2, name: \"CONCLUSION_DENY\", localName: \"DENY\"},\n    {no: 3, name: \"CONCLUSION_CHALLENGE\", localName: \"CHALLENGE\"},\n    {no: 4, name: \"CONCLUSION_ERROR\", localName: \"ERROR\"},\n  ],\n);\n\n/**\n * The SDK used to make the request. Used for analytics and to help us improve.\n *\n * @generated from enum proto.decide.v1alpha1.SDKStack\n */\nexport const SDKStack = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.SDKStack\",\n  [\n    {no: 0, name: \"SDK_STACK_UNSPECIFIED\"},\n    {no: 1, name: \"SDK_STACK_NODEJS\"},\n    {no: 2, name: \"SDK_STACK_NEXTJS\"},\n    {no: 3, name: \"SDK_STACK_PYTHON\"},\n    {no: 4, name: \"SDK_STACK_DJANGO\"},\n    {no: 5, name: \"SDK_STACK_BUN\"},\n    {no: 6, name: \"SDK_STACK_DENO\"},\n    {no: 7, name: \"SDK_STACK_SVELTEKIT\"},\n    {no: 8, name: \"SDK_STACK_HONO\"},\n    {no: 9, name: \"SDK_STACK_NUXT\"},\n    {no: 10, name: \"SDK_STACK_NESTJS\"},\n    {no: 11, name: \"SDK_STACK_REMIX\"},\n    {no: 12, name: \"SDK_STACK_ASTRO\"},\n  ],\n);\n\n/**\n * @generated from enum proto.decide.v1alpha1.RateLimitAlgorithm\n */\nexport const RateLimitAlgorithm = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.RateLimitAlgorithm\",\n  [\n    {no: 0, name: \"RATE_LIMIT_ALGORITHM_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n    {no: 1, name: \"RATE_LIMIT_ALGORITHM_TOKEN_BUCKET\", localName: \"TOKEN_BUCKET\"},\n    {no: 2, name: \"RATE_LIMIT_ALGORITHM_FIXED_WINDOW\", localName: \"FIXED_WINDOW\"},\n    {no: 3, name: \"RATE_LIMIT_ALGORITHM_SLIDING_WINDOW\", localName: \"SLIDING_WINDOW\"},\n  ],\n);\n\n/**\n * @generated from enum proto.decide.v1alpha1.RateLimitRuleVersion\n */\nexport const RateLimitRuleVersion = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.RateLimitRuleVersion\",\n  [\n    {no: 0, name: \"RATE_LIMIT_RULE_VERSION_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n  ],\n);\n\n/**\n * @generated from enum proto.decide.v1alpha1.BotV2RuleVersion\n */\nexport const BotV2RuleVersion = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.BotV2RuleVersion\",\n  [\n    {no: 0, name: \"BOT_V2_RULE_VERSION_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n  ],\n);\n\n/**\n * @generated from enum proto.decide.v1alpha1.EmailRuleVersion\n */\nexport const EmailRuleVersion = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.EmailRuleVersion\",\n  [\n    {no: 0, name: \"EMAIL_RULE_VERSION_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n  ],\n);\n\n/**\n * @generated from enum proto.decide.v1alpha1.SensitiveInfoRuleVersion\n */\nexport const SensitiveInfoRuleVersion = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.SensitiveInfoRuleVersion\",\n  [\n    {no: 0, name: \"SENSITIVE_INFO_RULE_VERSION_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n  ],\n);\n\n/**\n * @generated from enum proto.decide.v1alpha1.ShieldRuleVersion\n */\nexport const ShieldRuleVersion = /*@__PURE__*/ proto3.makeEnum(\n  \"proto.decide.v1alpha1.ShieldRuleVersion\",\n  [\n    {no: 0, name: \"SHIELD_RULE_VERSION_UNSPECIFIED\", localName: \"UNSPECIFIED\"},\n  ],\n);\n\n/**\n * Additional information from Arcjet about the IP address associated with a\n * request.\n *\n * @generated from message proto.decide.v1alpha1.IpDetails\n */\nexport const IpDetails = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.IpDetails\",\n  () => [\n    { no: 1, name: \"latitude\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 2, name: \"longitude\", kind: \"scalar\", T: 1 /* ScalarType.DOUBLE */ },\n    { no: 3, name: \"accuracy_radius\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 4, name: \"timezone\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"postal_code\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"city\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"region\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 8, name: \"country\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 9, name: \"country_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"continent\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 11, name: \"continent_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 12, name: \"asn\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 13, name: \"asn_name\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 14, name: \"asn_domain\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 15, name: \"asn_type\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 16, name: \"asn_country\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 17, name: \"service\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 18, name: \"is_hosting\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 19, name: \"is_vpn\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 20, name: \"is_proxy\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 21, name: \"is_tor\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 22, name: \"is_relay\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * The reason for the decision. This is populated based on the selected rules\n * for deny or challenge responses. Additional details can be found in the field\n * and by logging into the Arcjet dashboard and searching for the decision ID.\n *\n * @generated from message proto.decide.v1alpha1.Reason\n */\nexport const Reason = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.Reason\",\n  () => [\n    { no: 1, name: \"rate_limit\", kind: \"message\", T: RateLimitReason, oneof: \"reason\" },\n    { no: 2, name: \"edge_rule\", kind: \"message\", T: EdgeRuleReason, oneof: \"reason\" },\n    { no: 3, name: \"bot\", kind: \"message\", T: BotReason, oneof: \"reason\" },\n    { no: 4, name: \"shield\", kind: \"message\", T: ShieldReason, oneof: \"reason\" },\n    { no: 5, name: \"email\", kind: \"message\", T: EmailReason, oneof: \"reason\" },\n    { no: 6, name: \"error\", kind: \"message\", T: ErrorReason, oneof: \"reason\" },\n    { no: 7, name: \"sensitive_info\", kind: \"message\", T: SensitiveInfoReason, oneof: \"reason\" },\n    { no: 8, name: \"bot_v2\", kind: \"message\", T: BotV2Reason, oneof: \"reason\" },\n  ],\n);\n\n/**\n * Details of a rate limit decision.\n *\n * @generated from message proto.decide.v1alpha1.RateLimitReason\n */\nexport const RateLimitReason = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.RateLimitReason\",\n  () => [\n    { no: 1, name: \"max\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 2, name: \"count\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 3, name: \"remaining\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 4, name: \"reset_time\", kind: \"message\", T: Timestamp },\n    { no: 5, name: \"reset_in_seconds\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 6, name: \"window_in_seconds\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * Details of an edge rule decision. Unimplemented.\n *\n * @generated from message proto.decide.v1alpha1.EdgeRuleReason\n */\nexport const EdgeRuleReason = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.EdgeRuleReason\",\n  [],\n);\n\n/**\n * Details of a bot decision.\n *\n * @generated from message proto.decide.v1alpha1.BotReason\n */\nexport const BotReason = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.BotReason\",\n  () => [\n    { no: 1, name: \"bot_type\", kind: \"enum\", T: proto3.getEnumType(BotType) },\n    { no: 2, name: \"bot_score\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n    { no: 3, name: \"user_agent_match\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 5, name: \"ip_hosting\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 6, name: \"ip_vpn\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 7, name: \"ip_proxy\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 8, name: \"ip_tor\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 9, name: \"ip_relay\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * Details of a bot (v2) decision.\n *\n * @generated from message proto.decide.v1alpha1.BotV2Reason\n */\nexport const BotV2Reason = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.BotV2Reason\",\n  () => [\n    { no: 1, name: \"allowed\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 2, name: \"denied\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 3, name: \"verified\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 4, name: \"spoofed\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * Details of an Arcjet Shield decision.\n *\n * @generated from message proto.decide.v1alpha1.ShieldReason\n */\nexport const ShieldReason = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.ShieldReason\",\n  () => [\n    { no: 1, name: \"shield_triggered\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 2, name: \"suspicious\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n  ],\n);\n\n/**\n * Details of an email decision.\n *\n * @generated from message proto.decide.v1alpha1.EmailReason\n */\nexport const EmailReason = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.EmailReason\",\n  () => [\n    { no: 1, name: \"email_types\", kind: \"enum\", T: proto3.getEnumType(EmailType), repeated: true },\n  ],\n);\n\n/**\n * Details of an error decision.\n *\n * @generated from message proto.decide.v1alpha1.ErrorReason\n */\nexport const ErrorReason = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.ErrorReason\",\n  () => [\n    { no: 1, name: \"message\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * @generated from message proto.decide.v1alpha1.IdentifiedEntity\n */\nexport const IdentifiedEntity = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.IdentifiedEntity\",\n  () => [\n    { no: 1, name: \"identified_type\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"start\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 3, name: \"end\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * Details of a sensitive info reason.\n *\n * @generated from message proto.decide.v1alpha1.SensitiveInfoReason\n */\nexport const SensitiveInfoReason = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.SensitiveInfoReason\",\n  () => [\n    { no: 1, name: \"allowed\", kind: \"message\", T: IdentifiedEntity, repeated: true },\n    { no: 2, name: \"denied\", kind: \"message\", T: IdentifiedEntity, repeated: true },\n  ],\n);\n\n/**\n * The configuration for a rate limit rule.\n *\n * @generated from message proto.decide.v1alpha1.RateLimitRule\n */\nexport const RateLimitRule = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.RateLimitRule\",\n  () => [\n    { no: 1, name: \"mode\", kind: \"enum\", T: proto3.getEnumType(Mode) },\n    { no: 2, name: \"match\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"characteristics\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"window\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"max\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 6, name: \"timeout\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 7, name: \"algorithm\", kind: \"enum\", T: proto3.getEnumType(RateLimitAlgorithm) },\n    { no: 8, name: \"refill_rate\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 9, name: \"interval\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 10, name: \"capacity\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 12, name: \"window_in_seconds\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 13, name: \"version\", kind: \"enum\", T: proto3.getEnumType(RateLimitRuleVersion) },\n  ],\n);\n\n/**\n * The configuration for a bot rule.\n *\n * @generated from message proto.decide.v1alpha1.BotRule\n */\nexport const BotRule = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.BotRule\",\n  () => [\n    { no: 1, name: \"mode\", kind: \"enum\", T: proto3.getEnumType(Mode) },\n    { no: 2, name: \"block\", kind: \"enum\", T: proto3.getEnumType(BotType), repeated: true },\n    { no: 3, name: \"patterns\", kind: \"message\", T: BotRule_Patterns },\n  ],\n);\n\n/**\n * @generated from message proto.decide.v1alpha1.BotRule.Patterns\n */\nexport const BotRule_Patterns = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.BotRule.Patterns\",\n  () => [\n    { no: 1, name: \"add\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"enum\", T: proto3.getEnumType(BotType)} },\n    { no: 2, name: \"remove\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n  {localName: \"BotRule_Patterns\"},\n);\n\n/**\n * The configuration for a bot (v2) rule.\n *\n * @generated from message proto.decide.v1alpha1.BotV2Rule\n */\nexport const BotV2Rule = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.BotV2Rule\",\n  () => [\n    { no: 1, name: \"mode\", kind: \"enum\", T: proto3.getEnumType(Mode) },\n    { no: 2, name: \"allow\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 3, name: \"deny\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"version\", kind: \"enum\", T: proto3.getEnumType(BotV2RuleVersion) },\n  ],\n);\n\n/**\n * The configuration for an email rule.\n *\n * @generated from message proto.decide.v1alpha1.EmailRule\n */\nexport const EmailRule = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.EmailRule\",\n  () => [\n    { no: 1, name: \"mode\", kind: \"enum\", T: proto3.getEnumType(Mode) },\n    { no: 2, name: \"block\", kind: \"enum\", T: proto3.getEnumType(EmailType), repeated: true },\n    { no: 3, name: \"require_top_level_domain\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 4, name: \"allow_domain_literal\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 5, name: \"allow\", kind: \"enum\", T: proto3.getEnumType(EmailType), repeated: true },\n    { no: 6, name: \"deny\", kind: \"enum\", T: proto3.getEnumType(EmailType), repeated: true },\n    { no: 7, name: \"version\", kind: \"enum\", T: proto3.getEnumType(EmailRuleVersion) },\n  ],\n);\n\n/**\n * The configuration for a detect sensitive info rule.\n *\n * @generated from message proto.decide.v1alpha1.SensitiveInfoRule\n */\nexport const SensitiveInfoRule = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.SensitiveInfoRule\",\n  () => [\n    { no: 1, name: \"mode\", kind: \"enum\", T: proto3.getEnumType(Mode) },\n    { no: 2, name: \"allow\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 3, name: \"deny\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"version\", kind: \"enum\", T: proto3.getEnumType(SensitiveInfoRuleVersion) },\n  ],\n);\n\n/**\n * The configuration for a shield rule.\n *\n * @generated from message proto.decide.v1alpha1.ShieldRule\n */\nexport const ShieldRule = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.ShieldRule\",\n  () => [\n    { no: 1, name: \"mode\", kind: \"enum\", T: proto3.getEnumType(Mode) },\n    { no: 2, name: \"auto_added\", kind: \"scalar\", T: 8 /* ScalarType.BOOL */ },\n    { no: 3, name: \"characteristics\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n    { no: 4, name: \"version\", kind: \"enum\", T: proto3.getEnumType(ShieldRuleVersion) },\n  ],\n);\n\n/**\n * The configuration for Arcjet.\n *\n * @generated from message proto.decide.v1alpha1.Rule\n */\nexport const Rule = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.Rule\",\n  () => [\n    { no: 1, name: \"rate_limit\", kind: \"message\", T: RateLimitRule, oneof: \"rule\" },\n    { no: 2, name: \"bots\", kind: \"message\", T: BotRule, oneof: \"rule\" },\n    { no: 3, name: \"email\", kind: \"message\", T: EmailRule, oneof: \"rule\" },\n    { no: 4, name: \"shield\", kind: \"message\", T: ShieldRule, oneof: \"rule\" },\n    { no: 5, name: \"sensitive_info\", kind: \"message\", T: SensitiveInfoRule, oneof: \"rule\" },\n    { no: 6, name: \"bot_v2\", kind: \"message\", T: BotV2Rule, oneof: \"rule\" },\n  ],\n);\n\n/**\n * @generated from message proto.decide.v1alpha1.RuleResult\n */\nexport const RuleResult = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.RuleResult\",\n  () => [\n    { no: 1, name: \"rule_id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"state\", kind: \"enum\", T: proto3.getEnumType(RuleState) },\n    { no: 3, name: \"conclusion\", kind: \"enum\", T: proto3.getEnumType(Conclusion) },\n    { no: 4, name: \"reason\", kind: \"message\", T: Reason },\n    { no: 5, name: \"ttl\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n  ],\n);\n\n/**\n * Details about a request under investigation.\n *\n * @generated from message proto.decide.v1alpha1.RequestDetails\n */\nexport const RequestDetails = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.RequestDetails\",\n  () => [\n    { no: 1, name: \"ip\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"method\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 3, name: \"protocol\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"host\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 5, name: \"path\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 6, name: \"headers\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 7, name: \"body\", kind: \"scalar\", T: 12 /* ScalarType.BYTES */ },\n    { no: 8, name: \"extra\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n    { no: 9, name: \"email\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 10, name: \"cookies\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 11, name: \"query\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n  ],\n);\n\n/**\n * A decision made about the request under investigation.\n *\n * @generated from message proto.decide.v1alpha1.Decision\n */\nexport const Decision = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.Decision\",\n  () => [\n    { no: 1, name: \"id\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"conclusion\", kind: \"enum\", T: proto3.getEnumType(Conclusion) },\n    { no: 3, name: \"reason\", kind: \"message\", T: Reason },\n    { no: 4, name: \"rule_results\", kind: \"message\", T: RuleResult, repeated: true },\n    { no: 5, name: \"ttl\", kind: \"scalar\", T: 13 /* ScalarType.UINT32 */ },\n    { no: 6, name: \"ip_details\", kind: \"message\", T: IpDetails },\n  ],\n);\n\n/**\n * A request to the decide API.\n *\n * @generated from message proto.decide.v1alpha1.DecideRequest\n */\nexport const DecideRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.DecideRequest\",\n  () => [\n    { no: 1, name: \"sdk_stack\", kind: \"enum\", T: proto3.getEnumType(SDKStack) },\n    { no: 2, name: \"sdk_version\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"details\", kind: \"message\", T: RequestDetails },\n    { no: 5, name: \"rules\", kind: \"message\", T: Rule, repeated: true },\n    { no: 6, name: \"characteristics\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * A response from the decide API.\n *\n * @generated from message proto.decide.v1alpha1.DecideResponse\n */\nexport const DecideResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.DecideResponse\",\n  () => [\n    { no: 1, name: \"decision\", kind: \"message\", T: Decision },\n    { no: 2, name: \"extra\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n  ],\n);\n\n/**\n * A request to the Report RPC when SDK has already made a decision locally.\n *\n * @generated from message proto.decide.v1alpha1.ReportRequest\n */\nexport const ReportRequest = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.ReportRequest\",\n  () => [\n    { no: 1, name: \"sdk_stack\", kind: \"enum\", T: proto3.getEnumType(SDKStack) },\n    { no: 2, name: \"sdk_version\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 4, name: \"details\", kind: \"message\", T: RequestDetails },\n    { no: 5, name: \"decision\", kind: \"message\", T: Decision },\n    { no: 6, name: \"rules\", kind: \"message\", T: Rule, repeated: true },\n    { no: 8, name: \"characteristics\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n  ],\n);\n\n/**\n * A response from the Report RPC.\n *\n * @generated from message proto.decide.v1alpha1.ReportResponse\n */\nexport const ReportResponse = /*@__PURE__*/ proto3.makeMessageType(\n  \"proto.decide.v1alpha1.ReportResponse\",\n  () => [\n    { no: 2, name: \"extra\", kind: \"map\", K: 9 /* ScalarType.STRING */, V: {kind: \"scalar\", T: 9 /* ScalarType.STRING */} },\n  ],\n);\n\n"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,yGAAyG;AACzG,kBAAkB,GAClB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd;AAAA;;AASO,MAAM,UAAU,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CAClD,iCACA;IACE;QAAC,IAAI;QAAG,MAAM;QAAwB,WAAW;IAAa;IAC9D;QAAC,IAAI;QAAG,MAAM;QAAyB,WAAW;IAAc;IAChE;QAAC,IAAI;QAAG,MAAM;QAAsB,WAAW;IAAW;IAC1D;QAAC,IAAI;QAAG,MAAM;QAA6B,WAAW;IAAkB;IACxE;QAAC,IAAI;QAAG,MAAM;QAA6B,WAAW;IAAkB;IACxE;QAAC,IAAI;QAAG,MAAM;QAAyB,WAAW;IAAc;CACjE;AAQI,MAAM,YAAY,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CACpD,mCACA;IACE;QAAC,IAAI;QAAG,MAAM;QAA0B,WAAW;IAAa;IAChE;QAAC,IAAI;QAAG,MAAM;QAAyB,WAAW;IAAY;IAC9D;QAAC,IAAI;QAAG,MAAM;QAAmB,WAAW;IAAM;IAClD;QAAC,IAAI;QAAG,MAAM;QAA4B,WAAW;IAAe;IACpE;QAAC,IAAI;QAAG,MAAM;QAA0B,WAAW;IAAa;IAChE;QAAC,IAAI;QAAG,MAAM;QAAsB,WAAW;IAAS;CACzD;AAWI,MAAM,OAAO,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CAC/C,8BACA;IACE;QAAC,IAAI;QAAG,MAAM;QAAoB,WAAW;IAAa;IAC1D;QAAC,IAAI;QAAG,MAAM;QAAgB,WAAW;IAAS;IAClD;QAAC,IAAI;QAAG,MAAM;QAAa,WAAW;IAAM;CAC7C;AAMI,MAAM,YAAY,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CACpD,mCACA;IACE;QAAC,IAAI;QAAG,MAAM;QAA0B,WAAW;IAAa;IAChE;QAAC,IAAI;QAAG,MAAM;QAAkB,WAAW;IAAK;IAChD;QAAC,IAAI;QAAG,MAAM;QAAsB,WAAW;IAAS;IACxD;QAAC,IAAI;QAAG,MAAM;QAAsB,WAAW;IAAS;IACxD;QAAC,IAAI;QAAG,MAAM;QAAqB,WAAW;IAAQ;CACvD;AASI,MAAM,aAAa,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CACrD,oCACA;IACE;QAAC,IAAI;QAAG,MAAM;QAA0B,WAAW;IAAa;IAChE;QAAC,IAAI;QAAG,MAAM;QAAoB,WAAW;IAAO;IACpD;QAAC,IAAI;QAAG,MAAM;QAAmB,WAAW;IAAM;IAClD;QAAC,IAAI;QAAG,MAAM;QAAwB,WAAW;IAAW;IAC5D;QAAC,IAAI;QAAG,MAAM;QAAoB,WAAW;IAAO;CACrD;AAQI,MAAM,WAAW,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CACnD,kCACA;IACE;QAAC,IAAI;QAAG,MAAM;IAAuB;IACrC;QAAC,IAAI;QAAG,MAAM;IAAkB;IAChC;QAAC,IAAI;QAAG,MAAM;IAAkB;IAChC;QAAC,IAAI;QAAG,MAAM;IAAkB;IAChC;QAAC,IAAI;QAAG,MAAM;IAAkB;IAChC;QAAC,IAAI;QAAG,MAAM;IAAe;IAC7B;QAAC,IAAI;QAAG,MAAM;IAAgB;IAC9B;QAAC,IAAI;QAAG,MAAM;IAAqB;IACnC;QAAC,IAAI;QAAG,MAAM;IAAgB;IAC9B;QAAC,IAAI;QAAG,MAAM;IAAgB;IAC9B;QAAC,IAAI;QAAI,MAAM;IAAkB;IACjC;QAAC,IAAI;QAAI,MAAM;IAAiB;IAChC;QAAC,IAAI;QAAI,MAAM;IAAiB;CACjC;AAMI,MAAM,qBAAqB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CAC7D,4CACA;IACE;QAAC,IAAI;QAAG,MAAM;QAAoC,WAAW;IAAa;IAC1E;QAAC,IAAI;QAAG,MAAM;QAAqC,WAAW;IAAc;IAC5E;QAAC,IAAI;QAAG,MAAM;QAAqC,WAAW;IAAc;IAC5E;QAAC,IAAI;QAAG,MAAM;QAAuC,WAAW;IAAgB;CACjF;AAMI,MAAM,uBAAuB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CAC/D,8CACA;IACE;QAAC,IAAI;QAAG,MAAM;QAAuC,WAAW;IAAa;CAC9E;AAMI,MAAM,mBAAmB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CAC3D,0CACA;IACE;QAAC,IAAI;QAAG,MAAM;QAAmC,WAAW;IAAa;CAC1E;AAMI,MAAM,mBAAmB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CAC3D,0CACA;IACE;QAAC,IAAI;QAAG,MAAM;QAAkC,WAAW;IAAa;CACzE;AAMI,MAAM,2BAA2B,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CACnE,kDACA;IACE;QAAC,IAAI;QAAG,MAAM;QAA2C,WAAW;IAAa;CAClF;AAMI,MAAM,oBAAoB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,QAAQ,CAC5D,2CACA;IACE;QAAC,IAAI;QAAG,MAAM;QAAmC,WAAW;IAAa;CAC1E;AASI,MAAM,YAAY,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC3D,mCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACxE;YAAE,IAAI;YAAG,MAAM;YAAa,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACzE;YAAE,IAAI;YAAG,MAAM;YAAmB,MAAM;YAAU,GAAG,EAAE,oBAAoB;QAAG;QAC9E;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACxE;YAAE,IAAI;YAAG,MAAM;YAAe,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAC3E;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACtE;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACvE;YAAE,IAAI;YAAG,MAAM;YAAgB,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAC5E;YAAE,IAAI;YAAI,MAAM;YAAa,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAC1E;YAAE,IAAI;YAAI,MAAM;YAAkB,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAC/E;YAAE,IAAI;YAAI,MAAM;YAAO,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACpE;YAAE,IAAI;YAAI,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACzE;YAAE,IAAI;YAAI,MAAM;YAAc,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAC3E;YAAE,IAAI;YAAI,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACzE;YAAE,IAAI;YAAI,MAAM;YAAe,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAC5E;YAAE,IAAI;YAAI,MAAM;YAAW,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACxE;YAAE,IAAI;YAAI,MAAM;YAAc,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACzE;YAAE,IAAI;YAAI,MAAM;YAAU,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACrE;YAAE,IAAI;YAAI,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACvE;YAAE,IAAI;YAAI,MAAM;YAAU,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACrE;YAAE,IAAI;YAAI,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;KACxE;AAUI,MAAM,SAAS,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CACxD,gCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;YAAW,GAAG;YAAiB,OAAO;QAAS;QAClF;YAAE,IAAI;YAAG,MAAM;YAAa,MAAM;YAAW,GAAG;YAAgB,OAAO;QAAS;QAChF;YAAE,IAAI;YAAG,MAAM;YAAO,MAAM;YAAW,GAAG;YAAW,OAAO;QAAS;QACrE;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAW,GAAG;YAAc,OAAO;QAAS;QAC3E;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAW,GAAG;YAAa,OAAO;QAAS;QACzE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAW,GAAG;YAAa,OAAO;QAAS;QACzE;YAAE,IAAI;YAAG,MAAM;YAAkB,MAAM;YAAW,GAAG;YAAqB,OAAO;QAAS;QAC1F;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAW,GAAG;YAAa,OAAO;QAAS;KAC3E;AAQI,MAAM,kBAAkB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CACjE,yCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAO,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAU,GAAG,EAAE,oBAAoB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAa,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QAC1E;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;YAAW,GAAG,+PAAA,CAAA,YAAS;QAAC;QAC3D;YAAE,IAAI;YAAG,MAAM;YAAoB,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QACjF;YAAE,IAAI;YAAG,MAAM;YAAqB,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;KACnF;AAQI,MAAM,iBAAiB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAChE,wCACA,EAAE;AAQG,MAAM,YAAY,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC3D,mCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAS;QACxE;YAAE,IAAI;YAAG,MAAM;YAAa,MAAM;YAAU,GAAG,EAAE,oBAAoB;QAAG;QACxE;YAAE,IAAI;YAAG,MAAM;YAAoB,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QAC9E;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACxE;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACtE;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;KACvE;AAQI,MAAM,cAAc,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC7D,qCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;QACvF;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;QACtF;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACtE;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;KACtE;AAQI,MAAM,eAAe,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC9D,sCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAoB,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QAC9E;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;KACzE;AAQI,MAAM,cAAc,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC7D,qCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAe,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;YAAY,UAAU;QAAK;KAC9F;AAQI,MAAM,cAAc,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC7D,qCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;KACxE;AAMI,MAAM,mBAAmB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAClE,0CACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAmB,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAC/E;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QACtE;YAAE,IAAI;YAAG,MAAM;YAAO,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;KACrE;AAQI,MAAM,sBAAsB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CACrE,6CACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAW,GAAG;YAAkB,UAAU;QAAK;QAC/E;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAW,GAAG;YAAkB,UAAU;QAAK;KAC/E;AAQI,MAAM,gBAAgB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC/D,uCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAM;QACjE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACrE;YAAE,IAAI;YAAG,MAAM;YAAmB,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;QAC/F;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACtE;YAAE,IAAI;YAAG,MAAM;YAAO,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACvE;YAAE,IAAI;YAAG,MAAM;YAAa,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAoB;QACpF;YAAE,IAAI;YAAG,MAAM;YAAe,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QAC5E;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QACzE;YAAE,IAAI;YAAI,MAAM;YAAY,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QAC1E;YAAE,IAAI;YAAI,MAAM;YAAqB,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QACnF;YAAE,IAAI;YAAI,MAAM;YAAW,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAsB;KACtF;AAQI,MAAM,UAAU,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CACzD,iCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAM;QACjE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;YAAU,UAAU;QAAK;QACrF;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAW,GAAG;QAAiB;KACjE;AAMI,MAAM,mBAAmB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAClE,0CACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAO,MAAM;YAAO,GAAG,EAAE,qBAAqB;YAAI,GAAG;gBAAC,MAAM;gBAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;YAAQ;QAAE;QACnH;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;KACvF,EACD;IAAC,WAAW;AAAkB;AAQzB,MAAM,YAAY,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC3D,mCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAM;QACjE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;QACrF;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;QACpF;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAkB;KACjF;AAQI,MAAM,YAAY,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC3D,mCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAM;QACjE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;YAAY,UAAU;QAAK;QACvF;YAAE,IAAI;YAAG,MAAM;YAA4B,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACtF;YAAE,IAAI;YAAG,MAAM;YAAwB,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QAClF;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;YAAY,UAAU;QAAK;QACvF;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;YAAY,UAAU;QAAK;QACtF;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAkB;KACjF;AAQI,MAAM,oBAAoB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CACnE,2CACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAM;QACjE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;QACrF;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;QACpF;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAA0B;KACzF;AAQI,MAAM,aAAa,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC5D,oCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAM;QACjE;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;YAAU,GAAG,EAAE,mBAAmB;QAAG;QACxE;YAAE,IAAI;YAAG,MAAM;YAAmB,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;QAC/F;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAmB;KAClF;AAQI,MAAM,OAAO,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CACtD,8BACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;YAAW,GAAG;YAAe,OAAO;QAAO;QAC9E;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAW,GAAG;YAAS,OAAO;QAAO;QAClE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAW,GAAG;YAAW,OAAO;QAAO;QACrE;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAW,GAAG;YAAY,OAAO;QAAO;QACvE;YAAE,IAAI;YAAG,MAAM;YAAkB,MAAM;YAAW,GAAG;YAAmB,OAAO;QAAO;QACtF;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAW,GAAG;YAAW,OAAO;QAAO;KACvE;AAMI,MAAM,aAAa,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC5D,oCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACvE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAW;QACvE;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAY;QAC7E;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAW,GAAG;QAAO;QACpD;YAAE,IAAI;YAAG,MAAM;YAAO,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;KACrE;AAQI,MAAM,iBAAiB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAChE,wCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAM,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAClE;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACtE;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACxE;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAO,GAAG,EAAE,qBAAqB;YAAI,GAAG;gBAAC,MAAM;gBAAU,GAAG,EAAE,qBAAqB;YAAE;QAAE;QACvH;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAU,GAAG,GAAG,oBAAoB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAO,GAAG,EAAE,qBAAqB;YAAI,GAAG;gBAAC,MAAM;gBAAU,GAAG,EAAE,qBAAqB;YAAE;QAAE;QACrH;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACrE;YAAE,IAAI;YAAI,MAAM;YAAW,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QACxE;YAAE,IAAI;YAAI,MAAM;YAAS,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;KACvE;AAQI,MAAM,WAAW,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC1D,kCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAM,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAClE;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAY;QAC7E;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAW,GAAG;QAAO;QACpD;YAAE,IAAI;YAAG,MAAM;YAAgB,MAAM;YAAW,GAAG;YAAY,UAAU;QAAK;QAC9E;YAAE,IAAI;YAAG,MAAM;YAAO,MAAM;YAAU,GAAG,GAAG,qBAAqB;QAAG;QACpE;YAAE,IAAI;YAAG,MAAM;YAAc,MAAM;YAAW,GAAG;QAAU;KAC5D;AAQI,MAAM,gBAAgB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC/D,uCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAa,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAU;QAC1E;YAAE,IAAI;YAAG,MAAM;YAAe,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAC3E;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAW,GAAG;QAAe;QAC7D;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAW,GAAG;YAAM,UAAU;QAAK;QACjE;YAAE,IAAI;YAAG,MAAM;YAAmB,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;KAChG;AAQI,MAAM,iBAAiB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAChE,wCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAW,GAAG;QAAS;QACxD;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAO,GAAG,EAAE,qBAAqB;YAAI,GAAG;gBAAC,MAAM;gBAAU,GAAG,EAAE,qBAAqB;YAAE;QAAE;KACtH;AAQI,MAAM,gBAAgB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAC/D,uCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAa,MAAM;YAAQ,GAAG,mOAAA,CAAA,SAAM,CAAC,WAAW,CAAC;QAAU;QAC1E;YAAE,IAAI;YAAG,MAAM;YAAe,MAAM;YAAU,GAAG,EAAE,qBAAqB;QAAG;QAC3E;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;YAAW,GAAG;QAAe;QAC7D;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;YAAW,GAAG;QAAS;QACxD;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAW,GAAG;YAAM,UAAU;QAAK;QACjE;YAAE,IAAI;YAAG,MAAM;YAAmB,MAAM;YAAU,GAAG,EAAE,qBAAqB;YAAI,UAAU;QAAK;KAChG;AAQI,MAAM,iBAAiB,WAAW,GAAG,mOAAA,CAAA,SAAM,CAAC,eAAe,CAChE,wCACA,IAAM;QACJ;YAAE,IAAI;YAAG,MAAM;YAAS,MAAM;YAAO,GAAG,EAAE,qBAAqB;YAAI,GAAG;gBAAC,MAAM;gBAAU,GAAG,EAAE,qBAAqB;YAAE;QAAE;KACtH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40arcjet%2Bprotocol%401.0.0-beta.7/node_modules/%40arcjet/protocol/well-known-bots.js"], "sourcesContent": ["/** Automatically generated - DO NOT MANUALLY EDIT */\nconst categories = Object.freeze({\n    \"CATEGORY:ACADEMIC\": Object.freeze([\n        \"BLACKBOARD_CRAWLER\",\n        \"CISPA_CRAWLER\",\n        \"COMMONCRAWL_CRAWLER\",\n        \"DOMAINSPROJECT_CRAWLER\",\n        \"EPFL_CRAWLER\",\n        \"EZID_CRAWLER\",\n        \"LAW_UNIMI_CRAWLER\",\n        \"LEI<PERSON><PERSON>IG_FINDLINKS\",\n        \"LEIPZIG_LCC\",\n        \"MACOCU_CRAWLER\",\n        \"NICT_CRAWLER\",\n        \"SEMANTICSCHOLAR_CRAWLER\",\n        \"TURNITIN_CRAWLER\",\n        \"UTEXAS_CRAWLER\",\n        \"YAM<PERSON><PERSON>AB_CRAWLER\",\n    ]),\n    \"CATEGORY:ADVERTISING\": Object.freeze([\n        \"BING_ADS\",\n        \"GOOGLE_ADSBOT\",\n        \"GOOGLE_ADSBOT_MOBILE\",\n        \"GOOGLE_ADSENSE\",\n        \"GOOGLE_ADSENSE_GOOGLEBOT\",\n        \"GOO<PERSON><PERSON>_ADWORDS\",\n        \"MOAT_CRAWLER\",\n        \"MSN_CRAWLER\",\n    ]),\n    \"CATEGORY:AI\": Object.freeze([\n        \"AI_SEARCH_BOT\",\n        \"AI2_CRAWLER\",\n        \"AI2_CRAWLER_DOLMA\",\n        \"AIHIT_CRAWLER\",\n        \"ANTHROPIC_CRAWLER\",\n        \"BOTIFY_CRAWLER\",\n        \"BYTEDANCE_CRAWLER\",\n        \"COHERE_CRAWLER\",\n        \"COMMONCRAWL_CRAWLER\",\n        \"DIFFBOT_CRAWLER\",\n        \"FACEBOOK_CRAWLER\",\n        \"FACEBOOK_SHARE_CRAWLER\",\n        \"FRIENDLYCRAWLER\",\n        \"GLUTENFREEPLEASURE_CRAWLER\",\n        \"GOOGLE_CRAWLER_CLOUDVERTEX\",\n        \"IASK_CRAWLER\",\n        \"IMAGESIFT_CRAWLER\",\n        \"IMG2DATASET\",\n        \"INFEGY_CRAWLER\",\n        \"INTEGRALADS_CRAWLER\",\n        \"LEADCRUNCH_CRAWLER\",\n        \"MEDIATOOLKIT_CRAWLER\",\n        \"META_CRAWLER\",\n        \"META_CRAWLER_USER\",\n        \"NICT_CRAWLER\",\n        \"NTENT_CRAWLER\",\n        \"OMGILI_CRAWLER\",\n        \"OPENAI_CRAWLER\",\n        \"OPENAI_CRAWLER_SEARCH\",\n        \"OPENAI_CRAWLER_USER\",\n        \"PERPLEXITY_CRAWLER\",\n        \"PETALSEARCH_CRAWLER\",\n        \"PRIMAL_CRAWLER\",\n        \"PYTHON_SCRAPY\",\n        \"SAFEDNS_CRAWLER\",\n        \"SEARCHATLAS_CRAWLER\",\n        \"SEMANTICSCHOLAR_CRAWLER\",\n        \"SENTIONE_CRAWLER\",\n        \"STORYGIZE_CRAWLER\",\n        \"TIKTOK_CRAWLER\",\n        \"TIMPI_CRAWLER\",\n        \"TURNITIN_CRAWLER\",\n        \"VELEN_CRAWLER\",\n        \"WEBZIO_CRAWLER_AI\",\n        \"YOU_CRAWLER\",\n    ]),\n    \"CATEGORY:AMAZON\": Object.freeze([\n        \"AMAZON_ALEXA_CRAWLER\",\n        \"AMAZON_CLOUDFRONT\",\n        \"AMAZON_CRAWLER\",\n    ]),\n    \"CATEGORY:APPLE\": Object.freeze([\n        \"APPLE_CRAWLER\",\n        \"IMESSAGE_PREVIEW\",\n    ]),\n    \"CATEGORY:ARCHIVE\": Object.freeze([\n        \"ARCHIVEORG_ARCHIVER\",\n        \"CLOUDFLARE_ARCHIVER\",\n        \"COMMONCRAWL_CRAWLER\",\n        \"EZID_CRAWLER\",\n        \"INTERNETARCHIVE_CRAWLER_OSS\",\n        \"IRC_ARCHIVEBOT\",\n        \"LINKARCHIVER\",\n        \"NICECRAWLER_ARCHIVE\",\n        \"TRENDSMAP_CRAWLER\",\n        \"WEBARCHIVE_CRAWLER\",\n    ]),\n    \"CATEGORY:FEEDFETCHER\": Object.freeze([\n        \"APPLE_FEEDFETCHER\",\n        \"AWARIO_CRAWLER_RSS\",\n        \"BAZQUX_FEEDFETCHER\",\n        \"BLOGTRAFFIC_FEEDFETCHER\",\n        \"FEEDBIN_CRAWLER\",\n        \"FEEDLY_FEEDFETCHER\",\n        \"FEEDSPOT_FEEDFETCHER\",\n        \"FRESHRSS_FEEDFETCHER\",\n        \"G2READER_CRAWLER\",\n        \"GOOGLE_FEEDFETCHER\",\n        \"INOREADER_AGGREGATOR\",\n        \"MINIFLUX_FEEDFETCHER\",\n        \"NAVER_CRAWLER_RSS\",\n        \"NEWSBLUR_AGGREGATOR\",\n        \"POCKET_CRAWLER\",\n        \"REFIND_CRAWLER\",\n        \"RSSBOT_FEEDFETCHER\",\n        \"RSSING_CRAWLER\",\n        \"RSSMICRO_FEEDFETCHER\",\n        \"SERENDEPUTY_CRAWLER\",\n        \"STARTME_CRAWLER\",\n        \"SUPERFEEDR_CRAWLER\",\n        \"THEOLDREADER_CRAWLER\",\n        \"TTRSS_FEEDFETCHER\",\n        \"WORDPRESS_CRAWLER_RSS\",\n    ]),\n    \"CATEGORY:GOOGLE\": Object.freeze([\n        \"GOOGLE_ADSBOT\",\n        \"GOOGLE_ADSBOT_MOBILE\",\n        \"GOOGLE_ADSENSE\",\n        \"GOOGLE_ADSENSE_GOOGLEBOT\",\n        \"GOOGLE_ADWORDS\",\n        \"GOOGLE_APPENGINE\",\n        \"GOOGLE_CERTIFICATES_BRIDGE\",\n        \"GOOGLE_CRAWLER\",\n        \"GOOGLE_CRAWLER_CLOUDVERTEX\",\n        \"GOOGLE_CRAWLER_IMAGE\",\n        \"GOOGLE_CRAWLER_MOBILE\",\n        \"GOOGLE_CRAWLER_NEWS\",\n        \"GOOGLE_CRAWLER_OTHER\",\n        \"GOOGLE_CRAWLER_SAFETY\",\n        \"GOOGLE_CRAWLER_STORE\",\n        \"GOOGLE_CRAWLER_VIDEO\",\n        \"GOOGLE_FAVICON\",\n        \"GOOGLE_FEEDFETCHER\",\n        \"GOOGLE_INSPECTION_TOOL\",\n        \"GOOGLE_LIGHTHOUSE\",\n        \"GOOGLE_PHYSICAL_WEB\",\n        \"GOOGLE_PREVIEW\",\n        \"GOOGLE_PUSH_NOTIFICATIONS\",\n        \"GOOGLE_READ_ALOUD\",\n        \"GOOGLE_SITE_VERIFICATION\",\n        \"GOOGLE_STRUCTURED_DATA_TESTING_TOOL\",\n        \"GOOGLE_WEB_SNIPPET\",\n        \"GOOGLE_XRAWLER\",\n    ]),\n    \"CATEGORY:META\": Object.freeze([\n        \"FACEBOOK_CRAWLER\",\n        \"FACEBOOK_SHARE_CRAWLER\",\n        \"META_CRAWLER\",\n        \"META_CRAWLER_USER\",\n        \"WHATSAPP_CRAWLER\",\n    ]),\n    \"CATEGORY:MICROSOFT\": Object.freeze([\n        \"AZURE_APP_INSIGHTS\",\n        \"BING_ADS\",\n        \"BING_CRAWLER\",\n        \"BING_OFFICE_STORE\",\n        \"BING_PREVIEW\",\n        \"MICROSOFT_PREVIEW\",\n        \"MICROSOFT_RESEARCH_CRAWLER\",\n        \"MSN_CRAWLER\",\n        \"SKYPE_PREVIEW\",\n    ]),\n    \"CATEGORY:MONITOR\": Object.freeze([\n        \"AZURE_APP_INSIGHTS\",\n        \"BETTERUPTIME_MONITOR\",\n        \"BRANDVERITY_CRAWLER\",\n        \"CHANGEDETECTION_CRAWLER\",\n        \"DATADOG_MONITOR_SYNTHETICS\",\n        \"DEADLINKCHECKER\",\n        \"DISQUS_CRAWLER\",\n        \"DUBBOT_CRAWLER\",\n        \"DYNATRACE_MONITOR\",\n        \"FLIPBOARD_PROXY\",\n        \"FREEWEBMONITORING_MONITOR\",\n        \"FRESHWORKS_MONITOR\",\n        \"GOOGLE_CERTIFICATES_BRIDGE\",\n        \"GOOGLE_SITE_VERIFICATION\",\n        \"GOOGLE_STRUCTURED_DATA_TESTING_TOOL\",\n        \"HUBSPOT_CRAWLER\",\n        \"HYDROZEN_MONITOR\",\n        \"KUMA_MONITOR\",\n        \"MONITORBACKLINKS_CRAWLER\",\n        \"NIXSTATS_CRAWLER\",\n        \"OUTBRAIN_LINK_CHECKER\",\n        \"PAGEPEEKER_CRAWLER\",\n        \"PINGDOM_CRAWLER\",\n        \"SAFEDNS_CRAWLER\",\n        \"SENTRY_CRAWLER\",\n        \"SURLY_CRAWLER\",\n        \"TESTOMATO_CRAWLER\",\n        \"UPTIME_MONITOR\",\n        \"UPTIMEBOT_MONITOR\",\n        \"UPTIMEROBOT_MONITOR\",\n        \"VERCEL_MONITOR_PREVIEW\",\n        \"W3C_VALIDATOR_CSS\",\n        \"W3C_VALIDATOR_FEED\",\n        \"W3C_VALIDATOR_HTML\",\n        \"W3C_VALIDATOR_HTML_NU\",\n        \"W3C_VALIDATOR_I18N\",\n        \"W3C_VALIDATOR_LINKS\",\n        \"W3C_VALIDATOR_MOBILE\",\n        \"W3C_VALIDATOR_UNIFIED\",\n        \"WEBPAGETEST_CRAWLER\",\n        \"XENU_CRAWLER\",\n        \"ZABBIX_MONITOR\",\n    ]),\n    \"CATEGORY:OPTIMIZER\": Object.freeze([\n        \"CONDUCTOR_CRAWLER\",\n        \"DAREBOOST_CRAWLER\",\n        \"DUBBOT_CRAWLER\",\n        \"GOOGLE_LIGHTHOUSE\",\n        \"GOOGLE_STRUCTURED_DATA_TESTING_TOOL\",\n        \"MONSIDO_CRAWLER\",\n        \"MOZ_SITE_AUDIT\",\n        \"SCREAMINGFROG_CRAWLER\",\n        \"SISTRIX_CRAWLER\",\n        \"TESTOMATO_CRAWLER\",\n        \"WEBPAGETEST_CRAWLER\",\n    ]),\n    \"CATEGORY:PREVIEW\": Object.freeze([\n        \"BING_PREVIEW\",\n        \"BITLY_CRAWLER\",\n        \"DISCORD_CRAWLER\",\n        \"DUCKDUCKGO_CRAWLER_FAVICONS\",\n        \"EMBEDLY_CRAWLER\",\n        \"FACEBOOK_SHARE_CRAWLER\",\n        \"FLIPBOARD_PROXY\",\n        \"GOOGLE_FAVICON\",\n        \"GOOGLE_PREVIEW\",\n        \"GOOGLE_WEB_SNIPPET\",\n        \"IFRAMELY_PREVIEW\",\n        \"IMESSAGE_PREVIEW\",\n        \"MASTODON_CRAWLER\",\n        \"META_CRAWLER_USER\",\n        \"MICROSOFT_PREVIEW\",\n        \"PAGEPEEKER_CRAWLER\",\n        \"SKYPE_PREVIEW\",\n        \"SLACK_IMAGE_PROXY\",\n        \"SNAP_PREVIEW\",\n        \"STEAM_PREVIEW\",\n        \"SYNAPSE_CRAWLER\",\n        \"TELEGRAM_CRAWLER\",\n        \"TWITTER_CRAWLER\",\n        \"VERCEL_MONITOR_PREVIEW\",\n        \"VIBER_CRAWLER\",\n        \"WHATSAPP_CRAWLER\",\n        \"YAHOO_PREVIEW\",\n    ]),\n    \"CATEGORY:PROGRAMMATIC\": Object.freeze([\n        \"CODA_SERVER_FETCHER\",\n        \"GIGABLAST_CRAWLER_OSS\",\n        \"GO_HTTP\",\n        \"GOOGLE_APPENGINE\",\n        \"GOWIKI_CRAWLER\",\n        \"HTTP_GET\",\n        \"JAVA_APACHE_HTTPCLIENT\",\n        \"JAVA_ASYNCHTTPCLIENT\",\n        \"JAVA_CRAWLER4J\",\n        \"JAVA_HTTPUNIT\",\n        \"JAVA_JERSEY\",\n        \"JAVA_JETTY\",\n        \"JAVA_OKHTTP\",\n        \"JAVA_SNACKTORY\",\n        \"JAVASCRIPT_AXIOS\",\n        \"JAVASCRIPT_NODE_FETCH\",\n        \"JAVASCRIPT_PHANTOM\",\n        \"PERL_LIBWWW\",\n        \"PERL_PCORE\",\n        \"PHP_CURLCLASS\",\n        \"PHP_PHPCRAWL\",\n        \"PHP_SIMPLE_SCRAPER\",\n        \"PHP_SIMPLEPIE\",\n        \"PYTHON_AIOHTTP\",\n        \"PYTHON_BITBOT\",\n        \"PYTHON_HTTPX\",\n        \"PYTHON_OPENGRAPH\",\n        \"PYTHON_REQUESTS\",\n        \"PYTHON_SCRAPY\",\n        \"PYTHON_URLLIB\",\n        \"RUBY_METAINSPECTOR\",\n    ]),\n    \"CATEGORY:SEARCH_ENGINE\": Object.freeze([\n        \"ADDSEARCH_CRAWLER\",\n        \"AHREFS_CRAWLER\",\n        \"ALEXANDRIA_CRAWLER\",\n        \"APPLE_CRAWLER\",\n        \"ASK_CRAWLER\",\n        \"AVIRA_CRAWLER\",\n        \"BING_CRAWLER\",\n        \"BING_OFFICE_STORE\",\n        \"DUCKDUCKGO_CRAWLER\",\n        \"DUCKDUCKGO_CRAWLER_FAVICONS\",\n        \"ENTIREWEB_CRAWLER\",\n        \"GEEDO_CRAWLER\",\n        \"GEEDO_CRAWLER_PRODUCTS\",\n        \"GOOGLE_CRAWLER\",\n        \"GOOGLE_CRAWLER_IMAGE\",\n        \"GOOGLE_CRAWLER_MOBILE\",\n        \"GOOGLE_CRAWLER_NEWS\",\n        \"GOOGLE_CRAWLER_OTHER\",\n        \"GOOGLE_CRAWLER_STORE\",\n        \"GOOGLE_CRAWLER_VIDEO\",\n        \"GOOGLE_INSPECTION_TOOL\",\n        \"IASK_CRAWLER\",\n        \"LINGUEE_CRAWLER\",\n        \"MAJESTIC_CRAWLER\",\n        \"MARGINALIA_CRAWLER\",\n        \"MOJEEK_CRAWLER\",\n        \"NETESTATE_CRAWLER\",\n        \"OPENAI_CRAWLER_SEARCH\",\n        \"PETALSEARCH_CRAWLER\",\n        \"PIPL_CRAWLER\",\n        \"QWANT_CRAWLER\",\n        \"SEEKPORT_CRAWLER\",\n        \"STRACT_CRAWLER\",\n        \"WEBZIO_CRAWLER\",\n        \"WELLKNOWN_CRAWLER\",\n        \"YACY_CRAWLER\",\n        \"YAHOO_CRAWLER\",\n        \"YANDEX_CRAWLER\",\n        \"YANDEX_CRAWLER_JAVASCRIPT\",\n    ]),\n    \"CATEGORY:SLACK\": Object.freeze([\n        \"SLACK_CRAWLER\",\n        \"SLACK_IMAGE_PROXY\",\n    ]),\n    \"CATEGORY:SOCIAL\": Object.freeze([\n        \"DIGG_CRAWLER\",\n        \"DISCORD_CRAWLER\",\n        \"EVERYONESOCIAL_CRAWLER\",\n        \"FACEBOOK_CRAWLER\",\n        \"FACEBOOK_SHARE_CRAWLER\",\n        \"GOOGLE_PREVIEW\",\n        \"GOOGLE_WEB_SNIPPET\",\n        \"GROUPME_CRAWLER\",\n        \"IFRAMELY_PREVIEW\",\n        \"IMESSAGE_PREVIEW\",\n        \"IRC_ARCHIVEBOT\",\n        \"LEMMY_CRAWLER\",\n        \"LINKARCHIVER\",\n        \"LINKEDIN_CRAWLER\",\n        \"MASTODON_CRAWLER\",\n        \"NETICLE_CRAWLER\",\n        \"PINTREST_CRAWLER\",\n        \"REDDIT_CRAWLER\",\n        \"SNAP_PREVIEW\",\n        \"STEAM_PREVIEW\",\n        \"SYNAPSE_CRAWLER\",\n        \"TELEGRAM_CRAWLER\",\n        \"TIKTOK_CRAWLER\",\n        \"TRENDSMAP_CRAWLER\",\n        \"TWEETEDTIMES_CRAWLER\",\n        \"TWITTER_CRAWLER\",\n        \"VIBER_CRAWLER\",\n        \"WHATSAPP_CRAWLER\",\n    ]),\n    \"CATEGORY:TOOL\": Object.freeze([\n        \"CURL\",\n        \"DCRAWL\",\n        \"DOMAINSPROJECT_CRAWLER\",\n        \"GIGABLAST_CRAWLER_OSS\",\n        \"HEADLESS_CHROME\",\n        \"IMG2DATASET\",\n        \"INTERNETARCHIVE_CRAWLER_OSS\",\n        \"IPIP_CRAWLER\",\n        \"L9EXPLORE\",\n        \"LAW_UNIMI_CRAWLER\",\n        \"NAGIOS_CHECK_HTTP\",\n        \"NMAP\",\n        \"NUTCH\",\n        \"POSTMAN\",\n        \"SUMMALY_CRAWLER\",\n        \"WGET\",\n        \"XENU_CRAWLER\",\n        \"ZGRAB\",\n    ]),\n    \"CATEGORY:UNKNOWN\": Object.freeze([\n        \"A6CORP_CRAWLER\",\n        \"ABOUNDEX_CRAWLER\",\n        \"ACAPBOT\",\n        \"ACOON_CRAWLER\",\n        \"ADBEAT_CRAWLER\",\n        \"ADDTHIS_CRAWLER\",\n        \"ADMANTX_CRAWLER\",\n        \"ADSCANNER_CRAWLER\",\n        \"ADSTXTCRAWLER\",\n        \"ADVBOT_CRAWLER\",\n        \"ALPHASEOBOT_CRAWLER\",\n        \"ANDERSPINK_CRAWLER\",\n        \"ANTIBOT\",\n        \"APERCITE_CRAWLER\",\n        \"ARA_CRAWLER\",\n        \"ARATURKA_CRAWLER\",\n        \"AROCOM_CRAWLER\",\n        \"ASPIEGEL_CRAWLER\",\n        \"AUDISTO_CRAWLER\",\n        \"AWARIO_CRAWLER\",\n        \"AWARIO_CRAWLER_SMART\",\n        \"AWESOMECRAWLER\",\n        \"B2BBOT\",\n        \"BACKLINKTEST_CRAWLER\",\n        \"BAIDU_CLOUD_WATCH\",\n        \"BAIDU_CRAWLER\",\n        \"BETABOT\",\n        \"BIDSWITCH_CRAWLER\",\n        \"BIGDATACORP_CRAWLER\",\n        \"BIGLOTRON\",\n        \"BINLAR\",\n        \"BITSIGHT_CRAWLER\",\n        \"BLOGMURA_CRAWLER\",\n        \"BLP_BBOT\",\n        \"BNF_CRAWLER\",\n        \"BOMBORA_CRAWLER\",\n        \"BOXCAR_CRAWLER\",\n        \"BRAINOBOT\",\n        \"BRANDONMEDIA_CRAWLER\",\n        \"BRANDWATCH_CRAWLER\",\n        \"BRIGHTEDGE_CRAWLER\",\n        \"BUBLUP_CRAWLER\",\n        \"BUILTWITH_CRAWLER\",\n        \"BUZZSTREAM_CRAWLER\",\n        \"CAPSULINK_CRAWLER\",\n        \"CAREERX_CRAWLER\",\n        \"CENTURYBOT\",\n        \"CHECKMARKNETWORK_CRAWLER\",\n        \"CHLOOE_CRAWLER\",\n        \"CINCRAWDATA_CRAWLER\",\n        \"CITESEERX_CRAWLER\",\n        \"CLICKAGY_CRAWLER\",\n        \"CLIQZ_CRAWLER\",\n        \"CLOUDSYSTEMNETWORKS_CRAWLER\",\n        \"COCCOC_CRAWLER\",\n        \"COCOLYZE_CRAWLER\",\n        \"CODEWISE_CRAWLER\",\n        \"COGNITIVESEO_CRAWLER\",\n        \"COMPANYBOOK_CRAWLER\",\n        \"CONTENT_CRAWLER_SPIDER\",\n        \"CONTEXTAD_CRAWLER\",\n        \"CONTXBOT\",\n        \"CONVERA_CRAWLER\",\n        \"COOKIEBOT_CRAWLER\",\n        \"CREATIVECOMMONS_CRAWLER\",\n        \"CRITEO_CRAWLER\",\n        \"CRYSTALSEMANTICS_CRAWLER\",\n        \"CUREBOT_CRAWLER\",\n        \"CUTBOT_CRAWLER\",\n        \"CXENSE_CRAWLER\",\n        \"CYBERPATROL_CRAWLER\",\n        \"DATAFEEDWATCH_CRAWLER\",\n        \"DATAFORSEO_CRAWLER\",\n        \"DATAGNION_CRAWLER\",\n        \"DATANYZE_CRAWLER\",\n        \"DATAPROVIDER_CRAWLER\",\n        \"DATENBUTLER_CRAWLER\",\n        \"DAUM_CRAWLER\",\n        \"DEEPNOC_CRAWLER\",\n        \"DEUSU_CRAWLER\",\n        \"DIGINCORE_CRAWLER\",\n        \"DIGITALDRAGON_CRAWLER\",\n        \"DISCOVERYENGINE_CRAWLER\",\n        \"DNYZ_CRAWLER\",\n        \"DOMAINCRAWLER_CRAWLER\",\n        \"DOMAINREANIMATOR_CRAWLER\",\n        \"DOMAINSBOT_CRAWLER\",\n        \"DOMAINSTATS_CRAWLER\",\n        \"DOMAINTOOLS_CRAWLER\",\n        \"DOTNETDOTCOM_CRAWLER\",\n        \"DRAGONMETRICS_CRAWLER\",\n        \"DRIFTNET_CRAWLER\",\n        \"DUEDIL_CRAWLER\",\n        \"EC2LINKFINDER\",\n        \"EDISTER_CRAWLER\",\n        \"ELISABOT\",\n        \"EPICTIONS_CRAWLER\",\n        \"ERIGHT_CRAWLER\",\n        \"EUROPARCHIVE_CRAWLER\",\n        \"EVENTURES_CRAWLER\",\n        \"EVENTURES_CRAWLER_BATCH\",\n        \"EXENSA_CRAWLER\",\n        \"EXPERIBOT_CRAWLER\",\n        \"EXTLINKS_CRAWLER\",\n        \"EYEOTA_CRAWLER\",\n        \"FAST_CRAWLER\",\n        \"FAST_CRAWLER_ENTERPRISE\",\n        \"FEDORAPLANET_CRAWLER\",\n        \"FEEDAFEVER_CRAWLER\",\n        \"FEMTOSEARCH_CRAWLER\",\n        \"FINDTHATFILE_CRAWLER\",\n        \"FLAMINGOSEARCH_CRAWLER\",\n        \"FLUFFY\",\n        \"FR_CRAWLER\",\n        \"FUELBOT\",\n        \"FYREBOT\",\n        \"G00G1E_CRAWLER\",\n        \"G2WEBSERVICES_CRAWLER\",\n        \"GARLIK_CRAWLER\",\n        \"GENIEO_CRAWLER\",\n        \"GIGABLAST_CRAWLER\",\n        \"GINGER_CRAWLER\",\n        \"GNAM_GNAM_SPIDER\",\n        \"GNOWIT_CRAWLER\",\n        \"GOO_CRAWLER\",\n        \"GRAPESHOT_CRAWLER\",\n        \"GROB_CRAWLER\",\n        \"GROUPHIGH_CRAWLER\",\n        \"GRUB\",\n        \"GSLFBOT\",\n        \"GWENE_CRAWLER\",\n        \"HAOSOU_CRAWLER\",\n        \"HATENA_CRAWLER\",\n        \"HEADLINE_CRAWLER\",\n        \"HOYER_CRAWLER\",\n        \"HTTRACK\",\n        \"HYPEFACTORS_CRAWLER\",\n        \"HYPESTAT_CRAWLER\",\n        \"HYSCORE_CRAWLER\",\n        \"IA_ARCHIVER\",\n        \"IDEASANDCODE_CRAWLER\",\n        \"INDEED_CRAWLER\",\n        \"INETDEX_CRAWLER\",\n        \"INFOO_CRAWLER\",\n        \"INTEGROMEDB_CRAWLER\",\n        \"INTELIUM_CRAWLER\",\n        \"IONOS_CRAWLER\",\n        \"IP_WEB_CRAWLER\",\n        \"ISKANIE_CRAWLER\",\n        \"ISS_CRAWLER\",\n        \"IT2MEDIA_CRAWLER\",\n        \"ITINFLUENTIALS_CRAWLER\",\n        \"JAMIEMBROWN_CRAWLER\",\n        \"JETSLIDE_CRAWLER\",\n        \"JOBBOERSE_CRAWLER\",\n        \"JOOBLE_CRAWLER\",\n        \"JUSPROG_CRAWLER\",\n        \"JYXO_CRAWLER\",\n        \"K7COMPUTING_CRAWLER\",\n        \"KEMVI_CRAWLER\",\n        \"KOMODIA_CRAWLER\",\n        \"KOSMIO_CRAWLER\",\n        \"LANDAUMEDIA_CRAWLER\",\n        \"LASERLIKE_CRAWLER\",\n        \"LB_SPIDER\",\n        \"LEIKI_CRAWLER\",\n        \"LIGHTSPEEDSYSTEMS_CRAWLER\",\n        \"LINE_CRAWLER\",\n        \"LINKAPEDIA_CRAWLER\",\n        \"LINKDEX_CRAWLER\",\n        \"LINKFLUENCE_CRAWLER\",\n        \"LINKIS_CRAWLER\",\n        \"LIPPERHEY_CRAWLER\",\n        \"LIVELAP_CRAWLER\",\n        \"LOGLY_CRAWLER\",\n        \"LOOP_CRAWLER\",\n        \"LSSBOT\",\n        \"LSSBOT_ROCKET\",\n        \"LTX71_CRAWLER\",\n        \"LUMINATOR_CRAWLER\",\n        \"MAILRU_CRAWLER\",\n        \"MAPPYDATA_CRAWLER\",\n        \"MAUIBOT\",\n        \"MEGAINDEX_CRAWLER\",\n        \"MELTWATER_CRAWLER\",\n        \"METADATALABS_CRAWLER\",\n        \"METAJOB_CRAWLER\",\n        \"METAURI_CRAWLER\",\n        \"METRICSTOOLS_CRAWLER\",\n        \"MIGNIFY_CRAWLER\",\n        \"MIGNIFY_IMRBOT\",\n        \"MIXNODE_CACHE\",\n        \"MOODLE_CRAWLER\",\n        \"MOREOVER_CRAWLER\",\n        \"MOZ_CRAWLER\",\n        \"MUCKRACK_CRAWLER\",\n        \"MULTIVIEWBOT\",\n        \"NAVER_CRAWLER\",\n        \"NEEVA_CRAWLER\",\n        \"NERDBYNATURE_CRAWLER\",\n        \"NERDYBOT_CRAWLER\",\n        \"NETCRAFT_CRAWLER\",\n        \"NETSYSTEMSRESEARCH_CRAWLER\",\n        \"NETVIBES_CRAWLER\",\n        \"NEWSHARECOUNTS_CRAWLER\",\n        \"NEWSPAPER\",\n        \"NEXTCLOUD_CRAWLER\",\n        \"NIKI_BOT\",\n        \"NING_CRAWLER\",\n        \"NINJABOT\",\n        \"NUZZEL_CRAWLER\",\n        \"OCARINABOT\",\n        \"OKRU_CRAWLER\",\n        \"OPENGRAPHCHECK_CRAWLER\",\n        \"OPENHOSE_CRAWLER\",\n        \"OPENINDEX_CRAWLER\",\n        \"ORANGE_CRAWLER\",\n        \"ORANGE_FTGROUP_CRAWLER\",\n        \"OUTCLICKS_CRAWLER\",\n        \"PAGE_TO_RSS\",\n        \"PAGETHING_CRAWLER\",\n        \"PALOALTONETWORKS_CRAWLER\",\n        \"PANSCIENT_CRAWLER\",\n        \"PAPERLI_CRAWLER\",\n        \"PHXBOT\",\n        \"PICSEARCH_CRAWLER\",\n        \"POSTRANK_CRAWLER\",\n        \"PRCY_CRAWLER\",\n        \"PRIVACORE_CRAWLER\",\n        \"PRIVACYAWARE_CRAWLER\",\n        \"PROFOUND_CRAWLER\",\n        \"PROXIMIC_CRAWLER\",\n        \"PULSEPOINT_CRAWLER\",\n        \"PURE_CRAWLER\",\n        \"RANKACTIVE_CRAWLER\",\n        \"RETREVO_PAGE_ANALYZER\",\n        \"RIDDER_CRAWLER\",\n        \"RIVVA_CRAWLER\",\n        \"RYTE_CRAWLER\",\n        \"SCAN_INTERFAX_CRAWLER\",\n        \"SCHMORP_CRAWLER\",\n        \"SCOUTJET_CRAWLER\",\n        \"SCRIBD_CRAWLER\",\n        \"SCRITCH_CRAWLER\",\n        \"SEEKBOT_CRAWLER\",\n        \"SEEWITHKIDS_CRAWLER\",\n        \"SEMANTICAUDIENCE_CRAWLER\",\n        \"SEMPITECH_CRAWLER\",\n        \"SEMRUSH_CRAWLER\",\n        \"SENUTO_CRAWLER\",\n        \"SEOBILITY_CRAWLER\",\n        \"SEOKICKS_CRAWLER\",\n        \"SEOLIZER_CRAWLER\",\n        \"SEOPROFILER_CRAWLER\",\n        \"SEOSCANNERS_CRAWLER\",\n        \"SEOSTAR_CRAWLER\",\n        \"SEOZOOM_CRAWLER\",\n        \"SERPSTATBOT_CRAWLER\",\n        \"SEZNAM_CRAWLER\",\n        \"SIMILARTECH_CRAWLER\",\n        \"SIMPLE_CRAWLER\",\n        \"SISTRIX_007AC9_CRAWLER\",\n        \"SITEBOT_CRAWLER\",\n        \"SITECHECKER_CRAWLER\",\n        \"SITEEXPLORER_CRAWLER\",\n        \"SITEIMPROVE_CRAWLER\",\n        \"SOCIALRANK_CRAWLER\",\n        \"SOFTBYTELABS_CRAWLER\",\n        \"SOGOU_CRAWLER\",\n        \"STUTTGART_CRAWLER\",\n        \"SUMMIFY_CRAWLER\",\n        \"SWIMGBOT\",\n        \"SYSOMOS_CRAWLER\",\n        \"T3VERSIONS_CRAWLER\",\n        \"TABOOLA_CRAWLER\",\n        \"TAGOO_CRAWLER\",\n        \"TANGIBLEE_CRAWLER\",\n        \"THINKLAB_CRAWLER\",\n        \"TIGER_CRAWLER\",\n        \"TINEYE_CRAWLER\",\n        \"TISCALI_CRAWLER\",\n        \"TOMBASCRAPER_CRAWLER\",\n        \"TOPLIST_CRAWLER\",\n        \"TORUS_CRAWLER\",\n        \"TOUTIAO_CRAWLER\",\n        \"TRAACKR_CRAWLER\",\n        \"TRACEMYFILE_CRAWLER\",\n        \"TRENDICTION_CRAWLER\",\n        \"TROVE_CRAWLER\",\n        \"TROVIT_CRAWLER\",\n        \"TWEETMEMEBOT\",\n        \"TWENGA_CRAWLER\",\n        \"TWINGLY_CRAWLER\",\n        \"TWOIP_CRAWLER\",\n        \"TWOIP_CRAWLER_CMS\",\n        \"TWURLY_CRAWLER\",\n        \"UBERMETRICS_CRAWLER\",\n        \"UBT_CRAWLER\",\n        \"UPFLOW_CRAWLER\",\n        \"URLCLASSIFICATION_CRAWLER\",\n        \"USINE_NOUVELLE_CRAWLER\",\n        \"UTORRENT_CRAWLER\",\n        \"VEBIDOO_CRAWLER\",\n        \"VEOOZ_CRAWLER\",\n        \"VERISIGN_IPS_AGENT\",\n        \"VIGIL_CRAWLER\",\n        \"VIPNYTT_CRAWLER\",\n        \"VIRUSTOTAL_CRAWLER\",\n        \"VKROBOT_CRAWLER\",\n        \"VKSHARE_CRAWLER\",\n        \"VUHUV_CRAWLER\",\n        \"WAREBAY_CRAWLER\",\n        \"WEBCEO_CRAWLER\",\n        \"WEBCOMPANY_CRAWLER\",\n        \"WEBDATASTATS_CRAWLER\",\n        \"WEBEAVER_CRAWLER\",\n        \"WEBMEUP_CRAWLER\",\n        \"WEBMON\",\n        \"WESEE_CRAWLER\",\n        \"WOCODI_CRAWLER\",\n        \"WOORANK_CRAWLER\",\n        \"WOORANK_CRAWLER_REVIEW\",\n        \"WORDPRESS_CRAWLER\",\n        \"WORDUP_CRAWLER\",\n        \"WORIO_CRAWLER\",\n        \"WOTBOX_CRAWLER\",\n        \"XOVIBOT_CRAWLER\",\n        \"YANGA_CRAWLER\",\n        \"YELLOWBP_CRAWLER\",\n        \"YISOU_CRAWLER\",\n        \"YOOZ_CRAWLER\",\n        \"ZOOMINFO_CRAWLER\",\n        \"ZUM_CRAWLER\",\n        \"ZUPERLIST_CRAWLER\",\n    ]),\n    \"CATEGORY:VERCEL\": Object.freeze([\n        \"VERCEL_CRAWLER\",\n        \"VERCEL_MONITOR_PREVIEW\",\n    ]),\n    \"CATEGORY:WEBHOOK\": Object.freeze([\"STRIPE_WEBHOOK\"]),\n    \"CATEGORY:YAHOO\": Object.freeze([\n        \"YAHOO_CRAWLER\",\n        \"YAHOO_CRAWLER_JAPAN\",\n        \"YAHOO_PREVIEW\",\n    ]),\n});\n\nexport { categories };\n"], "names": [], "mappings": "AAAA,mDAAmD;;;AACnD,MAAM,aAAa,OAAO,MAAM,CAAC;IAC7B,qBAAqB,OAAO,MAAM,CAAC;QAC/B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,wBAAwB,OAAO,MAAM,CAAC;QAClC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,eAAe,OAAO,MAAM,CAAC;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,mBAAmB,OAAO,MAAM,CAAC;QAC7B;QACA;QACA;KACH;IACD,kBAAkB,OAAO,MAAM,CAAC;QAC5B;QACA;KACH;IACD,oBAAoB,OAAO,MAAM,CAAC;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,wBAAwB,OAAO,MAAM,CAAC;QAClC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,mBAAmB,OAAO,MAAM,CAAC;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,iBAAiB,OAAO,MAAM,CAAC;QAC3B;QACA;QACA;QACA;QACA;KACH;IACD,sBAAsB,OAAO,MAAM,CAAC;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,oBAAoB,OAAO,MAAM,CAAC;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,sBAAsB,OAAO,MAAM,CAAC;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,oBAAoB,OAAO,MAAM,CAAC;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,yBAAyB,OAAO,MAAM,CAAC;QACnC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,0BAA0B,OAAO,MAAM,CAAC;QACpC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,kBAAkB,OAAO,MAAM,CAAC;QAC5B;QACA;KACH;IACD,mBAAmB,OAAO,MAAM,CAAC;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,iBAAiB,OAAO,MAAM,CAAC;QAC3B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,oBAAoB,OAAO,MAAM,CAAC;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,mBAAmB,OAAO,MAAM,CAAC;QAC7B;QACA;KACH;IACD,oBAAoB,OAAO,MAAM,CAAC;QAAC;KAAiB;IACpD,kBAAkB,OAAO,MAAM,CAAC;QAC5B;QACA;QACA;KACH;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40arcjet%2Bprotocol%401.0.0-beta.7/node_modules/%40arcjet/protocol/index.js"], "sourcesContent": ["import { typeid } from 'typeid-js';\nimport { Reason } from './proto/decide/v1alpha1/decide_pb.js';\nexport { categories as botCategories } from './well-known-bots.js';\n\nconst ArcjetMode = Object.freeze({\n    /**\n     * @deprecated Use the string `\"LIVE\"` instead.\n     **/\n    LIVE: \"LIVE\",\n    /**\n     * @deprecated Use the string `\"DRY_RUN\"` instead.\n     **/\n    DRY_RUN: \"DRY_RUN\",\n});\nconst ArcjetRateLimitAlgorithm = Object.freeze({\n    /**\n     * @deprecated Use the string `\"TOKEN_BUCKET\"` instead.\n     **/\n    TOKEN_BUCKET: \"TOKEN_BUCKET\",\n    /**\n     * @deprecated Use the string `\"FIXED_WINDOW\"` instead.\n     **/\n    FIXED_WINDOW: \"FIXED_WINDOW\",\n    /**\n     * @deprecated Use the string `\"SLIDING_WINDOW\"` instead.\n     **/\n    SLIDING_WINDOW: \"SLIDING_WINDOW\",\n});\nconst ArcjetEmailType = Object.freeze({\n    /**\n     * @deprecated Use the string `\"DISPOSABLE\"` instead.\n     **/\n    DISPOSABLE: \"DISPOSABLE\",\n    /**\n     * @deprecated Use the string `\"FREE\"` instead.\n     **/\n    FREE: \"FREE\",\n    /**\n     * @deprecated Use the string `\"NO_MX_RECORDS\"` instead.\n     **/\n    NO_MX_RECORDS: \"NO_MX_RECORDS\",\n    /**\n     * @deprecated Use the string `\"NO_GRAVATAR\"` instead.\n     **/\n    NO_GRAVATAR: \"NO_GRAVATAR\",\n    /**\n     * @deprecated Use the string `\"INVALID\"` instead.\n     **/\n    INVALID: \"INVALID\",\n});\nconst ArcjetStack = Object.freeze({\n    /**\n     * @deprecated Use the string `\"NODEJS\"` instead.\n     **/\n    NODEJS: \"NODEJS\",\n    /**\n     * @deprecated Use the string `\"NEXTJS\"` instead.\n     **/\n    NEXTJS: \"NEXTJS\",\n    /**\n     * @deprecated Use the string `\"BUN\"` instead.\n     **/\n    BUN: \"BUN\",\n    /**\n     * @deprecated Use the string `\"SVELTEKIT\"` instead.\n     **/\n    SVELTEKIT: \"SVELTEKIT\",\n    /**\n     * @deprecated Use the string `\"DENO\"` instead.\n     **/\n    DENO: \"DENO\",\n    /**\n     * @deprecated Use the string `\"NESTJS\"` instead.\n     **/\n    NESTJS: \"NESTJS\",\n    /**\n     * @deprecated Use the string `\"REMIX\"` instead.\n     **/\n    REMIX: \"REMIX\",\n    /**\n     * @deprecated Use the string `\"ASTRO\"` instead.\n     **/\n    ASTRO: \"ASTRO\",\n});\nconst ArcjetRuleState = Object.freeze({\n    /**\n     * @deprecated Use the string `\"RUN\"` instead.\n     **/\n    RUN: \"RUN\",\n    /**\n     * @deprecated Use the string `\"NOT_RUN\"` instead.\n     **/\n    NOT_RUN: \"NOT_RUN\",\n    /**\n     * @deprecated Use the string `\"CACHED\"` instead.\n     **/\n    CACHED: \"CACHED\",\n    /**\n     * @deprecated Use the string `\"DRY_RUN\"` instead.\n     **/\n    DRY_RUN: \"DRY_RUN\",\n});\nconst ArcjetConclusion = Object.freeze({\n    /**\n     * @deprecated Use the string `\"ALLOW\"` instead.\n     **/\n    ALLOW: \"ALLOW\",\n    /**\n     * @deprecated Use the string `\"DENY\"` instead.\n     **/\n    DENY: \"DENY\",\n    /**\n     * @deprecated Use the string `\"CHALLENGE\"` instead.\n     **/\n    CHALLENGE: \"CHALLENGE\",\n    /**\n     * @deprecated Use the string `\"ERROR\"` instead.\n     **/\n    ERROR: \"ERROR\",\n});\nconst ArcjetSensitiveInfoType = Object.freeze({\n    /**\n     * @deprecated Use the string `\"EMAIL\"` instead.\n     **/\n    EMAIL: \"EMAIL\",\n    /**\n     * @deprecated Use the string `\"PHONE_NUMBER\"` instead.\n     **/\n    PHONE_NUMBER: \"PHONE_NUMBER\",\n    /**\n     * @deprecated Use the string `\"IP_ADDRESS\"` instead.\n     **/\n    IP_ADDRESS: \"IP_ADDRESS\",\n    /**\n     * @deprecated Use the string `\"CREDIT_CARD_NUMBER\"` instead.\n     **/\n    CREDIT_CARD_NUMBER: \"CREDIT_CARD_NUMBER\",\n});\nconst ArcjetRuleType = Object.freeze({\n    /**\n     * @deprecated Use the string `\"LOCAL\"` instead.\n     **/\n    LOCAL: \"LOCAL\",\n    /**\n     * @deprecated Use the string `\"REMOTE\"` instead.\n     **/\n    REMOTE: \"REMOTE\",\n});\nclass ArcjetReason {\n    type;\n    isSensitiveInfo() {\n        return this.type === \"SENSITIVE_INFO\";\n    }\n    isRateLimit() {\n        return this.type === \"RATE_LIMIT\";\n    }\n    isBot() {\n        return this.type === \"BOT\";\n    }\n    isEdgeRule() {\n        return this.type === \"EDGE_RULE\";\n    }\n    isShield() {\n        return this.type === \"SHIELD\";\n    }\n    isEmail() {\n        return this.type === \"EMAIL\";\n    }\n    isError() {\n        return this.type === \"ERROR\";\n    }\n}\nclass ArcjetSensitiveInfoReason extends ArcjetReason {\n    type = \"SENSITIVE_INFO\";\n    denied;\n    allowed;\n    constructor(init) {\n        super();\n        this.denied = init.denied;\n        this.allowed = init.allowed;\n    }\n}\nclass ArcjetRateLimitReason extends ArcjetReason {\n    type = \"RATE_LIMIT\";\n    max;\n    remaining;\n    reset;\n    window;\n    resetTime;\n    constructor(init) {\n        super();\n        this.max = init.max;\n        this.remaining = init.remaining;\n        this.reset = init.reset;\n        this.window = init.window;\n        this.resetTime = init.resetTime;\n    }\n}\nclass ArcjetBotReason extends ArcjetReason {\n    type = \"BOT\";\n    allowed;\n    denied;\n    verified;\n    spoofed;\n    constructor(init) {\n        super();\n        this.allowed = init.allowed;\n        this.denied = init.denied;\n        this.verified = init.verified;\n        this.spoofed = init.spoofed;\n    }\n    isVerified() {\n        return this.verified;\n    }\n    isSpoofed() {\n        return this.spoofed;\n    }\n}\nclass ArcjetEdgeRuleReason extends ArcjetReason {\n    type = \"EDGE_RULE\";\n}\nclass ArcjetShieldReason extends ArcjetReason {\n    type = \"SHIELD\";\n    shieldTriggered;\n    constructor(init) {\n        super();\n        this.shieldTriggered = init.shieldTriggered ?? false;\n    }\n}\nclass ArcjetEmailReason extends ArcjetReason {\n    type = \"EMAIL\";\n    emailTypes;\n    constructor(init) {\n        super();\n        if (typeof init === \"undefined\") {\n            this.emailTypes = [];\n        }\n        else {\n            this.emailTypes = init.emailTypes ?? [];\n        }\n    }\n}\nclass ArcjetErrorReason extends ArcjetReason {\n    type = \"ERROR\";\n    message;\n    constructor(error) {\n        super();\n        // TODO: Get rid of instanceof check\n        if (error instanceof Reason) {\n            if (error.reason.case === \"error\") {\n                this.message = error.reason.value.message;\n                return;\n            }\n            else {\n                this.message = \"Missing error reason\";\n            }\n        }\n        // TODO: Get rid of instanceof check\n        if (error instanceof Error) {\n            this.message = error.message;\n            return;\n        }\n        if (typeof error === \"string\") {\n            this.message = error;\n            return;\n        }\n        this.message = \"Unknown error occurred\";\n    }\n}\nclass ArcjetRuleResult {\n    /**\n     * The stable, deterministic, and unique identifier of the rule that generated\n     * this result.\n     */\n    ruleId;\n    /**\n     * The duration in seconds this result should be considered valid, also known\n     * as time-to-live.\n     */\n    ttl;\n    state;\n    conclusion;\n    reason;\n    constructor(init) {\n        this.ruleId = init.ruleId;\n        this.ttl = init.ttl;\n        this.state = init.state;\n        this.conclusion = init.conclusion;\n        this.reason = init.reason;\n    }\n    isDenied() {\n        return this.conclusion === \"DENY\";\n    }\n}\nclass ArcjetIpDetails {\n    /**\n     * The estimated latitude of the IP address within the `accuracyRadius` margin\n     * of error.\n     */\n    latitude;\n    /**\n     * The estimated longitude of the IP address - see accuracy_radius for the\n     * margin of error.\n     */\n    longitude;\n    /**\n     * The accuracy radius of the IP address location in kilometers.\n     */\n    accuracyRadius;\n    /**\n     * The timezone of the IP address.\n     */\n    timezone;\n    /**\n     * The postal code of the IP address.\n     */\n    postalCode;\n    /**\n     * The city the IP address is located in.\n     */\n    city;\n    /**\n     * The region the IP address is located in.\n     */\n    region;\n    /**\n     * The country code the IP address is located in.\n     */\n    country;\n    /**\n     * The country name the IP address is located in.\n     */\n    countryName;\n    /**\n     * The continent code the IP address is located in.\n     */\n    continent;\n    /**\n     * The continent name the IP address is located in.\n     */\n    continentName;\n    /**\n     * The AS number the IP address belongs to.\n     */\n    asn;\n    /**\n     * The AS name the IP address belongs to.\n     */\n    asnName;\n    /**\n     * The AS domain the IP address belongs to.\n     */\n    asnDomain;\n    /**\n     * The ASN type: ISP, hosting, business, or education\n     */\n    asnType;\n    /**\n     * The ASN country code the IP address belongs to.\n     */\n    asnCountry;\n    /**\n     * The name of the service the IP address belongs to.\n     */\n    service;\n    constructor(init = {}) {\n        this.latitude = init.latitude;\n        this.longitude = init.longitude;\n        this.accuracyRadius = init.accuracyRadius;\n        this.timezone = init.timezone;\n        this.postalCode = init.postalCode;\n        this.city = init.city;\n        this.region = init.region;\n        this.country = init.country;\n        this.countryName = init.countryName;\n        this.continent = init.continent;\n        this.continentName = init.continentName;\n        this.asn = init.asn;\n        this.asnName = init.asnName;\n        this.asnDomain = init.asnDomain;\n        this.asnType = init.asnType;\n        this.asnCountry = init.asnCountry;\n        this.service = init.service;\n        // TypeScript creates symbols on the class when using `private` or `#`\n        // identifiers for tracking these properties. We don't want to end up with\n        // the same issues as Next.js with private symbols so we use\n        // `Object.defineProperties` here and then `@ts-expect-error` when we access\n        // the values. This is mostly to improve the editor experience, as props\n        // starting with `_` are sorted to the top of autocomplete.\n        Object.defineProperties(this, {\n            _isHosting: {\n                configurable: false,\n                enumerable: false,\n                writable: false,\n                value: init.isHosting ?? false,\n            },\n            _isVpn: {\n                configurable: false,\n                enumerable: false,\n                writable: false,\n                value: init.isVpn ?? false,\n            },\n            _isProxy: {\n                configurable: false,\n                enumerable: false,\n                writable: false,\n                value: init.isProxy ?? false,\n            },\n            _isTor: {\n                configurable: false,\n                enumerable: false,\n                writable: false,\n                value: init.isTor ?? false,\n            },\n            _isRelay: {\n                configurable: false,\n                enumerable: false,\n                writable: false,\n                value: init.isRelay ?? false,\n            },\n        });\n    }\n    hasLatitude() {\n        return typeof this.latitude !== \"undefined\";\n    }\n    hasLongitude() {\n        return typeof this.longitude !== \"undefined\";\n    }\n    hasAccuracyRadius() {\n        return typeof this.accuracyRadius !== \"undefined\";\n    }\n    hasTimezone() {\n        return typeof this.timezone !== \"undefined\";\n    }\n    hasPostalCode() {\n        return typeof this.postalCode !== \"undefined\";\n    }\n    // TODO: If we have city, what other data are we sure to have?\n    hasCity() {\n        return typeof this.city !== \"undefined\";\n    }\n    // TODO: If we have region, what other data are we sure to have?\n    hasRegion() {\n        return typeof this.region !== \"undefined\";\n    }\n    // If we have country, we should have country name\n    // TODO: If we have country, should we also have continent?\n    hasCountry() {\n        return typeof this.country !== \"undefined\";\n    }\n    // If we have continent, we should have continent name\n    hasContintent() {\n        return typeof this.continent !== \"undefined\";\n    }\n    // If we have ASN, we should have every piece of ASN information.\n    hasASN() {\n        return typeof this.asn !== \"undefined\";\n    }\n    hasService() {\n        return typeof this.service !== \"undefined\";\n    }\n    /**\n     * @returns `true` if the IP address belongs to a hosting provider.\n     */\n    isHosting() {\n        // @ts-expect-error because we attach this with Object.defineProperties\n        return this._isHosting;\n    }\n    /**\n     * @returns `true` if the IP address belongs to a VPN provider.\n     */\n    isVpn() {\n        // @ts-expect-error because we attach this with Object.defineProperties\n        return this._isVpn;\n    }\n    /**\n     * @returns `true` if the IP address belongs to a proxy provider.\n     */\n    isProxy() {\n        // @ts-expect-error because we attach this with Object.defineProperties\n        return this._isProxy;\n    }\n    /**\n     * @returns `true` if the IP address belongs to a Tor node.\n     */\n    isTor() {\n        // @ts-expect-error because we attach this with Object.defineProperties\n        return this._isTor;\n    }\n    /**\n     * @returns `true` if the the IP address belongs to a relay service.\n     */\n    isRelay() {\n        // @ts-expect-error because we attach this with Object.defineProperties\n        return this._isRelay;\n    }\n}\n/**\n * Represents a decision returned by the Arcjet SDK.\n *\n * @property `id` - The unique ID of the decision. This can be used to look up\n * the decision in the Arcjet dashboard.\n * @property `conclusion` - Arcjet's conclusion about the request. This will be\n * one of `\"ALLOW\"`, `\"DENY\"`, `\"CHALLENGE\"`, or `\"ERROR\"`.\n * @property `reason` - A structured data type about the reason for the\n * decision. One of: {@link ArcjetRateLimitReason}, {@link ArcjetEdgeRuleReason},\n * {@link ArcjetBotReason}, {@link ArcjetShieldReason},\n * {@link ArcjetEmailReason}, or {@link ArcjetErrorReason}.\n * @property `ttl` - The duration in milliseconds this decision should be\n * considered valid, also known as time-to-live.\n * @property `results` - Each separate {@link ArcjetRuleResult} can be found here\n * or by logging into the Arcjet dashboard and searching for the decision `id`.\n */\nclass ArcjetDecision {\n    id;\n    /**\n     * The duration in milliseconds this decision should be considered valid, also\n     * known as time-to-live.\n     */\n    ttl;\n    results;\n    /**\n     * Details about the IP address that informed the `conclusion`.\n     */\n    ip;\n    constructor(init) {\n        if (typeof init.id === \"string\") {\n            this.id = init.id;\n        }\n        else {\n            this.id = typeid(\"lreq\").toString();\n        }\n        this.results = init.results;\n        this.ttl = init.ttl;\n        this.ip = init.ip ?? new ArcjetIpDetails();\n    }\n    isAllowed() {\n        return this.conclusion === \"ALLOW\" || this.conclusion === \"ERROR\";\n    }\n    isDenied() {\n        return this.conclusion === \"DENY\";\n    }\n    isChallenged() {\n        return this.conclusion === \"CHALLENGE\";\n    }\n    isErrored() {\n        return this.conclusion === \"ERROR\";\n    }\n}\nclass ArcjetAllowDecision extends ArcjetDecision {\n    conclusion = \"ALLOW\";\n    reason;\n    constructor(init) {\n        super(init);\n        this.reason = init.reason;\n    }\n}\nclass ArcjetDenyDecision extends ArcjetDecision {\n    conclusion = \"DENY\";\n    reason;\n    constructor(init) {\n        super(init);\n        this.reason = init.reason;\n    }\n}\nclass ArcjetChallengeDecision extends ArcjetDecision {\n    conclusion = \"CHALLENGE\";\n    reason;\n    constructor(init) {\n        super(init);\n        this.reason = init.reason;\n    }\n}\nclass ArcjetErrorDecision extends ArcjetDecision {\n    conclusion = \"ERROR\";\n    reason;\n    constructor(init) {\n        super(init);\n        this.reason = init.reason;\n    }\n}\n\nexport { ArcjetAllowDecision, ArcjetBotReason, ArcjetChallengeDecision, ArcjetConclusion, ArcjetDecision, ArcjetDenyDecision, ArcjetEdgeRuleReason, ArcjetEmailReason, ArcjetEmailType, ArcjetErrorDecision, ArcjetErrorReason, ArcjetIpDetails, ArcjetMode, ArcjetRateLimitAlgorithm, ArcjetRateLimitReason, ArcjetReason, ArcjetRuleResult, ArcjetRuleState, ArcjetRuleType, ArcjetSensitiveInfoReason, ArcjetSensitiveInfoType, ArcjetShieldReason, ArcjetStack };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa,OAAO,MAAM,CAAC;IAC7B;;MAEE,GACF,MAAM;IACN;;MAEE,GACF,SAAS;AACb;AACA,MAAM,2BAA2B,OAAO,MAAM,CAAC;IAC3C;;MAEE,GACF,cAAc;IACd;;MAEE,GACF,cAAc;IACd;;MAEE,GACF,gBAAgB;AACpB;AACA,MAAM,kBAAkB,OAAO,MAAM,CAAC;IAClC;;MAEE,GACF,YAAY;IACZ;;MAEE,GACF,MAAM;IACN;;MAEE,GACF,eAAe;IACf;;MAEE,GACF,aAAa;IACb;;MAEE,GACF,SAAS;AACb;AACA,MAAM,cAAc,OAAO,MAAM,CAAC;IAC9B;;MAEE,GACF,QAAQ;IACR;;MAEE,GACF,QAAQ;IACR;;MAEE,GACF,KAAK;IACL;;MAEE,GACF,WAAW;IACX;;MAEE,GACF,MAAM;IACN;;MAEE,GACF,QAAQ;IACR;;MAEE,GACF,OAAO;IACP;;MAEE,GACF,OAAO;AACX;AACA,MAAM,kBAAkB,OAAO,MAAM,CAAC;IAClC;;MAEE,GACF,KAAK;IACL;;MAEE,GACF,SAAS;IACT;;MAEE,GACF,QAAQ;IACR;;MAEE,GACF,SAAS;AACb;AACA,MAAM,mBAAmB,OAAO,MAAM,CAAC;IACnC;;MAEE,GACF,OAAO;IACP;;MAEE,GACF,MAAM;IACN;;MAEE,GACF,WAAW;IACX;;MAEE,GACF,OAAO;AACX;AACA,MAAM,0BAA0B,OAAO,MAAM,CAAC;IAC1C;;MAEE,GACF,OAAO;IACP;;MAEE,GACF,cAAc;IACd;;MAEE,GACF,YAAY;IACZ;;MAEE,GACF,oBAAoB;AACxB;AACA,MAAM,iBAAiB,OAAO,MAAM,CAAC;IACjC;;MAEE,GACF,OAAO;IACP;;MAEE,GACF,QAAQ;AACZ;AACA,MAAM;IACF,KAAK;IACL,kBAAkB;QACd,OAAO,IAAI,CAAC,IAAI,KAAK;IACzB;IACA,cAAc;QACV,OAAO,IAAI,CAAC,IAAI,KAAK;IACzB;IACA,QAAQ;QACJ,OAAO,IAAI,CAAC,IAAI,KAAK;IACzB;IACA,aAAa;QACT,OAAO,IAAI,CAAC,IAAI,KAAK;IACzB;IACA,WAAW;QACP,OAAO,IAAI,CAAC,IAAI,KAAK;IACzB;IACA,UAAU;QACN,OAAO,IAAI,CAAC,IAAI,KAAK;IACzB;IACA,UAAU;QACN,OAAO,IAAI,CAAC,IAAI,KAAK;IACzB;AACJ;AACA,MAAM,kCAAkC;IACpC,OAAO,iBAAiB;IACxB,OAAO;IACP,QAAQ;IACR,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;IAC/B;AACJ;AACA,MAAM,8BAA8B;IAChC,OAAO,aAAa;IACpB,IAAI;IACJ,UAAU;IACV,MAAM;IACN,OAAO;IACP,UAAU;IACV,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;QACnB,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;QAC/B,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;IACnC;AACJ;AACA,MAAM,wBAAwB;IAC1B,OAAO,MAAM;IACb,QAAQ;IACR,OAAO;IACP,SAAS;IACT,QAAQ;IACR,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;QAC7B,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;IAC/B;IACA,aAAa;QACT,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,YAAY;QACR,OAAO,IAAI,CAAC,OAAO;IACvB;AACJ;AACA,MAAM,6BAA6B;IAC/B,OAAO,YAAY;AACvB;AACA,MAAM,2BAA2B;IAC7B,OAAO,SAAS;IAChB,gBAAgB;IAChB,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,CAAC,eAAe,GAAG,KAAK,eAAe,IAAI;IACnD;AACJ;AACA,MAAM,0BAA0B;IAC5B,OAAO,QAAQ;IACf,WAAW;IACX,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,OAAO,SAAS,aAAa;YAC7B,IAAI,CAAC,UAAU,GAAG,EAAE;QACxB,OACK;YACD,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU,IAAI,EAAE;QAC3C;IACJ;AACJ;AACA,MAAM,0BAA0B;IAC5B,OAAO,QAAQ;IACf,QAAQ;IACR,YAAY,KAAK,CAAE;QACf,KAAK;QACL,oCAAoC;QACpC,IAAI,iBAAiB,8PAAA,CAAA,SAAM,EAAE;YACzB,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,SAAS;gBAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO;gBACzC;YACJ,OACK;gBACD,IAAI,CAAC,OAAO,GAAG;YACnB;QACJ;QACA,oCAAoC;QACpC,IAAI,iBAAiB,OAAO;YACxB,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YAC5B;QACJ;QACA,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,OAAO,GAAG;YACf;QACJ;QACA,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACA,MAAM;IACF;;;KAGC,GACD,OAAO;IACP;;;KAGC,GACD,IAAI;IACJ,MAAM;IACN,WAAW;IACX,OAAO;IACP,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;QACnB,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK;QACvB,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;QACjC,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IAC7B;IACA,WAAW;QACP,OAAO,IAAI,CAAC,UAAU,KAAK;IAC/B;AACJ;AACA,MAAM;IACF;;;KAGC,GACD,SAAS;IACT;;;KAGC,GACD,UAAU;IACV;;KAEC,GACD,eAAe;IACf;;KAEC,GACD,SAAS;IACT;;KAEC,GACD,WAAW;IACX;;KAEC,GACD,KAAK;IACL;;KAEC,GACD,OAAO;IACP;;KAEC,GACD,QAAQ;IACR;;KAEC,GACD,YAAY;IACZ;;KAEC,GACD,UAAU;IACV;;KAEC,GACD,cAAc;IACd;;KAEC,GACD,IAAI;IACJ;;KAEC,GACD,QAAQ;IACR;;KAEC,GACD,UAAU;IACV;;KAEC,GACD,QAAQ;IACR;;KAEC,GACD,WAAW;IACX;;KAEC,GACD,QAAQ;IACR,YAAY,OAAO,CAAC,CAAC,CAAE;QACnB,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;QAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;QAC/B,IAAI,CAAC,cAAc,GAAG,KAAK,cAAc;QACzC,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;QAC7B,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;QACjC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACrB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,IAAI,CAAC,WAAW,GAAG,KAAK,WAAW;QACnC,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;QAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,aAAa;QACvC,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;QAC/B,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;QACjC,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,sEAAsE;QACtE,0EAA0E;QAC1E,4DAA4D;QAC5D,4EAA4E;QAC5E,wEAAwE;QACxE,2DAA2D;QAC3D,OAAO,gBAAgB,CAAC,IAAI,EAAE;YAC1B,YAAY;gBACR,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,OAAO,KAAK,SAAS,IAAI;YAC7B;YACA,QAAQ;gBACJ,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,OAAO,KAAK,KAAK,IAAI;YACzB;YACA,UAAU;gBACN,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,OAAO,KAAK,OAAO,IAAI;YAC3B;YACA,QAAQ;gBACJ,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,OAAO,KAAK,KAAK,IAAI;YACzB;YACA,UAAU;gBACN,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,OAAO,KAAK,OAAO,IAAI;YAC3B;QACJ;IACJ;IACA,cAAc;QACV,OAAO,OAAO,IAAI,CAAC,QAAQ,KAAK;IACpC;IACA,eAAe;QACX,OAAO,OAAO,IAAI,CAAC,SAAS,KAAK;IACrC;IACA,oBAAoB;QAChB,OAAO,OAAO,IAAI,CAAC,cAAc,KAAK;IAC1C;IACA,cAAc;QACV,OAAO,OAAO,IAAI,CAAC,QAAQ,KAAK;IACpC;IACA,gBAAgB;QACZ,OAAO,OAAO,IAAI,CAAC,UAAU,KAAK;IACtC;IACA,8DAA8D;IAC9D,UAAU;QACN,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK;IAChC;IACA,gEAAgE;IAChE,YAAY;QACR,OAAO,OAAO,IAAI,CAAC,MAAM,KAAK;IAClC;IACA,kDAAkD;IAClD,2DAA2D;IAC3D,aAAa;QACT,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK;IACnC;IACA,sDAAsD;IACtD,gBAAgB;QACZ,OAAO,OAAO,IAAI,CAAC,SAAS,KAAK;IACrC;IACA,iEAAiE;IACjE,SAAS;QACL,OAAO,OAAO,IAAI,CAAC,GAAG,KAAK;IAC/B;IACA,aAAa;QACT,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK;IACnC;IACA;;KAEC,GACD,YAAY;QACR,uEAAuE;QACvE,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA;;KAEC,GACD,QAAQ;QACJ,uEAAuE;QACvE,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;KAEC,GACD,UAAU;QACN,uEAAuE;QACvE,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA;;KAEC,GACD,QAAQ;QACJ,uEAAuE;QACvE,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;KAEC,GACD,UAAU;QACN,uEAAuE;QACvE,OAAO,IAAI,CAAC,QAAQ;IACxB;AACJ;AACA;;;;;;;;;;;;;;;CAeC,GACD,MAAM;IACF,GAAG;IACH;;;KAGC,GACD,IAAI;IACJ,QAAQ;IACR;;KAEC,GACD,GAAG;IACH,YAAY,IAAI,CAAE;QACd,IAAI,OAAO,KAAK,EAAE,KAAK,UAAU;YAC7B,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE;QACrB,OACK;YACD,IAAI,CAAC,EAAE,GAAG,CAAA,GAAA,uMAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,QAAQ;QACrC;QACA,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;QACnB,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE,IAAI,IAAI;IAC7B;IACA,YAAY;QACR,OAAO,IAAI,CAAC,UAAU,KAAK,WAAW,IAAI,CAAC,UAAU,KAAK;IAC9D;IACA,WAAW;QACP,OAAO,IAAI,CAAC,UAAU,KAAK;IAC/B;IACA,eAAe;QACX,OAAO,IAAI,CAAC,UAAU,KAAK;IAC/B;IACA,YAAY;QACR,OAAO,IAAI,CAAC,UAAU,KAAK;IAC/B;AACJ;AACA,MAAM,4BAA4B;IAC9B,aAAa,QAAQ;IACrB,OAAO;IACP,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IAC7B;AACJ;AACA,MAAM,2BAA2B;IAC7B,aAAa,OAAO;IACpB,OAAO;IACP,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IAC7B;AACJ;AACA,MAAM,gCAAgC;IAClC,aAAa,YAAY;IACzB,OAAO;IACP,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IAC7B;AACJ;AACA,MAAM,4BAA4B;IAC9B,aAAa,QAAQ;IACrB,OAAO;IACP,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40arcjet%2Bprotocol%401.0.0-beta.7/node_modules/%40arcjet/protocol/convert.js"], "sourcesContent": ["import { Timestamp } from '@bufbuild/protobuf';\nimport { ArcjetErrorDecision, ArcjetIpDetails, ArcjetErrorReason, ArcjetChallengeDecision, ArcjetDenyDecision, ArcjetAllowDecision, ArcjetRuleResult, ArcjetReason, ArcjetSensitiveInfoReason, ArcjetEmailReason, ArcjetShieldReason, ArcjetEdgeRuleReason, ArcjetBotReason, ArcjetRateLimitReason } from './index.js';\nimport { SDKStack, Decision, Rule, RateLimitAlgorithm, Conclusion, Reason, RateLimitReason, BotV2Reason, EdgeRuleReason, ShieldReason, EmailReason, ErrorReason, SensitiveInfoReason, RuleResult, Mode, EmailType, RuleState } from './proto/decide/v1alpha1/decide_pb.js';\n\nfunction ArcjetModeToProtocol(mode) {\n    switch (mode) {\n        case \"LIVE\":\n            return Mode.LIVE;\n        case \"DRY_RUN\":\n            return Mode.DRY_RUN;\n        default: {\n            return Mode.UNSPECIFIED;\n        }\n    }\n}\nfunction ArcjetEmailTypeToProtocol(emailType) {\n    switch (emailType) {\n        case \"DISPOSABLE\":\n            return EmailType.DISPOSABLE;\n        case \"FREE\":\n            return EmailType.FREE;\n        case \"NO_MX_RECORDS\":\n            return EmailType.NO_MX_RECORDS;\n        case \"NO_GRAVATAR\":\n            return EmailType.NO_GRAVATAR;\n        case \"INVALID\":\n            return EmailType.INVALID;\n        default: {\n            return EmailType.UNSPECIFIED;\n        }\n    }\n}\nfunction ArcjetEmailTypeFromProtocol(emailType) {\n    switch (emailType) {\n        case EmailType.UNSPECIFIED:\n            throw new Error(\"Invalid EmailType\");\n        case EmailType.DISPOSABLE:\n            return \"DISPOSABLE\";\n        case EmailType.FREE:\n            return \"FREE\";\n        case EmailType.NO_MX_RECORDS:\n            return \"NO_MX_RECORDS\";\n        case EmailType.NO_GRAVATAR:\n            return \"NO_GRAVATAR\";\n        case EmailType.INVALID:\n            return \"INVALID\";\n        default: {\n            throw new Error(\"Invalid EmailType\");\n        }\n    }\n}\nfunction ArcjetStackToProtocol(stack) {\n    switch (stack) {\n        case \"NODEJS\":\n            return SDKStack.SDK_STACK_NODEJS;\n        case \"NEXTJS\":\n            return SDKStack.SDK_STACK_NEXTJS;\n        case \"BUN\":\n            return SDKStack.SDK_STACK_BUN;\n        case \"SVELTEKIT\":\n            return SDKStack.SDK_STACK_SVELTEKIT;\n        case \"DENO\":\n            return SDKStack.SDK_STACK_DENO;\n        case \"NESTJS\":\n            return SDKStack.SDK_STACK_NESTJS;\n        case \"REMIX\":\n            return SDKStack.SDK_STACK_REMIX;\n        case \"ASTRO\":\n            return SDKStack.SDK_STACK_ASTRO;\n        default: {\n            return SDKStack.SDK_STACK_UNSPECIFIED;\n        }\n    }\n}\nfunction ArcjetRuleStateToProtocol(stack) {\n    switch (stack) {\n        case \"RUN\":\n            return RuleState.RUN;\n        case \"NOT_RUN\":\n            return RuleState.NOT_RUN;\n        case \"CACHED\":\n            return RuleState.CACHED;\n        case \"DRY_RUN\":\n            return RuleState.DRY_RUN;\n        default: {\n            return RuleState.UNSPECIFIED;\n        }\n    }\n}\nfunction ArcjetRuleStateFromProtocol(ruleState) {\n    switch (ruleState) {\n        case RuleState.UNSPECIFIED:\n            throw new Error(\"Invalid RuleState\");\n        case RuleState.RUN:\n            return \"RUN\";\n        case RuleState.NOT_RUN:\n            return \"NOT_RUN\";\n        case RuleState.DRY_RUN:\n            return \"DRY_RUN\";\n        case RuleState.CACHED:\n            return \"CACHED\";\n        default: {\n            throw new Error(\"Invalid RuleState\");\n        }\n    }\n}\nfunction ArcjetConclusionToProtocol(conclusion) {\n    switch (conclusion) {\n        case \"ALLOW\":\n            return Conclusion.ALLOW;\n        case \"DENY\":\n            return Conclusion.DENY;\n        case \"CHALLENGE\":\n            return Conclusion.CHALLENGE;\n        case \"ERROR\":\n            return Conclusion.ERROR;\n        default: {\n            return Conclusion.UNSPECIFIED;\n        }\n    }\n}\nfunction ArcjetConclusionFromProtocol(conclusion) {\n    switch (conclusion) {\n        case Conclusion.UNSPECIFIED:\n            throw new Error(\"Invalid Conclusion\");\n        case Conclusion.ALLOW:\n            return \"ALLOW\";\n        case Conclusion.DENY:\n            return \"DENY\";\n        case Conclusion.CHALLENGE:\n            return \"CHALLENGE\";\n        case Conclusion.ERROR:\n            return \"ERROR\";\n        default: {\n            throw new Error(\"Invalid Conclusion\");\n        }\n    }\n}\nfunction ArcjetReasonFromProtocol(proto) {\n    if (typeof proto === \"undefined\") {\n        return new ArcjetReason();\n    }\n    if (typeof proto !== \"object\" || typeof proto.reason !== \"object\") {\n        throw new Error(\"Invalid Reason\");\n    }\n    switch (proto.reason.case) {\n        case \"rateLimit\": {\n            const reason = proto.reason.value;\n            return new ArcjetRateLimitReason({\n                max: reason.max,\n                remaining: reason.remaining,\n                reset: reason.resetInSeconds,\n                window: reason.windowInSeconds,\n                resetTime: reason.resetTime?.toDate(),\n            });\n        }\n        case \"botV2\": {\n            const reason = proto.reason.value;\n            return new ArcjetBotReason({\n                allowed: reason.allowed,\n                denied: reason.denied,\n                verified: reason.verified,\n                spoofed: reason.spoofed,\n            });\n        }\n        case \"edgeRule\": {\n            return new ArcjetEdgeRuleReason();\n        }\n        case \"shield\": {\n            const reason = proto.reason.value;\n            return new ArcjetShieldReason({\n                shieldTriggered: reason.shieldTriggered,\n            });\n        }\n        case \"email\": {\n            const reason = proto.reason.value;\n            return new ArcjetEmailReason({\n                emailTypes: reason.emailTypes.map(ArcjetEmailTypeFromProtocol),\n            });\n        }\n        case \"sensitiveInfo\": {\n            const reason = proto.reason.value;\n            return new ArcjetSensitiveInfoReason({\n                allowed: reason.allowed,\n                denied: reason.denied,\n            });\n        }\n        case \"bot\": {\n            return new ArcjetErrorReason(\"bot detection v1 is deprecated\");\n        }\n        case \"error\": {\n            const reason = proto.reason.value;\n            return new ArcjetErrorReason(reason.message);\n        }\n        case undefined: {\n            return new ArcjetReason();\n        }\n        default: {\n            proto.reason;\n            return new ArcjetReason();\n        }\n    }\n}\nfunction ArcjetReasonToProtocol(reason) {\n    if (reason.isRateLimit()) {\n        return new Reason({\n            reason: {\n                case: \"rateLimit\",\n                value: new RateLimitReason({\n                    max: reason.max,\n                    remaining: reason.remaining,\n                    resetInSeconds: reason.reset,\n                    windowInSeconds: reason.window,\n                    resetTime: reason.resetTime\n                        ? Timestamp.fromDate(reason.resetTime)\n                        : undefined,\n                }),\n            },\n        });\n    }\n    if (reason.isBot()) {\n        return new Reason({\n            reason: {\n                case: \"botV2\",\n                value: new BotV2Reason({\n                    allowed: reason.allowed,\n                    denied: reason.denied,\n                    verified: reason.verified,\n                    spoofed: reason.spoofed,\n                }),\n            },\n        });\n    }\n    if (reason.isEdgeRule()) {\n        return new Reason({\n            reason: {\n                case: \"edgeRule\",\n                value: new EdgeRuleReason({}),\n            },\n        });\n    }\n    if (reason.isShield()) {\n        return new Reason({\n            reason: {\n                case: \"shield\",\n                value: new ShieldReason({\n                    shieldTriggered: reason.shieldTriggered,\n                }),\n            },\n        });\n    }\n    if (reason.isEmail()) {\n        return new Reason({\n            reason: {\n                case: \"email\",\n                value: new EmailReason({\n                    emailTypes: reason.emailTypes.map(ArcjetEmailTypeToProtocol),\n                }),\n            },\n        });\n    }\n    if (reason.isError()) {\n        return new Reason({\n            reason: {\n                case: \"error\",\n                value: new ErrorReason({\n                    message: reason.message,\n                }),\n            },\n        });\n    }\n    if (reason.isSensitiveInfo()) {\n        return new Reason({\n            reason: {\n                case: \"sensitiveInfo\",\n                value: new SensitiveInfoReason({\n                    allowed: reason.allowed,\n                    denied: reason.denied,\n                }),\n            },\n        });\n    }\n    return new Reason();\n}\nfunction ArcjetRuleResultToProtocol(ruleResult) {\n    return new RuleResult({\n        ruleId: ruleResult.ruleId,\n        ttl: ruleResult.ttl,\n        state: ArcjetRuleStateToProtocol(ruleResult.state),\n        conclusion: ArcjetConclusionToProtocol(ruleResult.conclusion),\n        reason: ArcjetReasonToProtocol(ruleResult.reason),\n    });\n}\nfunction ArcjetRuleResultFromProtocol(proto) {\n    return new ArcjetRuleResult({\n        ruleId: proto.ruleId,\n        ttl: proto.ttl,\n        state: ArcjetRuleStateFromProtocol(proto.state),\n        conclusion: ArcjetConclusionFromProtocol(proto.conclusion),\n        reason: ArcjetReasonFromProtocol(proto.reason),\n    });\n}\nfunction ArcjetDecisionToProtocol(decision) {\n    return new Decision({\n        id: decision.id,\n        ttl: decision.ttl,\n        conclusion: ArcjetConclusionToProtocol(decision.conclusion),\n        reason: ArcjetReasonToProtocol(decision.reason),\n        ruleResults: decision.results.map(ArcjetRuleResultToProtocol),\n    });\n}\nfunction ArcjetIpDetailsFromProtocol(ipDetails) {\n    if (!ipDetails) {\n        return new ArcjetIpDetails();\n    }\n    // A default value from the Decide service means we don't have data for the\n    // field so we translate to `undefined`. Some fields have interconnected logic\n    // that determines if the default value can be provided to users.\n    return new ArcjetIpDetails({\n        // If we have a non-0 latitude, or a 0 latitude with a non-0 accuracy radius\n        // then we have a latitude from the Decide service\n        latitude: ipDetails.latitude || ipDetails.accuracyRadius\n            ? ipDetails.latitude\n            : undefined,\n        // If we have a non-0 longitude, or a 0 longitude with a non-0 accuracy\n        // radius then we have a longitude from the Decide service\n        longitude: ipDetails.longitude || ipDetails.accuracyRadius\n            ? ipDetails.longitude\n            : undefined,\n        // If we have a non-0 latitude/longitude/accuracyRadius, we assume that the\n        // accuracyRadius value was set\n        accuracyRadius: ipDetails.longitude || ipDetails.latitude || ipDetails.accuracyRadius\n            ? ipDetails.accuracyRadius\n            : undefined,\n        timezone: ipDetails.timezone !== \"\" ? ipDetails.timezone : undefined,\n        postalCode: ipDetails.postalCode !== \"\" ? ipDetails.postalCode : undefined,\n        city: ipDetails.city !== \"\" ? ipDetails.city : undefined,\n        region: ipDetails.region !== \"\" ? ipDetails.region : undefined,\n        country: ipDetails.country !== \"\" ? ipDetails.country : undefined,\n        countryName: ipDetails.countryName !== \"\" ? ipDetails.countryName : undefined,\n        continent: ipDetails.continent !== \"\" ? ipDetails.continent : undefined,\n        continentName: ipDetails.continentName !== \"\" ? ipDetails.continentName : undefined,\n        asn: ipDetails.asn !== \"\" ? ipDetails.asn : undefined,\n        asnName: ipDetails.asnName !== \"\" ? ipDetails.asnName : undefined,\n        asnDomain: ipDetails.asnDomain !== \"\" ? ipDetails.asnDomain : undefined,\n        asnType: ipDetails.asnType !== \"\" ? ipDetails.asnType : undefined,\n        asnCountry: ipDetails.asnCountry !== \"\" ? ipDetails.asnCountry : undefined,\n        service: ipDetails.service !== \"\" ? ipDetails.service : undefined,\n        isHosting: ipDetails.isHosting,\n        isVpn: ipDetails.isVpn,\n        isProxy: ipDetails.isProxy,\n        isTor: ipDetails.isTor,\n        isRelay: ipDetails.isRelay,\n    });\n}\nfunction ArcjetDecisionFromProtocol(decision) {\n    if (typeof decision === \"undefined\") {\n        return new ArcjetErrorDecision({\n            reason: new ArcjetErrorReason(\"Missing Decision\"),\n            ttl: 0,\n            results: [],\n            ip: new ArcjetIpDetails(),\n        });\n    }\n    const results = Array.isArray(decision.ruleResults)\n        ? decision.ruleResults.map(ArcjetRuleResultFromProtocol)\n        : [];\n    switch (decision.conclusion) {\n        case Conclusion.ALLOW:\n            return new ArcjetAllowDecision({\n                id: decision.id,\n                ttl: decision.ttl,\n                reason: ArcjetReasonFromProtocol(decision.reason),\n                results,\n                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails),\n            });\n        case Conclusion.DENY:\n            return new ArcjetDenyDecision({\n                id: decision.id,\n                ttl: decision.ttl,\n                reason: ArcjetReasonFromProtocol(decision.reason),\n                results,\n                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails),\n            });\n        case Conclusion.CHALLENGE:\n            return new ArcjetChallengeDecision({\n                id: decision.id,\n                ttl: decision.ttl,\n                reason: ArcjetReasonFromProtocol(decision.reason),\n                results,\n                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails),\n            });\n        case Conclusion.ERROR:\n            return new ArcjetErrorDecision({\n                id: decision.id,\n                ttl: decision.ttl,\n                reason: new ArcjetErrorReason(decision.reason),\n                results,\n                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails),\n            });\n        case Conclusion.UNSPECIFIED:\n            return new ArcjetErrorDecision({\n                id: decision.id,\n                ttl: decision.ttl,\n                reason: new ArcjetErrorReason(\"Invalid Conclusion\"),\n                results,\n                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails),\n            });\n        default: {\n            decision.conclusion;\n            return new ArcjetErrorDecision({\n                ttl: 0,\n                reason: new ArcjetErrorReason(\"Missing Conclusion\"),\n                results: [],\n                ip: ArcjetIpDetailsFromProtocol(decision.ipDetails),\n            });\n        }\n    }\n}\nfunction isRateLimitRule(rule) {\n    return rule.type === \"RATE_LIMIT\";\n}\nfunction isTokenBucketRule(rule) {\n    return isRateLimitRule(rule) && rule.algorithm === \"TOKEN_BUCKET\";\n}\nfunction isFixedWindowRule(rule) {\n    return isRateLimitRule(rule) && rule.algorithm === \"FIXED_WINDOW\";\n}\nfunction isSlidingWindowRule(rule) {\n    return isRateLimitRule(rule) && rule.algorithm === \"SLIDING_WINDOW\";\n}\nfunction isBotRule(rule) {\n    return rule.type === \"BOT\";\n}\nfunction isEmailRule(rule) {\n    return rule.type === \"EMAIL\";\n}\nfunction isShieldRule(rule) {\n    return rule.type === \"SHIELD\";\n}\nfunction isSensitiveInfoRule(rule) {\n    return rule.type === \"SENSITIVE_INFO\";\n}\nfunction ArcjetRuleToProtocol(rule) {\n    if (isTokenBucketRule(rule)) {\n        return new Rule({\n            rule: {\n                case: \"rateLimit\",\n                value: {\n                    version: rule.version,\n                    mode: ArcjetModeToProtocol(rule.mode),\n                    characteristics: rule.characteristics,\n                    algorithm: RateLimitAlgorithm.TOKEN_BUCKET,\n                    refillRate: rule.refillRate,\n                    interval: rule.interval,\n                    capacity: rule.capacity,\n                },\n            },\n        });\n    }\n    if (isFixedWindowRule(rule)) {\n        return new Rule({\n            rule: {\n                case: \"rateLimit\",\n                value: {\n                    version: rule.version,\n                    mode: ArcjetModeToProtocol(rule.mode),\n                    characteristics: rule.characteristics,\n                    algorithm: RateLimitAlgorithm.FIXED_WINDOW,\n                    max: rule.max,\n                    windowInSeconds: rule.window,\n                },\n            },\n        });\n    }\n    if (isSlidingWindowRule(rule)) {\n        return new Rule({\n            rule: {\n                case: \"rateLimit\",\n                value: {\n                    version: rule.version,\n                    mode: ArcjetModeToProtocol(rule.mode),\n                    characteristics: rule.characteristics,\n                    algorithm: RateLimitAlgorithm.SLIDING_WINDOW,\n                    max: rule.max,\n                    interval: rule.interval,\n                },\n            },\n        });\n    }\n    if (isEmailRule(rule)) {\n        const allow = Array.isArray(rule.allow)\n            ? rule.allow.map(ArcjetEmailTypeToProtocol)\n            : [];\n        const deny = Array.isArray(rule.deny)\n            ? rule.deny.map(ArcjetEmailTypeToProtocol)\n            : [];\n        return new Rule({\n            rule: {\n                case: \"email\",\n                value: {\n                    version: rule.version,\n                    mode: ArcjetModeToProtocol(rule.mode),\n                    allow,\n                    deny,\n                    requireTopLevelDomain: rule.requireTopLevelDomain,\n                    allowDomainLiteral: rule.allowDomainLiteral,\n                },\n            },\n        });\n    }\n    if (isBotRule(rule)) {\n        const allow = Array.isArray(rule.allow) ? rule.allow : [];\n        const deny = Array.isArray(rule.deny) ? rule.deny : [];\n        return new Rule({\n            rule: {\n                case: \"botV2\",\n                value: {\n                    version: rule.version,\n                    mode: ArcjetModeToProtocol(rule.mode),\n                    allow,\n                    deny,\n                },\n            },\n        });\n    }\n    if (isShieldRule(rule)) {\n        return new Rule({\n            rule: {\n                case: \"shield\",\n                value: {\n                    version: rule.version,\n                    mode: ArcjetModeToProtocol(rule.mode),\n                    autoAdded: false,\n                },\n            },\n        });\n    }\n    if (isSensitiveInfoRule(rule)) {\n        return new Rule({\n            rule: {\n                case: \"sensitiveInfo\",\n                value: {\n                    version: rule.version,\n                    mode: ArcjetModeToProtocol(rule.mode),\n                    allow: rule.allow,\n                    deny: rule.deny,\n                },\n            },\n        });\n    }\n    return new Rule();\n}\n\nexport { ArcjetConclusionFromProtocol, ArcjetConclusionToProtocol, ArcjetDecisionFromProtocol, ArcjetDecisionToProtocol, ArcjetEmailTypeFromProtocol, ArcjetEmailTypeToProtocol, ArcjetIpDetailsFromProtocol, ArcjetModeToProtocol, ArcjetReasonFromProtocol, ArcjetReasonToProtocol, ArcjetRuleResultFromProtocol, ArcjetRuleResultToProtocol, ArcjetRuleStateFromProtocol, ArcjetRuleStateToProtocol, ArcjetRuleToProtocol, ArcjetStackToProtocol, isRateLimitRule };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AAAA;AACA;;;;AAEA,SAAS,qBAAqB,IAAI;IAC9B,OAAQ;QACJ,KAAK;YACD,OAAO,8PAAA,CAAA,OAAI,CAAC,IAAI;QACpB,KAAK;YACD,OAAO,8PAAA,CAAA,OAAI,CAAC,OAAO;QACvB;YAAS;gBACL,OAAO,8PAAA,CAAA,OAAI,CAAC,WAAW;YAC3B;IACJ;AACJ;AACA,SAAS,0BAA0B,SAAS;IACxC,OAAQ;QACJ,KAAK;YACD,OAAO,8PAAA,CAAA,YAAS,CAAC,UAAU;QAC/B,KAAK;YACD,OAAO,8PAAA,CAAA,YAAS,CAAC,IAAI;QACzB,KAAK;YACD,OAAO,8PAAA,CAAA,YAAS,CAAC,aAAa;QAClC,KAAK;YACD,OAAO,8PAAA,CAAA,YAAS,CAAC,WAAW;QAChC,KAAK;YACD,OAAO,8PAAA,CAAA,YAAS,CAAC,OAAO;QAC5B;YAAS;gBACL,OAAO,8PAAA,CAAA,YAAS,CAAC,WAAW;YAChC;IACJ;AACJ;AACA,SAAS,4BAA4B,SAAS;IAC1C,OAAQ;QACJ,KAAK,8PAAA,CAAA,YAAS,CAAC,WAAW;YACtB,MAAM,IAAI,MAAM;QACpB,KAAK,8PAAA,CAAA,YAAS,CAAC,UAAU;YACrB,OAAO;QACX,KAAK,8PAAA,CAAA,YAAS,CAAC,IAAI;YACf,OAAO;QACX,KAAK,8PAAA,CAAA,YAAS,CAAC,aAAa;YACxB,OAAO;QACX,KAAK,8PAAA,CAAA,YAAS,CAAC,WAAW;YACtB,OAAO;QACX,KAAK,8PAAA,CAAA,YAAS,CAAC,OAAO;YAClB,OAAO;QACX;YAAS;gBACL,MAAM,IAAI,MAAM;YACpB;IACJ;AACJ;AACA,SAAS,sBAAsB,KAAK;IAChC,OAAQ;QACJ,KAAK;YACD,OAAO,8PAAA,CAAA,WAAQ,CAAC,gBAAgB;QACpC,KAAK;YACD,OAAO,8PAAA,CAAA,WAAQ,CAAC,gBAAgB;QACpC,KAAK;YACD,OAAO,8PAAA,CAAA,WAAQ,CAAC,aAAa;QACjC,KAAK;YACD,OAAO,8PAAA,CAAA,WAAQ,CAAC,mBAAmB;QACvC,KAAK;YACD,OAAO,8PAAA,CAAA,WAAQ,CAAC,cAAc;QAClC,KAAK;YACD,OAAO,8PAAA,CAAA,WAAQ,CAAC,gBAAgB;QACpC,KAAK;YACD,OAAO,8PAAA,CAAA,WAAQ,CAAC,eAAe;QACnC,KAAK;YACD,OAAO,8PAAA,CAAA,WAAQ,CAAC,eAAe;QACnC;YAAS;gBACL,OAAO,8PAAA,CAAA,WAAQ,CAAC,qBAAqB;YACzC;IACJ;AACJ;AACA,SAAS,0BAA0B,KAAK;IACpC,OAAQ;QACJ,KAAK;YACD,OAAO,8PAAA,CAAA,YAAS,CAAC,GAAG;QACxB,KAAK;YACD,OAAO,8PAAA,CAAA,YAAS,CAAC,OAAO;QAC5B,KAAK;YACD,OAAO,8PAAA,CAAA,YAAS,CAAC,MAAM;QAC3B,KAAK;YACD,OAAO,8PAAA,CAAA,YAAS,CAAC,OAAO;QAC5B;YAAS;gBACL,OAAO,8PAAA,CAAA,YAAS,CAAC,WAAW;YAChC;IACJ;AACJ;AACA,SAAS,4BAA4B,SAAS;IAC1C,OAAQ;QACJ,KAAK,8PAAA,CAAA,YAAS,CAAC,WAAW;YACtB,MAAM,IAAI,MAAM;QACpB,KAAK,8PAAA,CAAA,YAAS,CAAC,GAAG;YACd,OAAO;QACX,KAAK,8PAAA,CAAA,YAAS,CAAC,OAAO;YAClB,OAAO;QACX,KAAK,8PAAA,CAAA,YAAS,CAAC,OAAO;YAClB,OAAO;QACX,KAAK,8PAAA,CAAA,YAAS,CAAC,MAAM;YACjB,OAAO;QACX;YAAS;gBACL,MAAM,IAAI,MAAM;YACpB;IACJ;AACJ;AACA,SAAS,2BAA2B,UAAU;IAC1C,OAAQ;QACJ,KAAK;YACD,OAAO,8PAAA,CAAA,aAAU,CAAC,KAAK;QAC3B,KAAK;YACD,OAAO,8PAAA,CAAA,aAAU,CAAC,IAAI;QAC1B,KAAK;YACD,OAAO,8PAAA,CAAA,aAAU,CAAC,SAAS;QAC/B,KAAK;YACD,OAAO,8PAAA,CAAA,aAAU,CAAC,KAAK;QAC3B;YAAS;gBACL,OAAO,8PAAA,CAAA,aAAU,CAAC,WAAW;YACjC;IACJ;AACJ;AACA,SAAS,6BAA6B,UAAU;IAC5C,OAAQ;QACJ,KAAK,8PAAA,CAAA,aAAU,CAAC,WAAW;YACvB,MAAM,IAAI,MAAM;QACpB,KAAK,8PAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO;QACX,KAAK,8PAAA,CAAA,aAAU,CAAC,IAAI;YAChB,OAAO;QACX,KAAK,8PAAA,CAAA,aAAU,CAAC,SAAS;YACrB,OAAO;QACX,KAAK,8PAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO;QACX;YAAS;gBACL,MAAM,IAAI,MAAM;YACpB;IACJ;AACJ;AACA,SAAS,yBAAyB,KAAK;IACnC,IAAI,OAAO,UAAU,aAAa;QAC9B,OAAO,IAAI,2OAAA,CAAA,eAAY;IAC3B;IACA,IAAI,OAAO,UAAU,YAAY,OAAO,MAAM,MAAM,KAAK,UAAU;QAC/D,MAAM,IAAI,MAAM;IACpB;IACA,OAAQ,MAAM,MAAM,CAAC,IAAI;QACrB,KAAK;YAAa;gBACd,MAAM,SAAS,MAAM,MAAM,CAAC,KAAK;gBACjC,OAAO,IAAI,2OAAA,CAAA,wBAAqB,CAAC;oBAC7B,KAAK,OAAO,GAAG;oBACf,WAAW,OAAO,SAAS;oBAC3B,OAAO,OAAO,cAAc;oBAC5B,QAAQ,OAAO,eAAe;oBAC9B,WAAW,OAAO,SAAS,EAAE;gBACjC;YACJ;QACA,KAAK;YAAS;gBACV,MAAM,SAAS,MAAM,MAAM,CAAC,KAAK;gBACjC,OAAO,IAAI,2OAAA,CAAA,kBAAe,CAAC;oBACvB,SAAS,OAAO,OAAO;oBACvB,QAAQ,OAAO,MAAM;oBACrB,UAAU,OAAO,QAAQ;oBACzB,SAAS,OAAO,OAAO;gBAC3B;YACJ;QACA,KAAK;YAAY;gBACb,OAAO,IAAI,2OAAA,CAAA,uBAAoB;YACnC;QACA,KAAK;YAAU;gBACX,MAAM,SAAS,MAAM,MAAM,CAAC,KAAK;gBACjC,OAAO,IAAI,2OAAA,CAAA,qBAAkB,CAAC;oBAC1B,iBAAiB,OAAO,eAAe;gBAC3C;YACJ;QACA,KAAK;YAAS;gBACV,MAAM,SAAS,MAAM,MAAM,CAAC,KAAK;gBACjC,OAAO,IAAI,2OAAA,CAAA,oBAAiB,CAAC;oBACzB,YAAY,OAAO,UAAU,CAAC,GAAG,CAAC;gBACtC;YACJ;QACA,KAAK;YAAiB;gBAClB,MAAM,SAAS,MAAM,MAAM,CAAC,KAAK;gBACjC,OAAO,IAAI,2OAAA,CAAA,4BAAyB,CAAC;oBACjC,SAAS,OAAO,OAAO;oBACvB,QAAQ,OAAO,MAAM;gBACzB;YACJ;QACA,KAAK;YAAO;gBACR,OAAO,IAAI,2OAAA,CAAA,oBAAiB,CAAC;YACjC;QACA,KAAK;YAAS;gBACV,MAAM,SAAS,MAAM,MAAM,CAAC,KAAK;gBACjC,OAAO,IAAI,2OAAA,CAAA,oBAAiB,CAAC,OAAO,OAAO;YAC/C;QACA,KAAK;YAAW;gBACZ,OAAO,IAAI,2OAAA,CAAA,eAAY;YAC3B;QACA;YAAS;gBACL,MAAM,MAAM;gBACZ,OAAO,IAAI,2OAAA,CAAA,eAAY;YAC3B;IACJ;AACJ;AACA,SAAS,uBAAuB,MAAM;IAClC,IAAI,OAAO,WAAW,IAAI;QACtB,OAAO,IAAI,8PAAA,CAAA,SAAM,CAAC;YACd,QAAQ;gBACJ,MAAM;gBACN,OAAO,IAAI,8PAAA,CAAA,kBAAe,CAAC;oBACvB,KAAK,OAAO,GAAG;oBACf,WAAW,OAAO,SAAS;oBAC3B,gBAAgB,OAAO,KAAK;oBAC5B,iBAAiB,OAAO,MAAM;oBAC9B,WAAW,OAAO,SAAS,GACrB,+PAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,OAAO,SAAS,IACnC;gBACV;YACJ;QACJ;IACJ;IACA,IAAI,OAAO,KAAK,IAAI;QAChB,OAAO,IAAI,8PAAA,CAAA,SAAM,CAAC;YACd,QAAQ;gBACJ,MAAM;gBACN,OAAO,IAAI,8PAAA,CAAA,cAAW,CAAC;oBACnB,SAAS,OAAO,OAAO;oBACvB,QAAQ,OAAO,MAAM;oBACrB,UAAU,OAAO,QAAQ;oBACzB,SAAS,OAAO,OAAO;gBAC3B;YACJ;QACJ;IACJ;IACA,IAAI,OAAO,UAAU,IAAI;QACrB,OAAO,IAAI,8PAAA,CAAA,SAAM,CAAC;YACd,QAAQ;gBACJ,MAAM;gBACN,OAAO,IAAI,8PAAA,CAAA,iBAAc,CAAC,CAAC;YAC/B;QACJ;IACJ;IACA,IAAI,OAAO,QAAQ,IAAI;QACnB,OAAO,IAAI,8PAAA,CAAA,SAAM,CAAC;YACd,QAAQ;gBACJ,MAAM;gBACN,OAAO,IAAI,8PAAA,CAAA,eAAY,CAAC;oBACpB,iBAAiB,OAAO,eAAe;gBAC3C;YACJ;QACJ;IACJ;IACA,IAAI,OAAO,OAAO,IAAI;QAClB,OAAO,IAAI,8PAAA,CAAA,SAAM,CAAC;YACd,QAAQ;gBACJ,MAAM;gBACN,OAAO,IAAI,8PAAA,CAAA,cAAW,CAAC;oBACnB,YAAY,OAAO,UAAU,CAAC,GAAG,CAAC;gBACtC;YACJ;QACJ;IACJ;IACA,IAAI,OAAO,OAAO,IAAI;QAClB,OAAO,IAAI,8PAAA,CAAA,SAAM,CAAC;YACd,QAAQ;gBACJ,MAAM;gBACN,OAAO,IAAI,8PAAA,CAAA,cAAW,CAAC;oBACnB,SAAS,OAAO,OAAO;gBAC3B;YACJ;QACJ;IACJ;IACA,IAAI,OAAO,eAAe,IAAI;QAC1B,OAAO,IAAI,8PAAA,CAAA,SAAM,CAAC;YACd,QAAQ;gBACJ,MAAM;gBACN,OAAO,IAAI,8PAAA,CAAA,sBAAmB,CAAC;oBAC3B,SAAS,OAAO,OAAO;oBACvB,QAAQ,OAAO,MAAM;gBACzB;YACJ;QACJ;IACJ;IACA,OAAO,IAAI,8PAAA,CAAA,SAAM;AACrB;AACA,SAAS,2BAA2B,UAAU;IAC1C,OAAO,IAAI,8PAAA,CAAA,aAAU,CAAC;QAClB,QAAQ,WAAW,MAAM;QACzB,KAAK,WAAW,GAAG;QACnB,OAAO,0BAA0B,WAAW,KAAK;QACjD,YAAY,2BAA2B,WAAW,UAAU;QAC5D,QAAQ,uBAAuB,WAAW,MAAM;IACpD;AACJ;AACA,SAAS,6BAA6B,KAAK;IACvC,OAAO,IAAI,2OAAA,CAAA,mBAAgB,CAAC;QACxB,QAAQ,MAAM,MAAM;QACpB,KAAK,MAAM,GAAG;QACd,OAAO,4BAA4B,MAAM,KAAK;QAC9C,YAAY,6BAA6B,MAAM,UAAU;QACzD,QAAQ,yBAAyB,MAAM,MAAM;IACjD;AACJ;AACA,SAAS,yBAAyB,QAAQ;IACtC,OAAO,IAAI,8PAAA,CAAA,WAAQ,CAAC;QAChB,IAAI,SAAS,EAAE;QACf,KAAK,SAAS,GAAG;QACjB,YAAY,2BAA2B,SAAS,UAAU;QAC1D,QAAQ,uBAAuB,SAAS,MAAM;QAC9C,aAAa,SAAS,OAAO,CAAC,GAAG,CAAC;IACtC;AACJ;AACA,SAAS,4BAA4B,SAAS;IAC1C,IAAI,CAAC,WAAW;QACZ,OAAO,IAAI,2OAAA,CAAA,kBAAe;IAC9B;IACA,2EAA2E;IAC3E,8EAA8E;IAC9E,iEAAiE;IACjE,OAAO,IAAI,2OAAA,CAAA,kBAAe,CAAC;QACvB,4EAA4E;QAC5E,kDAAkD;QAClD,UAAU,UAAU,QAAQ,IAAI,UAAU,cAAc,GAClD,UAAU,QAAQ,GAClB;QACN,uEAAuE;QACvE,0DAA0D;QAC1D,WAAW,UAAU,SAAS,IAAI,UAAU,cAAc,GACpD,UAAU,SAAS,GACnB;QACN,2EAA2E;QAC3E,+BAA+B;QAC/B,gBAAgB,UAAU,SAAS,IAAI,UAAU,QAAQ,IAAI,UAAU,cAAc,GAC/E,UAAU,cAAc,GACxB;QACN,UAAU,UAAU,QAAQ,KAAK,KAAK,UAAU,QAAQ,GAAG;QAC3D,YAAY,UAAU,UAAU,KAAK,KAAK,UAAU,UAAU,GAAG;QACjE,MAAM,UAAU,IAAI,KAAK,KAAK,UAAU,IAAI,GAAG;QAC/C,QAAQ,UAAU,MAAM,KAAK,KAAK,UAAU,MAAM,GAAG;QACrD,SAAS,UAAU,OAAO,KAAK,KAAK,UAAU,OAAO,GAAG;QACxD,aAAa,UAAU,WAAW,KAAK,KAAK,UAAU,WAAW,GAAG;QACpE,WAAW,UAAU,SAAS,KAAK,KAAK,UAAU,SAAS,GAAG;QAC9D,eAAe,UAAU,aAAa,KAAK,KAAK,UAAU,aAAa,GAAG;QAC1E,KAAK,UAAU,GAAG,KAAK,KAAK,UAAU,GAAG,GAAG;QAC5C,SAAS,UAAU,OAAO,KAAK,KAAK,UAAU,OAAO,GAAG;QACxD,WAAW,UAAU,SAAS,KAAK,KAAK,UAAU,SAAS,GAAG;QAC9D,SAAS,UAAU,OAAO,KAAK,KAAK,UAAU,OAAO,GAAG;QACxD,YAAY,UAAU,UAAU,KAAK,KAAK,UAAU,UAAU,GAAG;QACjE,SAAS,UAAU,OAAO,KAAK,KAAK,UAAU,OAAO,GAAG;QACxD,WAAW,UAAU,SAAS;QAC9B,OAAO,UAAU,KAAK;QACtB,SAAS,UAAU,OAAO;QAC1B,OAAO,UAAU,KAAK;QACtB,SAAS,UAAU,OAAO;IAC9B;AACJ;AACA,SAAS,2BAA2B,QAAQ;IACxC,IAAI,OAAO,aAAa,aAAa;QACjC,OAAO,IAAI,2OAAA,CAAA,sBAAmB,CAAC;YAC3B,QAAQ,IAAI,2OAAA,CAAA,oBAAiB,CAAC;YAC9B,KAAK;YACL,SAAS,EAAE;YACX,IAAI,IAAI,2OAAA,CAAA,kBAAe;QAC3B;IACJ;IACA,MAAM,UAAU,MAAM,OAAO,CAAC,SAAS,WAAW,IAC5C,SAAS,WAAW,CAAC,GAAG,CAAC,gCACzB,EAAE;IACR,OAAQ,SAAS,UAAU;QACvB,KAAK,8PAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO,IAAI,2OAAA,CAAA,sBAAmB,CAAC;gBAC3B,IAAI,SAAS,EAAE;gBACf,KAAK,SAAS,GAAG;gBACjB,QAAQ,yBAAyB,SAAS,MAAM;gBAChD;gBACA,IAAI,4BAA4B,SAAS,SAAS;YACtD;QACJ,KAAK,8PAAA,CAAA,aAAU,CAAC,IAAI;YAChB,OAAO,IAAI,2OAAA,CAAA,qBAAkB,CAAC;gBAC1B,IAAI,SAAS,EAAE;gBACf,KAAK,SAAS,GAAG;gBACjB,QAAQ,yBAAyB,SAAS,MAAM;gBAChD;gBACA,IAAI,4BAA4B,SAAS,SAAS;YACtD;QACJ,KAAK,8PAAA,CAAA,aAAU,CAAC,SAAS;YACrB,OAAO,IAAI,2OAAA,CAAA,0BAAuB,CAAC;gBAC/B,IAAI,SAAS,EAAE;gBACf,KAAK,SAAS,GAAG;gBACjB,QAAQ,yBAAyB,SAAS,MAAM;gBAChD;gBACA,IAAI,4BAA4B,SAAS,SAAS;YACtD;QACJ,KAAK,8PAAA,CAAA,aAAU,CAAC,KAAK;YACjB,OAAO,IAAI,2OAAA,CAAA,sBAAmB,CAAC;gBAC3B,IAAI,SAAS,EAAE;gBACf,KAAK,SAAS,GAAG;gBACjB,QAAQ,IAAI,2OAAA,CAAA,oBAAiB,CAAC,SAAS,MAAM;gBAC7C;gBACA,IAAI,4BAA4B,SAAS,SAAS;YACtD;QACJ,KAAK,8PAAA,CAAA,aAAU,CAAC,WAAW;YACvB,OAAO,IAAI,2OAAA,CAAA,sBAAmB,CAAC;gBAC3B,IAAI,SAAS,EAAE;gBACf,KAAK,SAAS,GAAG;gBACjB,QAAQ,IAAI,2OAAA,CAAA,oBAAiB,CAAC;gBAC9B;gBACA,IAAI,4BAA4B,SAAS,SAAS;YACtD;QACJ;YAAS;gBACL,SAAS,UAAU;gBACnB,OAAO,IAAI,2OAAA,CAAA,sBAAmB,CAAC;oBAC3B,KAAK;oBACL,QAAQ,IAAI,2OAAA,CAAA,oBAAiB,CAAC;oBAC9B,SAAS,EAAE;oBACX,IAAI,4BAA4B,SAAS,SAAS;gBACtD;YACJ;IACJ;AACJ;AACA,SAAS,gBAAgB,IAAI;IACzB,OAAO,KAAK,IAAI,KAAK;AACzB;AACA,SAAS,kBAAkB,IAAI;IAC3B,OAAO,gBAAgB,SAAS,KAAK,SAAS,KAAK;AACvD;AACA,SAAS,kBAAkB,IAAI;IAC3B,OAAO,gBAAgB,SAAS,KAAK,SAAS,KAAK;AACvD;AACA,SAAS,oBAAoB,IAAI;IAC7B,OAAO,gBAAgB,SAAS,KAAK,SAAS,KAAK;AACvD;AACA,SAAS,UAAU,IAAI;IACnB,OAAO,KAAK,IAAI,KAAK;AACzB;AACA,SAAS,YAAY,IAAI;IACrB,OAAO,KAAK,IAAI,KAAK;AACzB;AACA,SAAS,aAAa,IAAI;IACtB,OAAO,KAAK,IAAI,KAAK;AACzB;AACA,SAAS,oBAAoB,IAAI;IAC7B,OAAO,KAAK,IAAI,KAAK;AACzB;AACA,SAAS,qBAAqB,IAAI;IAC9B,IAAI,kBAAkB,OAAO;QACzB,OAAO,IAAI,8PAAA,CAAA,OAAI,CAAC;YACZ,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH,SAAS,KAAK,OAAO;oBACrB,MAAM,qBAAqB,KAAK,IAAI;oBACpC,iBAAiB,KAAK,eAAe;oBACrC,WAAW,8PAAA,CAAA,qBAAkB,CAAC,YAAY;oBAC1C,YAAY,KAAK,UAAU;oBAC3B,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;gBAC3B;YACJ;QACJ;IACJ;IACA,IAAI,kBAAkB,OAAO;QACzB,OAAO,IAAI,8PAAA,CAAA,OAAI,CAAC;YACZ,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH,SAAS,KAAK,OAAO;oBACrB,MAAM,qBAAqB,KAAK,IAAI;oBACpC,iBAAiB,KAAK,eAAe;oBACrC,WAAW,8PAAA,CAAA,qBAAkB,CAAC,YAAY;oBAC1C,KAAK,KAAK,GAAG;oBACb,iBAAiB,KAAK,MAAM;gBAChC;YACJ;QACJ;IACJ;IACA,IAAI,oBAAoB,OAAO;QAC3B,OAAO,IAAI,8PAAA,CAAA,OAAI,CAAC;YACZ,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH,SAAS,KAAK,OAAO;oBACrB,MAAM,qBAAqB,KAAK,IAAI;oBACpC,iBAAiB,KAAK,eAAe;oBACrC,WAAW,8PAAA,CAAA,qBAAkB,CAAC,cAAc;oBAC5C,KAAK,KAAK,GAAG;oBACb,UAAU,KAAK,QAAQ;gBAC3B;YACJ;QACJ;IACJ;IACA,IAAI,YAAY,OAAO;QACnB,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,KAAK,IAChC,KAAK,KAAK,CAAC,GAAG,CAAC,6BACf,EAAE;QACR,MAAM,OAAO,MAAM,OAAO,CAAC,KAAK,IAAI,IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,6BACd,EAAE;QACR,OAAO,IAAI,8PAAA,CAAA,OAAI,CAAC;YACZ,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH,SAAS,KAAK,OAAO;oBACrB,MAAM,qBAAqB,KAAK,IAAI;oBACpC;oBACA;oBACA,uBAAuB,KAAK,qBAAqB;oBACjD,oBAAoB,KAAK,kBAAkB;gBAC/C;YACJ;QACJ;IACJ;IACA,IAAI,UAAU,OAAO;QACjB,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,EAAE;QACzD,MAAM,OAAO,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE;QACtD,OAAO,IAAI,8PAAA,CAAA,OAAI,CAAC;YACZ,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH,SAAS,KAAK,OAAO;oBACrB,MAAM,qBAAqB,KAAK,IAAI;oBACpC;oBACA;gBACJ;YACJ;QACJ;IACJ;IACA,IAAI,aAAa,OAAO;QACpB,OAAO,IAAI,8PAAA,CAAA,OAAI,CAAC;YACZ,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH,SAAS,KAAK,OAAO;oBACrB,MAAM,qBAAqB,KAAK,IAAI;oBACpC,WAAW;gBACf;YACJ;QACJ;IACJ;IACA,IAAI,oBAAoB,OAAO;QAC3B,OAAO,IAAI,8PAAA,CAAA,OAAI,CAAC;YACZ,MAAM;gBACF,MAAM;gBACN,OAAO;oBACH,SAAS,KAAK,OAAO;oBACrB,MAAM,qBAAqB,KAAK,IAAI;oBACpC,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;gBACnB;YACJ;QACJ;IACJ;IACA,OAAO,IAAI,8PAAA,CAAA,OAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40arcjet%2Bprotocol%401.0.0-beta.7/node_modules/%40arcjet/protocol/proto/decide/v1alpha1/decide_connect.js"], "sourcesContent": ["// @generated by protoc-gen-connect-es v1.6.1\n// @generated from file proto/decide/v1alpha1/decide.proto (package proto.decide.v1alpha1, syntax proto3)\n/* eslint-disable */\n// @ts-nocheck\n\nimport { DecideRequest, DecideResponse, ReportRequest, ReportResponse } from \"./decide_pb.js\";\nimport { MethodKind } from \"@bufbuild/protobuf\";\n\n/**\n * @generated from service proto.decide.v1alpha1.DecideService\n */\nexport const DecideService = {\n  typeName: \"proto.decide.v1alpha1.DecideService\",\n  methods: {\n    /**\n     * @generated from rpc proto.decide.v1alpha1.DecideService.Decide\n     */\n    decide: {\n      name: \"Decide\",\n      I: DecideRequest,\n      O: DecideResponse,\n      kind: MethodKind.Unary,\n    },\n    /**\n     * @generated from rpc proto.decide.v1alpha1.DecideService.Report\n     */\n    report: {\n      name: \"Report\",\n      I: ReportRequest,\n      O: ReportResponse,\n      kind: MethodKind.Unary,\n    },\n  }\n};\n\n"], "names": [], "mappings": "AAAA,6CAA6C;AAC7C,yGAAyG;AACzG,kBAAkB,GAClB,cAAc;;;;AAEd;AACA;;;AAKO,MAAM,gBAAgB;IAC3B,UAAU;IACV,SAAS;QACP;;KAEC,GACD,QAAQ;YACN,MAAM;YACN,GAAG,8PAAA,CAAA,gBAAa;YAChB,GAAG,8PAAA,CAAA,iBAAc;YACjB,MAAM,4OAAA,CAAA,aAAU,CAAC,KAAK;QACxB;QACA;;KAEC,GACD,QAAQ;YACN,MAAM;YACN,GAAG,8PAAA,CAAA,gBAAa;YAChB,GAAG,8PAAA,CAAA,iBAAc;YACjB,MAAM,4OAAA,CAAA,aAAU,CAAC,KAAK;QACxB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/%40arcjet%2Bprotocol%401.0.0-beta.7/node_modules/%40arcjet/protocol/client.js"], "sourcesContent": ["import { createPromiseClient } from '@connectrpc/connect';\nimport { ArcjetStackToProtocol, ArcjetDecisionToProtocol, ArcjetRuleToProtocol, ArcjetDecisionFromProtocol } from './convert.js';\nimport 'typeid-js';\nimport { ReportRequest, DecideRequest } from './proto/decide/v1alpha1/decide_pb.js';\nimport { DecideService } from './proto/decide/v1alpha1/decide_connect.js';\n\n// TODO: Dedupe with `errorMessage` in core\nfunction errorMessage(err) {\n    if (err) {\n        if (typeof err === \"string\") {\n            return err;\n        }\n        if (typeof err === \"object\" &&\n            \"message\" in err &&\n            typeof err.message === \"string\") {\n            return err.message;\n        }\n    }\n    return \"Unknown problem\";\n}\nfunction createClient(options) {\n    const { transport, sdkVersion, baseUrl, timeout } = options;\n    const sdkStack = ArcjetStackToProtocol(options.sdkStack);\n    const client = createPromiseClient(DecideService, transport);\n    return Object.freeze({\n        async decide(context, details, rules) {\n            const { log } = context;\n            let hasValidateEmail = false;\n            const protoRules = [];\n            for (const rule of rules) {\n                if (rule.type === \"EMAIL\") {\n                    hasValidateEmail = true;\n                }\n                protoRules.push(ArcjetRuleToProtocol(rule));\n            }\n            // Build the request object from the Protobuf generated class.\n            const decideRequest = new DecideRequest({\n                sdkStack,\n                sdkVersion,\n                characteristics: context.characteristics,\n                details: {\n                    ip: details.ip,\n                    method: details.method,\n                    protocol: details.protocol,\n                    host: details.host,\n                    path: details.path,\n                    headers: Object.fromEntries(details.headers.entries()),\n                    cookies: details.cookies,\n                    query: details.query,\n                    // TODO(#208): Re-add body\n                    // body: details.body,\n                    extra: details.extra,\n                    email: typeof details.email === \"string\" ? details.email : undefined,\n                },\n                rules: protoRules,\n            });\n            log.debug(\"Decide request to %s\", baseUrl);\n            const response = await client.decide(decideRequest, {\n                headers: { Authorization: `Bearer ${context.key}` },\n                // If an email rule is configured, we double the timeout.\n                // See https://github.com/arcjet/arcjet-js/issues/1697\n                timeoutMs: hasValidateEmail ? timeout * 2 : timeout,\n            });\n            const decision = ArcjetDecisionFromProtocol(response.decision);\n            log.debug({\n                id: decision.id,\n                fingerprint: context.fingerprint,\n                path: details.path,\n                runtime: context.runtime,\n                ttl: decision.ttl,\n                conclusion: decision.conclusion,\n                reason: decision.reason,\n                ruleResults: decision.results,\n            }, \"Decide response\");\n            return decision;\n        },\n        report(context, details, decision, rules) {\n            const { log } = context;\n            // Build the request object from the Protobuf generated class.\n            const reportRequest = new ReportRequest({\n                sdkStack,\n                sdkVersion,\n                characteristics: context.characteristics,\n                details: {\n                    ip: details.ip,\n                    method: details.method,\n                    protocol: details.protocol,\n                    host: details.host,\n                    path: details.path,\n                    headers: Object.fromEntries(details.headers.entries()),\n                    cookies: details.cookies,\n                    query: details.query,\n                    // TODO(#208): Re-add body\n                    // body: details.body,\n                    extra: details.extra,\n                    email: typeof details.email === \"string\" ? details.email : undefined,\n                },\n                decision: ArcjetDecisionToProtocol(decision),\n                rules: rules.map(ArcjetRuleToProtocol),\n            });\n            log.debug(\"Report request to %s\", baseUrl);\n            // We use the promise API directly to avoid returning a promise from this\n            // function so execution can't be paused with `await`\n            const reportPromise = client\n                .report(reportRequest, {\n                headers: { Authorization: `Bearer ${context.key}` },\n                // Rules don't execute during `Report` so we don't adjust the timeout\n                // if an email rule is configured.\n                timeoutMs: 2_000, // 2 seconds\n            })\n                .then((response) => {\n                log.debug({\n                    id: decision.id,\n                    fingerprint: context.fingerprint,\n                    path: details.path,\n                    runtime: context.runtime,\n                    ttl: decision.ttl,\n                }, \"Report response\");\n            })\n                .catch((err) => {\n                log.info(\"Encountered problem sending report: %s\", errorMessage(err));\n            });\n            if (typeof context.waitUntil === \"function\") {\n                context.waitUntil(reportPromise);\n            }\n        },\n    });\n}\n\nexport { createClient };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;;;;;;AAEA,2CAA2C;AAC3C,SAAS,aAAa,GAAG;IACrB,IAAI,KAAK;QACL,IAAI,OAAO,QAAQ,UAAU;YACzB,OAAO;QACX;QACA,IAAI,OAAO,QAAQ,YACf,aAAa,OACb,OAAO,IAAI,OAAO,KAAK,UAAU;YACjC,OAAO,IAAI,OAAO;QACtB;IACJ;IACA,OAAO;AACX;AACA,SAAS,aAAa,OAAO;IACzB,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IACpD,MAAM,WAAW,CAAA,GAAA,6NAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,QAAQ;IACvD,MAAM,SAAS,CAAA,GAAA,wRAAA,CAAA,sBAAmB,AAAD,EAAE,mQAAA,CAAA,gBAAa,EAAE;IAClD,OAAO,OAAO,MAAM,CAAC;QACjB,MAAM,QAAO,OAAO,EAAE,OAAO,EAAE,KAAK;YAChC,MAAM,EAAE,GAAG,EAAE,GAAG;YAChB,IAAI,mBAAmB;YACvB,MAAM,aAAa,EAAE;YACrB,KAAK,MAAM,QAAQ,MAAO;gBACtB,IAAI,KAAK,IAAI,KAAK,SAAS;oBACvB,mBAAmB;gBACvB;gBACA,WAAW,IAAI,CAAC,CAAA,GAAA,6NAAA,CAAA,uBAAoB,AAAD,EAAE;YACzC;YACA,8DAA8D;YAC9D,MAAM,gBAAgB,IAAI,8PAAA,CAAA,gBAAa,CAAC;gBACpC;gBACA;gBACA,iBAAiB,QAAQ,eAAe;gBACxC,SAAS;oBACL,IAAI,QAAQ,EAAE;oBACd,QAAQ,QAAQ,MAAM;oBACtB,UAAU,QAAQ,QAAQ;oBAC1B,MAAM,QAAQ,IAAI;oBAClB,MAAM,QAAQ,IAAI;oBAClB,SAAS,OAAO,WAAW,CAAC,QAAQ,OAAO,CAAC,OAAO;oBACnD,SAAS,QAAQ,OAAO;oBACxB,OAAO,QAAQ,KAAK;oBACpB,0BAA0B;oBAC1B,sBAAsB;oBACtB,OAAO,QAAQ,KAAK;oBACpB,OAAO,OAAO,QAAQ,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG;gBAC/D;gBACA,OAAO;YACX;YACA,IAAI,KAAK,CAAC,wBAAwB;YAClC,MAAM,WAAW,MAAM,OAAO,MAAM,CAAC,eAAe;gBAChD,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE;gBAAC;gBAClD,yDAAyD;gBACzD,sDAAsD;gBACtD,WAAW,mBAAmB,UAAU,IAAI;YAChD;YACA,MAAM,WAAW,CAAA,GAAA,6NAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,QAAQ;YAC7D,IAAI,KAAK,CAAC;gBACN,IAAI,SAAS,EAAE;gBACf,aAAa,QAAQ,WAAW;gBAChC,MAAM,QAAQ,IAAI;gBAClB,SAAS,QAAQ,OAAO;gBACxB,KAAK,SAAS,GAAG;gBACjB,YAAY,SAAS,UAAU;gBAC/B,QAAQ,SAAS,MAAM;gBACvB,aAAa,SAAS,OAAO;YACjC,GAAG;YACH,OAAO;QACX;QACA,QAAO,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;YACpC,MAAM,EAAE,GAAG,EAAE,GAAG;YAChB,8DAA8D;YAC9D,MAAM,gBAAgB,IAAI,8PAAA,CAAA,gBAAa,CAAC;gBACpC;gBACA;gBACA,iBAAiB,QAAQ,eAAe;gBACxC,SAAS;oBACL,IAAI,QAAQ,EAAE;oBACd,QAAQ,QAAQ,MAAM;oBACtB,UAAU,QAAQ,QAAQ;oBAC1B,MAAM,QAAQ,IAAI;oBAClB,MAAM,QAAQ,IAAI;oBAClB,SAAS,OAAO,WAAW,CAAC,QAAQ,OAAO,CAAC,OAAO;oBACnD,SAAS,QAAQ,OAAO;oBACxB,OAAO,QAAQ,KAAK;oBACpB,0BAA0B;oBAC1B,sBAAsB;oBACtB,OAAO,QAAQ,KAAK;oBACpB,OAAO,OAAO,QAAQ,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG;gBAC/D;gBACA,UAAU,CAAA,GAAA,6NAAA,CAAA,2BAAwB,AAAD,EAAE;gBACnC,OAAO,MAAM,GAAG,CAAC,6NAAA,CAAA,uBAAoB;YACzC;YACA,IAAI,KAAK,CAAC,wBAAwB;YAClC,yEAAyE;YACzE,qDAAqD;YACrD,MAAM,gBAAgB,OACjB,MAAM,CAAC,eAAe;gBACvB,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE;gBAAC;gBAClD,qEAAqE;gBACrE,kCAAkC;gBAClC,WAAW;YACf,GACK,IAAI,CAAC,CAAC;gBACP,IAAI,KAAK,CAAC;oBACN,IAAI,SAAS,EAAE;oBACf,aAAa,QAAQ,WAAW;oBAChC,MAAM,QAAQ,IAAI;oBAClB,SAAS,QAAQ,OAAO;oBACxB,KAAK,SAAS,GAAG;gBACrB,GAAG;YACP,GACK,KAAK,CAAC,CAAC;gBACR,IAAI,IAAI,CAAC,0CAA0C,aAAa;YACpE;YACA,IAAI,OAAO,QAAQ,SAAS,KAAK,YAAY;gBACzC,QAAQ,SAAS,CAAC;YACtB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}]}