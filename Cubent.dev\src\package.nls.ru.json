{"extension.displayName": "Roo Code (ранее Roo Cline)", "extension.description": "Целая команда ИИ-разработчиков в вашем редакторе.", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.activitybar.title": "Roo Code", "views.sidebar.name": "Roo Code", "command.newTask.title": "Новая задача", "command.mcpServers.title": "MCP серверы", "command.prompts.title": "Режимы", "command.history.title": "История", "command.openInEditor.title": "Открыть в редакторе", "command.settings.title": "Настройки", "command.documentation.title": "Документация", "command.openInNewTab.title": "Открыть в новой вкладке", "command.explainCode.title": "Объяснить код", "command.fixCode.title": "Исправить код", "command.improveCode.title": "Улучшить код", "command.addToContext.title": "Добавить в контекст", "command.focusInput.title": "Фокус на поле ввода", "command.setCustomStoragePath.title": "Указать путь хранения", "command.terminal.addToContext.title": "Добавить содержимое терминала в контекст", "command.terminal.fixCommand.title": "Исправить эту команду", "command.terminal.explainCommand.title": "Объяснить эту команду", "command.acceptInput.title": "Принять ввод/предложение", "configuration.title": "qapt coder", "commands.allowedCommands.description": "Команды, которые могут быть автоматически выполнены, когда включена опция 'Всегда подтверждать операции выполнения'", "settings.vsCodeLmModelSelector.description": "Настройки для VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "Поставщик языковой модели (например, copilot)", "settings.vsCodeLmModelSelector.family.description": "Семейство языковой модели (например, gpt-4)", "settings.customStoragePath.description": "Пользовательский путь хранения. Оставьте пустым для использования пути по умолчанию. Поддерживает абсолютные пути (например, 'D:\\qaptCoderStorage')", "settings.qaptCoderCloudEnabled.description": "Включить qapt coder Cloud."}