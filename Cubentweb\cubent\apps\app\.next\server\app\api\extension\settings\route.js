(()=>{var e={};e.id=1023,e.ids=[1023],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26142:(e,r,t)=>{"use strict";e.exports=t(44870)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86262:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>q,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>l});var n=t(26142),i=t(94327),o=t(34862),u=t(37838),a=t(18815),p=t(26239),c=t(25);let x=c.z.object({extensionSettings:c.z.record(c.z.any()).optional(),preferences:c.z.record(c.z.any()).optional()});async function d(){try{let{userId:e}=await (0,u.j)();if(!e)return p.NextResponse.json({error:"Unauthorized"},{status:401});let r=await a.database.user.findUnique({where:{clerkId:e},select:{extensionSettings:!0,preferences:!0}});if(!r)return p.NextResponse.json({error:"User not found"},{status:404});return p.NextResponse.json({extensionSettings:r.extensionSettings||{},preferences:r.preferences||{}})}catch(e){return console.error("Settings fetch error:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let{userId:r}=await (0,u.j)();if(!r)return p.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),{extensionSettings:s,preferences:n}=x.parse(t),i=await a.database.user.findUnique({where:{clerkId:r}});if(!i)return p.NextResponse.json({error:"User not found"},{status:404});let o={};void 0!==s&&(o.extensionSettings=s),void 0!==n&&(o.preferences=n);let c=await a.database.user.update({where:{id:i.id},data:o,select:{extensionSettings:!0,preferences:!0}});return p.NextResponse.json({success:!0,extensionSettings:c.extensionSettings,preferences:c.preferences})}catch(e){return console.error("Settings update error:",e),p.NextResponse.json({error:"Internal server error"},{status:500})}}let q=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/extension/settings/route",pathname:"/api/extension/settings",filename:"route",bundlePath:"app/api/extension/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\api\\extension\\settings\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:h}=q;function j(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},89259:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,6239,2923,25,903,7838,5480,864],()=>t(86262));module.exports=s})();