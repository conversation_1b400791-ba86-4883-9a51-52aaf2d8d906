{"name": "@qapt-coder/vscode-nightly", "description": "Nightly build for the qapt coder VSCode extension.", "private": true, "packageManager": "pnpm@10.8.1", "scripts": {"bundle:nightly": "node esbuild.mjs", "vsix:nightly": "cd build && mkdirp ../../../bin && npx vsce package --no-dependencies --out ../../../bin", "clean": "rimraf build .turbo"}, "devDependencies": {"@qapt-coder/build": "workspace:^"}}